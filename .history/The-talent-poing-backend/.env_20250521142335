APP_NAME="The Talent Point"
APP_ENV=prod
APP_KEY=base64:BguoZSb1cSR+hTWhH0bVvZ2yChINiy4uacNJFbM05iY=
APP_DEBUG=false
APP_URL=https://api.thetalentpoint.com

TELESCOPE_PATH=werq234234-logs

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=thetalentpoint_api
DB_USERNAME=thetalentpoint_api
DB_PASSWORD=D=6e4hl]1vd(

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
#QUEUE_CONNECTION=sync
QUEUE_CONNECTION=queue_jobs
QUEUE_FAILED_DRIVER=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

#MAIL_MAILER=smtp
#MAIL_HOST=smtp.gmail.com
#MAIL_PORT=587
#MAIL_USERNAME="<EMAIL>"
#MAIL_PASSWORD="%u*5>8%<amAX=B8PyTY3hL>%^Gh"
#MAIL_ENCRYPTION=tls
#MAIL_FROM_ADDRESS="<EMAIL>"
#MAIL_FROM_NAME="The Talent Point Team"


MAIL_MAILER=smtp
MAIL_HOST=in-v3.mailjet.com
MAIL_PORT=587
MAIL_USERNAME=********************************
MAIL_PASSWORD=4726180f864b98c92fe0e44a9b81b244
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"


AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

NEWSLETTER_DRIVER=Spatie\Newsletter\Drivers\MailChimpDriver
NEWSLETTER_API_KEY=
NEWSLETTER_LIST_ID=

STRIPE_KEY=pk_test_FQu4ActGupRmMrkmBpwU26js
STRIPE_SECRET=sk_test_8yTMfGjWta7zVzyhB6S3N2ws

JWT_SECRET=TtsnU9NyxnwcyNBBH0mdlYzuRPhtEMbofveH1dD0wGn5dnMMdazNxEjGR6syBuI8