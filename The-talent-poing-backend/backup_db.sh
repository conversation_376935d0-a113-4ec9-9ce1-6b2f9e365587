#!/bin/bash

# Server details
SERVER="65.108.252.17"
USER="thetalentpoint"
DB_USER="thetalentpoint_api"
DB_PASS="D=6e4hl]1vd("
DB_NAME="thetalentpoint_api"
LOCAL_PATH="./db_backup"

# Create backup directory if it doesn't exist
mkdir -p $LOCAL_PATH

# Current date for filename
DATE=$(date +%Y%m%d_%H%M%S)
FILENAME="thetalentpoint_backup_$DATE.sql"

# Connect to server, create dump, and download
echo "Connecting to server and creating database dump..."
ssh $USER@$SERVER "mysqldump -u$DB_USER -p'$DB_PASS' $DB_NAME > ~/$FILENAME && echo 'Dump created successfully.'"

echo "Downloading dump file to local machine..."
scp $USER@$SERVER:~/$FILENAME $LOCAL_PATH/

echo "Cleaning up remote file..."
ssh $USER@$SERVER "rm ~/$FILENAME"

echo "Backup completed: $LOCAL_PATH/$FILENAME"