<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\SavedJobs
 *
 * @property int $id
 * @property int|null $user_id
 * @property int|null $company_id
 * @property int|null $job_id
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|SavedJobs newModelQuery()
 * @method static Builder|SavedJobs newQuery()
 * @method static Builder|SavedJobs query()
 * @method static Builder|SavedJobs whereCompanyId($value)
 * @method static Builder|SavedJobs whereCreatedAt($value)
 * @method static Builder|SavedJobs whereId($value)
 * @method static Builder|SavedJobs whereJobId($value)
 * @method static Builder|SavedJobs whereStatus($value)
 * @method static Builder|SavedJobs whereUpdatedAt($value)
 * @method static Builder|SavedJobs whereUserId($value)
 * @mixin Eloquent
 * @mixin Builder
 * @mixin \Eloquent
 */
class SavedJobs extends Model
{
    protected $table = 'saved_jobs';
    use HasFactory;
}
