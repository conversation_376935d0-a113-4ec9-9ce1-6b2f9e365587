<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExpandJobSearch extends Model
{
    use HasFactory;
    protected $table = 'expand_job_search'; 
    protected $fillable = [
        'blog_id',
        'keyword',
        'country',
        'title'
    ];
    public function blog()
    {
        return $this->belongsTo(Blog::class);
    }
}
