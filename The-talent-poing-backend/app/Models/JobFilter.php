<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\JobFilter
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $section_name
 * @property string|null $job_title
 * @property string|null $country_id
 * @property string|null $currency
 * @property string|null $salary
 * @property string|null $experience
 * @property string|null $skills
 * @property string|null $sector
 * @property string|null $job_type
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter query()
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereExperience($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereJobType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereSalary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereSectionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereSector($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereSkills($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobFilter whereUserId($value)
 * @mixin \Eloquent
 */
class JobFilter extends Model
{
    use HasFactory;
    protected $table = 'job_filters';

     protected $fillable = [
        'user_id',
        'section_name',
        'job_title',
        'country_id',
        'currency',
        'salary',
        'experience',
        'skills',
        'sector',
        'job_type',
        'status'
    ];
}
