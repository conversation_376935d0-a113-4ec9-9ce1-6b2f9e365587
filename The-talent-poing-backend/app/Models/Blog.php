<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\Blog
 *
 * @property int $id
 * @property int|null $blog_category_id
 * @property int|null $created_by_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $author_name
 * @property string|null $author_image
 * @property string|null $tag
 * @property string|null $description
 * @property string|null $image
 * @property string|null $meta_tag
 * @property string|null $meta_desc
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|Blog newModelQuery()
 * @method static Builder|Blog newQuery()
 * @method static Builder|Blog query()
 * @method static Builder|Blog whereAuthorImage($value)
 * @method static Builder|Blog whereAuthorName($value)
 * @method static Builder|Blog whereBlogCategoryId($value)
 * @method static Builder|Blog whereCreatedAt($value)
 * @method static Builder|Blog whereCreatedById($value)
 * @method static Builder|Blog whereDescription($value)
 * @method static Builder|Blog whereId($value)
 * @method static Builder|Blog whereImage($value)
 * @method static Builder|Blog whereMetaDesc($value)
 * @method static Builder|Blog whereMetaTag($value)
 * @method static Builder|Blog whereName($value)
 * @method static Builder|Blog whereSlug($value)
 * @method static Builder|Blog whereStatus($value)
 * @method static Builder|Blog whereTag($value)
 * @method static Builder|Blog whereUpdatedAt($value)
 * @mixin Builder
 * @mixin Eloquent
 */
class Blog extends Model
{
    use HasFactory;
    protected $table = 'blogs'; // Ensure this matches your table name
    protected $primaryKey = 'id'; // Ensure this matches your primary key column
    public $incrementing = true; // Ensure that your primary key is auto-incrementing
    protected $keyType = 'int';
    protected $fillable = [
        'name',
        'slug'
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(BlogCategory::class, 'blog_category_id', 'id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($blog) {
            $originalSlug = $slug = \Str::slug($blog->slug);

            $count = 1;
            while (static::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $count;
                $count++;
            }

            $blog->slug = $slug;
        });
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by-id', 'id');
    }

    public function author()
    {
        return $this->belongsTo(Author::class);
    }

    public function articles()
    {
        return $this->hasMany(BlogArticle::class);
    }

    public function jobSearches()
    {
        return $this->hasMany(ExpandJobSearch::class);
    }

    public function blogCta()
    {
        return $this->hasMany(BlogCta::class);
    }
}
