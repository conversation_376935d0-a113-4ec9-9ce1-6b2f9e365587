<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Author extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($author) {
            $slug = Str::slug($author->name);

            $originalSlug = $slug;
            $count = 1;

            while (static::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $count;
                $count++;
            }

            $author->slug = $slug;
        });
    }
    protected $fillable = [
        'name',
        'designation',
        'description',
        'profile_image',
        'linkedin',
        'gender'
    ];

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function blogs()
    {
        return $this->hasMany(Blog::class);
    }

}
