<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\WorkExperience
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $title
 * @property string|null $company
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $currently_work_here
 * @property string|null $description
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereCurrentlyWorkHere($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkExperience whereUserId($value)
 * @mixin \Eloquent
 */
class WorkExperience extends Model
{
    protected $table = 'work_experience';
    use HasFactory;

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
