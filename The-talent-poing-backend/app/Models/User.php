<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Database\Factories\UserFactory;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Src\FileManagement\Domain\File;

//use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;

/**
 * App\Models\User
 *
 * @property int $id
 * @property string|null $name
 * @property string $email
 * @property int $isShowEmail
 * @property string|null $password
 * @property string|null $view_password
 * @property string|null $role
 * @property int|null $company_id
 * @property int|null $available_resume_count
 * @property string|null $profile_image
 * @property string|null $where_job_search
 * @property string|null $job_type
 * @property string $job_status
 * @property string|null $where_currently_based
 * @property string|null $current_position
 * @property int|null $profile_complete_percentage
 * @property int $unlock_instant_apply
 * @property string|null $linked_id
 * @property string|null $google_id
 * @property int $showcontact_no
 * @property string|null $date_of_birth
 * @property string|null $gender
 * @property string|null $years_of_experience
 * @property string|null $current_salary
 * @property string|null $desired_salary
 * @property string|null $bio
 * @property string|null $currency
 * @property string|null $industry
 * @property string|null $sector
 * @property string|null $skills
 * @property string|null $facebook_link
 * @property string|null $twitter_link
 * @property string|null $instagram_link
 * @property string|null $website_url
 * @property string|null $linkedin_link
 * @property Carbon|null $email_verified_at
 * @property string|null $contact_no
 * @property int|null $login_count
 * @property int $first_login
 * @property int|null $created_by_id
 * @property string|null $slug
 * @property int $is2FA
 * @property string|null $otp
 * @property string|null $card_number
 * @property string|null $card_exp_month
 * @property string|null $card_exp_year
 * @property string|null $card_cvv
 * @property string|null $card_type
 * @property string $is_approved
 * @property string $status
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $nationality
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection<int, PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static UserFactory factory($count = null, $state = [])
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User query()
 * @method static Builder|User whereProfileImage($value)
 * @method static Builder|User whereAvailableResumeCount($value)
 * @method static Builder|User whereBio($value)
 * @method static Builder|User whereCardCvv($value)
 * @method static Builder|User whereCardExpMonth($value)
 * @method static Builder|User whereCardExpYear($value)
 * @method static Builder|User whereCardNumber($value)
 * @method static Builder|User whereCardType($value)
 * @method static Builder|User whereCompanyId($value)
 * @method static Builder|User whereContactNo($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereCreatedById($value)
 * @method static Builder|User whereCurrency($value)
 * @method static Builder|User whereCurrentPosition($value)
 * @method static Builder|User whereCurrentSalary($value)
 * @method static Builder|User whereDateOfBirth($value)
 * @method static Builder|User whereDesiredSalary($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereFacebookLink($value)
 * @method static Builder|User whereFirstLogin($value)
 * @method static Builder|User whereGender($value)
 * @method static Builder|User whereGoogleId($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereIndustry($value)
 * @method static Builder|User whereInstagramLink($value)
 * @method static Builder|User whereIs2FA($value)
 * @method static Builder|User whereIsApproved($value)
 * @method static Builder|User whereIsShowEmail($value)
 * @method static Builder|User whereJobStatus($value)
 * @method static Builder|User whereJobType($value)
 * @method static Builder|User whereLinkedId($value)
 * @method static Builder|User whereLinkedinLink($value)
 * @method static Builder|User whereLoginCount($value)
 * @method static Builder|User whereName($value)
 * @method static Builder|User whereNationality($value)
 * @method static Builder|User whereOtp($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User whereProfileCompletePercentage($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereRole($value)
 * @method static Builder|User whereSector($value)
 * @method static Builder|User whereShowcontactNo($value)
 * @method static Builder|User whereSkills($value)
 * @method static Builder|User whereSlug($value)
 * @method static Builder|User whereStatus($value)
 * @method static Builder|User whereTwitterLink($value)
 * @method static Builder|User whereUnlockInstantApply($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereViewPassword($value)
 * @method static Builder|User whereWebsiteUrl($value)
 * @method static Builder|User whereWhereCurrentlyBased($value)
 * @method static Builder|User whereWhereJobSearch($value)
 * @method static Builder|User whereYearsOfExperience($value)
 * @mixin Eloquent
 * @mixin Builder
 * @property-read Company|null $company
 * @property-read Membership|null $membership
 * @property-read Company|null $ownCompany
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    static string $ROLE_ADMIN = 'admin';
    static string $ROLE_EMPLOYER = 'employer';
    static string $ROLE_EMPLOYEE = 'employee';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'slug',
        'password',
        'view_password',
        'role',
        'company_id',
        'available_resume_count',
        'profile_image',
        'where_job_search',
        'job_type',
        'job_status',
        'where_currently_based',
        'cities',
        'countries',
        'where_job_search',
        'current_position',
        'profile_complete_percentage',
        'unlock_instant_apply',
        'linked_id',
        'google_id',
        'email_verified_at',
        'contact_no',
        'login_count',
        'first_login',
        'is_approved',
        'date_of_birth',
        'gender',
        'years_of_experience',
        'current_salary',
        'desired_salary',
        'bio',
        'background_banner_image',
        'otp',
        'status',
        'created_by_id',
        'is2FA'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function company(): HasOne
    {
        return $this->hasOne(Company::class)->with('logo', 'jobs')->latestOfMany(); // newly add latest of many part
    }

    public function ownCompany(): HasOne
    {
        return $this->hasOne(Company::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'where_currently_based', 'id');
    }

    public function claim(): HasOne
    {
        return $this->hasOne(ClaimCompany::class);
    }
    public function resumes(): HasMany
    {
        return $this
            ->hasMany(Resumes::class)
            ->where('status', 'active')
            ->where('default_resume', 1);
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(File::class, 'profile_image', 'uuid');
    }

    public function chats(): HasMany
    {
        return $this->hasMany(Messages::class, 'receiver_id', 'id');
    }

    public function languages(): HasMany
    {
        return $this->hasMany(Languages::class);
    }

    public function work_experience(): HasMany
    {
        return $this->hasMany(WorkExperience::class);
    }

    public function education(): HasMany
    {
        return $this->hasMany(Education::class);
    }

    public function portfolio(): HasMany
    {
        return $this->hasMany(Portfolio::class);
    }

    public function teamMembers(): HasMany
    {
        return $this->hasMany(User::class, 'created_by_id', 'id');
    }

    public function membership(): HasOne
    {
        return $this->hasOne(Membership::class, 'user_id', 'id');
    }

    public function application(): HasMany
    {
        return $this->hasMany(Applications::class);
    }

    public function jobsview(): HasMany
    {
        return $this->hasMany(JobsView::class);
    }

    public function savedjobs(): HasMany
    {
        return $this->hasMany(SavedJobs::class);
    }

    public function jobpost(): HasMany
    {
        return $this->hasMany(Job::class);
    }

    public function skills(): HasMany
    {
        return $this->hasMany(EmployeeSkills::class);
    }
}
