<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Applications
 *
 * @property int $id
 * @property int|null $user_id
 * @property int|null $job_id
 * @property int|null $company_id
 * @property int|null $jobpost_by_userId
 * @property string|null $resume_path
 * @property string|null $cover_letter
 * @property string|null $description
 * @property string $apply_status
 * @property string $hiring_status
 * @property int|null $instant_apply
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Applications newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Applications newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Applications query()
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereApplyStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereCoverLetter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereHiringStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereInstantApply($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereJobpostByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereResumePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereUserId($value)
 * @mixin \Eloquent
 */
class Applications extends Model
{
    use HasFactory;
}
