<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ResumesViewed
 *
 * @property int $id
 * @property int|null $user_id
 * @property int|null $company_id
 * @property int|null $applicant_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereApplicantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResumesViewed whereUserId($value)
 * @mixin \Eloquent
 */
class ResumesViewed extends Model
{

    protected $table = 'resumes_viewed';
    use HasFactory;
}
