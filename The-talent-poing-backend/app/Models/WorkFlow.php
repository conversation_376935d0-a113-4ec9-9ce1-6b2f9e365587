<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkFlow extends Model
{
    use HasFactory;

    // Specify which attributes are mass assignable
    protected $fillable = [
        'name',
        'instance',
        'parent_id',
        'condition_data_point',
        'condition_status',
        'contacts',
        'segment',
        'segment_condition',
        'segment_name',
        'segment_value',
        'frequency_value',
        'frequency_period',
        'frequency_time',
        'AM_PM',
        'start_date',
        'start_time',
        'email_sent',
        'status',
        'email_template_id'
    ];

    // Define relationships if applicable
    public function parent()
    {
        return $this->belongsTo(WorkFlow::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(WorkFlow::class, 'parent_id');
    }

    // Additional methods can be added here
    public function isActive()
    {
        return $this->status === 1; // Assuming 1 means active
    }

    public function conditions()
    {
        return $this->hasMany(WorkflowCondition::class, 'workflow_id');
    }

    public function segments()
    {
        return $this->hasMany(WorkflowSegment::class, 'workflow_id');
    }
}
