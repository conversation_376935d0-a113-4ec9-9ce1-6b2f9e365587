<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\JobsView
 *
 * @property int $id
 * @property int|null $job_id
 * @property int|null $user_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView query()
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobsView whereUserId($value)
 * @mixin \Eloquent
 */
class JobsView extends Model
{
    protected $table = 'jobs_view';
    use HasFactory;
}