<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogCta extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'blog_id',
        'title',
        'description',
        'link_name',
        'link_url'
    ];

    public function blog()
    {
        return $this->belongsTo(Blog::class);
    }
}
