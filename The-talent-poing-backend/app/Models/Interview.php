<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Interview
 *
 * @property int $id
 * @property int|null $job_id
 * @property int|null $company_id
 * @property int|null $applicant_id
 * @property string|null $meeting_link
 * @property string|null $interview_schedule_date
 * @property string|null $interview_schedule_from_time
 * @property string|null $interview_schedule_to_time
 * @property string|null $interview_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Interview newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Interview newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Interview query()
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereApplicantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereInterviewScheduleDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereInterviewScheduleFromTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereInterviewScheduleToTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereInterviewStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereMeetingLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Interview whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Interview extends Model
{
    use HasFactory;
}
