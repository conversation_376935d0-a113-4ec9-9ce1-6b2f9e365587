<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Payment
 *
 * @property int $id
 * @property int|null $user_id
 * @property int|null $company_id
 * @property int|null $plan_id
 * @property int|null $membership_id
 * @property string|null $customer_id
 * @property string|null $transaction_id
 * @property string|null $subscription_id
 * @property string|null $stripe_plan_id
 * @property string|null $stripe_plan_product_id
 * @property string|null $card_id
 * @property string|null $customer_email
 * @property string|null $subscription_status
 * @property string|null $current_period_start
 * @property string|null $current_period_end
 * @property string|null $card_holder_name
 * @property string|null $card_number
 * @property int|null $card_exp_month
 * @property int|null $card_exp_year
 * @property int|null $card_cvv
 * @property string|null $card_type
 * @property string|null $amount
 * @property string|null $currency
 * @property string|null $payment_gateway
 * @property string|null $plan_type
 * @property string|null $charge_id
 * @property string|null $charge_receipt_url
 * @property string|null $charge_created_date
 * @property string|null $expire_at
 * @property string|null $purchase_at
 * @property string|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Payment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardCvv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardExpMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardExpYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardHolderName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCardType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereChargeCreatedDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereChargeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereChargeReceiptUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCurrentPeriodEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCurrentPeriodStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCustomerEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereExpireAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereMembershipId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePaymentGateway($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePlanType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment wherePurchaseAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereStripePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereStripePlanProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereSubscriptionStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Payment whereUserId($value)
 * @mixin \Eloquent
 */
class Payment extends Model
{
    protected $table = 'payment';
    use HasFactory;
}