<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CompanyFollower
 *
 * @property int $id
 * @property int $user_id
 * @property int $company_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyFollower whereUserId($value)
 * @mixin \Eloquent
 */
class CompanyFollower extends Model
{
    protected $table = 'company_followers';
    use HasFactory;
}
