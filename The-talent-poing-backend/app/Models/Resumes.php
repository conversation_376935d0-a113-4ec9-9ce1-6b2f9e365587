<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resumes
 *
 * @property int $id
 * @property int $user_id
 * @property string $resume_pdf_path
 * @property int $default_resume
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes query()
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereDefaultResume($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereResumePdfPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resumes whereUserId($value)
 * @mixin \Eloquent
 */
class Resumes extends Model
{
    use HasFactory;
    protected $fillable = [
        'default_resume'
    ];

    protected $appends = ['url']; // Append the custom URL field
    // Define an accessor for the URL
    public function getUrlAttribute()
    {
        return url('/storage/images/employee/resume/' . $this->resume_pdf_path); // Customize this path as per your needs
    }
}
