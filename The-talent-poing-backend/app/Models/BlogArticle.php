<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogArticle extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'blog_id',
        'article_content_link',
        'article_content_desc'
    ];

    public function blog()
    {
        return $this->belongsTo(Blog::class);
    }
}
