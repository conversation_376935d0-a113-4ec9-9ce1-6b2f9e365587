<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\EmployeeSkills
 *
 * @property int $id
 * @property int $user_id
 * @property int $skill_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills query()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills whereSkillId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSkills whereUserId($value)
 * @mixin \Eloquent
 */
class EmployeeSkills extends Model
{
    use HasFactory;
}
