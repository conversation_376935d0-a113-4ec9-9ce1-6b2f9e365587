<?php

namespace App\Models;

use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AdminSettings
 *
 * @mixin Builder
 * @property int $id
 * @property int|null $user_id
 * @property string|null $logo
 * @property string|null $favicon
 * @property string|null $payment_gateway
 * @property string|null $payment_mode
 * @property string|null $stripe_test_secret_key
 * @property string|null $stripe_test_publish_key
 * @property string|null $stripe_live_secret_key
 * @property string|null $stripe_live_publish_key
 * @property string|null $homepage_meta_title
 * @property string|null $homepage_meta_description
 * @property string|null $jobs_meta_title
 * @property string|null $jobs_meta_description
 * @property string|null $carrer_meta_title
 * @property string|null $carrer_meta_description
 * @property string|null $about_meta_title
 * @property string|null $about_meta_description
 * @property string|null $employer_meta_title
 * @property string|null $employer_meta_description
 * @property string|null $employee_meta_title
 * @property string|null $employee_meta_description
 * @property string|null $pricing_meta_title
 * @property string|null $pricing_meta_description
 * @property string|null $blog_listing_meta_title
 * @property string|null $blog_listing_meta_description
 * @property string|null $linkedin_link
 * @property string|null $twitter_link
 * @property string|null $instagram_link
 * @property string|null $facebook_link
 * @property string|null $website_url
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereAboutMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereAboutMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereBlogListingMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereBlogListingMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereCarrerMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereCarrerMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereEmployeeMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereEmployeeMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereEmployerMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereEmployerMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereFacebookLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereFavicon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereHomepageMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereHomepageMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereInstagramLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereJobsMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereJobsMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereLinkedinLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereLogo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings wherePaymentGateway($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings wherePaymentMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings wherePricingMetaDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings wherePricingMetaTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereStripeLivePublishKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereStripeLiveSecretKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereStripeTestPublishKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereStripeTestSecretKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereTwitterLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminSettings whereWebsiteUrl($value)
 * @mixin \Eloquent
 */
class AdminSettings extends Model
{
    use HasFactory;
}
