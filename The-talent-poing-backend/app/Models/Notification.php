<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\Notification
 *
 * @property int $id
 * @property int|null $notify_by
 * @property int|null $notify_to
 * @property string|null $notification
 * @property string|null $link
 * @property int $is_read
 * @property string $notification_type
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|Notification newModelQuery()
 * @method static Builder|Notification newQuery()
 * @method static Builder|Notification query()
 * @method static Builder|Notification whereCreatedAt($value)
 * @method static Builder|Notification whereId($value)
 * @method static Builder|Notification whereIsRead($value)
 * @method static Builder|Notification whereLink($value)
 * @method static Builder|Notification whereNotification($value)
 * @method static Builder|Notification whereNotificationType($value)
 * @method static Builder|Notification whereNotifyBy($value)
 * @method static Builder|Notification whereNotifyTo($value)
 * @method static Builder|Notification whereStatus($value)
 * @method static Builder|Notification whereUpdatedAt($value)
 * @mixin Eloquent
 * @mixin Builder
 */
class Notification extends Model
{
    use HasFactory;

    public function userBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'notify_by', 'id');
    }

    public function userTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'notify_to', 'id');
    }

    public function scopeRead($query, $read = null)
    {
        if ($read === null) {
            return $query;
        }

        return $query->where('is_read', $read ? 1 : 0);
    }

}
