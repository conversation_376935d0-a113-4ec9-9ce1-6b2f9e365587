<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Errorlogs
 *
 * @property int $id
 * @property string|null $error_message
 * @property string|null $line_number
 * @property string|null $file_name
 * @property string|null $browser
 * @property string|null $operating_system
 * @property int|null $loggedin_id
 * @property string|null $ip_address
 * @property string $status 1=Active,0=Deactive
 * @property string $deleted 1=Deleted,0=Not Deleted
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs query()
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereBrowser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereDeleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereErrorMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereLineNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereLoggedinId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereOperatingSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Errorlogs whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Errorlogs extends Model
{   
    protected $table = 'errorlog';

    protected $fillable = [
        'error_message', 'line_number', 'file_name', 'browser', 'operating_system', 'loggedin_id', 'ip_address'
    ];

    protected $hidden = [
        
    ]; 

    

}
