<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Skills
 *
 * @property int $id
 * @property int $sector_id
 * @property string $skills
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Skills newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Skills newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Skills query()
 * @method static \Illuminate\Database\Eloquent\Builder|Skills whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Skills whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Skills whereSectorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Skills whereSkills($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Skills whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Skills whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Skills extends Model
{
    protected $table = 'skills';
    use HasFactory;
}