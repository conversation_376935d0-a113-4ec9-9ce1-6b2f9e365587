<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CandidatesFilter
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $section_name
 * @property string|null $job_status
 * @property string|null $currency
 * @property string|null $salary
 * @property string|null $experience
 * @property string|null $country_id
 * @property string|null $skills
 * @property string|null $sector
 * @property string|null $job_type
 * @property string|null $profile_status
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter query()
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereExperience($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereJobStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereJobType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereProfileStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereSalary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereSectionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereSector($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereSkills($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidatesFilter whereUserId($value)
 * @mixin \Eloquent
 */
class CandidatesFilter extends Model
{


    use HasFactory;

    protected $table = 'candidates_filters';

    protected $fillable = [
        'user_id',
        'section_name',
        'job_status',
        'currency',
        'salary',
        'experience',
        'country_id',
        'skills ',
        'sector',
        'job_type',
        'status'
    ];

}
