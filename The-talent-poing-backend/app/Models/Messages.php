<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\Messages
 *
 * @property int $id
 * @property int|null $sender_id
 * @property int|null $receiver_id
 * @property int|null $job_id
 * @property int|null $applicants_id
 * @property string|null $message_title
 * @property string|null $message_description
 * @property string|null $message_type
 * @property string $message_status
 * @property string|null $attachment_path
 * @property int|null $archived_messages
 * @property string|null $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Messages newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Messages newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Messages query()
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereApplicantsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereArchivedMessages($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereAttachmentPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereMessageDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereMessageStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereMessageTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereMessageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereReceiverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereSenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Messages whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Messages extends Model
{
    use HasFactory;
    protected $table = 'messages';

    protected $fillable = [
        'sender_id',
        'receiver_id'
    ];
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id', 'id')->with([
            'profile',
        ]);
    }

    public function scopeUnread($query)
    {
        return $query->where('message_status', 'unread');
    }

    public function scopeLast($query)
    {
        return $query->latest('created_at')->first();
    }


}
