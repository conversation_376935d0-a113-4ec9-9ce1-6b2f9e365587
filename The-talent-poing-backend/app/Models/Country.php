<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Countries
 *
 * @property int $id
 * @property string|null $country_name
 * @property string|null $slug
 * @property string|null $flag
 * @property string|null $currency
 * @property string|null $capital
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|Country newModelQuery()
 * @method static Builder|Country newQuery()
 * @method static Builder|Country query()
 * @method static Builder|Country whereCapital($value)
 * @method static Builder|Country whereCountryName($value)
 * @method static Builder|Country whereCreatedAt($value)
 * @method static Builder|Country whereCurrency($value)
 * @method static Builder|Country whereFlag($value)
 * @method static Builder|Country whereId($value)
 * @method static Builder|Country whereSlug($value)
 * @method static Builder|Country whereStatus($value)
 * @method static Builder|Country whereUpdatedAt($value)
 * @mixin Eloquent
 * @mixin Builder
 */
class Country extends Model
{
    use HasFactory;
}
