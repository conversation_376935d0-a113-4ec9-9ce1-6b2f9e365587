<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Industries
 *
 * @property int $id
 * @property string $name
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|Industry newModelQuery()
 * @method static Builder|Industry newQuery()
 * @method static Builder|Industry query()
 * @method static Builder|Industry whereCreatedAt($value)
 * @method static Builder|Industry whereId($value)
 * @method static Builder|Industry whereName($value)
 * @method static Builder|Industry whereStatus($value)
 * @method static Builder|Industry whereUpdatedAt($value)
 * @mixin Eloquent
 * @mixin Builder
 */
class Industry extends Model
{
    use HasFactory;
}
