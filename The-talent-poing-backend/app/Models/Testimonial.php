<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_name', 
        'user_profile_photo', 
        'position', 
        'company_name', 
        'review_comment',
    ];
    protected $hidden = [
        'user_profile_photo',
    ];

    // public function getUserProfilePhotoAttribute($value)
    // {
    //     // Assuming 'user_profile_photo' is stored in the 'storage/testimonal/' directory
    //     return asset('storage/images/testimonal/' . $value);
    // }
}
