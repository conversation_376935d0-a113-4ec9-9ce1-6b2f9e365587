<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobCountryFaqs extends Model
{
    use HasFactory;
    protected $table = 'job_country_faqs';

    protected $fillable = [
       'location_id',
       'location_name',
       'question',
       'answer',
       'more_about_location'
   ];

    public function country()
    {
        return $this->belongsTo(Country::class, 'location_id');
    }
}
