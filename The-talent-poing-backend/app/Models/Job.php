<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Src\FileManagement\Domain\File;

/**
 * App\Models\Jobs
 *
 * @property int $id
 * @property int|null $user_id
 * @property int|null $company_id
 * @property int|null $sector_id
 * @property string|null $job_title
 * @property string|null $job_slug
 * @property string|null $job_description
 * @property string|null $type_of_position
 * @property string|null $job_country
 * @property string|null $industry
 * @property string|null $experience
 * @property string|null $skills_required
 * @property string|null $monthly_fixed_salary_currency
 * @property string|null $monthly_fixed_salary_min
 * @property string|null $monthly_fixed_salary_max
 * @property string|null $available_vacancies
 * @property string|null $deadline
 * @property int $is_featured
 * @property int $hide_employer_details
 * @property string|null $background_banner_image
 * @property string|null $meta_tag
 * @property string|null $meta_desc
 * @property string|null $job_type
 * @property string|null $job_status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int $job_city
 * @method static Builder|Job newModelQuery()
 * @method static Builder|Job newQuery()
 * @method static Builder|Job query()
 * @method static Builder|Job whereAvailableVacancies($value)
 * @method static Builder|Job whereBackgroundBannerImage($value)
 * @method static Builder|Job whereCompanyId($value)
 * @method static Builder|Job whereCreatedAt($value)
 * @method static Builder|Job whereDeadline($value)
 * @method static Builder|Job whereExperience($value)
 * @method static Builder|Job whereHideEmployerDetails($value)
 * @method static Builder|Job whereId($value)
 * @method static Builder|Job whereIndustry($value)
 * @method static Builder|Job whereIsFeatured($value)
 * @method static Builder|Job whereJobCity($value)
 * @method static Builder|Job whereJobCountry($value)
 * @method static Builder|Job whereJobDescription($value)
 * @method static Builder|Job whereJobSlug($value)
 * @method static Builder|Job whereJobStatus($value)
 * @method static Builder|Job whereJobTitle($value)
 * @method static Builder|Job whereJobType($value)
 * @method static Builder|Job whereMetaDesc($value)
 * @method static Builder|Job whereMetaTag($value)
 * @method static Builder|Job whereMonthlyFixedSalaryCurrency($value)
 * @method static Builder|Job whereMonthlyFixedSalaryMax($value)
 * @method static Builder|Job whereMonthlyFixedSalaryMin($value)
 * @method static Builder|Job whereSectorId($value)
 * @method static Builder|Job whereSkillsRequired($value)
 * @method static Builder|Job whereTypeOfPosition($value)
 * @method static Builder|Job whereUpdatedAt($value)
 * @method static Builder|Job whereUserId($value)
 * @mixin Eloquent
 * @mixin Builder
 * @property-read Company|null $company
 * @property-read Country|null $country
 */
class Job extends Model
{
    use HasFactory;

    public static string $ACTIVE = 'active';
    protected $fillable = [
        'background_banner_image',
        'postal_code', 
        'street_address', 
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'job_country', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function related_industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, 'industry', 'id');
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(Cities::class, 'job_city', 'id');
    }

    public function banner(): BelongsTo
    {
        return $this->belongsTo(File::class, 'background_banner_image', 'uuid');
    }

    public function skill(): BelongsTo
    {
        return $this->belongsTo(EmployeeSkills::class, 'skills_required', 'id');
    }

    public function scopeActive($query)
    {
        return $query->where('job_status', 'active');
    }

    public function scopeCountry($query, $id)
    {
        return $query->where('job_country', $id);
    }

    public function scopeSavedJob($query, $user_id)
    {
        return $query->join('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
            ->where('saved_jobs.user_id', $user_id);
    }

    public function related_sector(): BelongsTo
    {
        return $this->belongsTo(Sector::class, 'sector_id', 'id');
    }

}
