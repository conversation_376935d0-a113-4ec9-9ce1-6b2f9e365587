<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CompanyProfileView
 *
 * @property int $id
 * @property int|null $company_id
 * @property int|null $user_id
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProfileView whereUserId($value)
 * @mixin \Eloquent
 */
class CompanyProfileView extends Model
{
    protected $table = 'company_profile_view';
    use HasFactory;

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
}