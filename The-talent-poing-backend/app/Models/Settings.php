<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings
 *
 * @property int $id
 * @property int|null $user_id
 * @property int $account_access
 * @property int $newsletter_access
 * @property int $recommendations_access
 * @property int $announcements_access
 * @property int $message_from_candidate_access
 * @property int $display_contact_no
 * @property int $display_email_address
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Settings newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Settings newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Settings query()
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereAccountAccess($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereAnnouncementsAccess($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereDisplayContactNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereDisplayEmailAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereMessageFromCandidateAccess($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereNewsletterAccess($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereRecommendationsAccess($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Settings whereUserId($value)
 * @mixin \Eloquent
 */
class Settings extends Model
{
    use HasFactory;
    protected $table = 'settings';
    protected $fillable = [
        'user_id',
        'account_access',
        'newsletter_access',
        'recommendations_access',
        'announcements_access',
        'message_from_candidate_access',
        'display_contact_no',
        'display_email_address',
        'status',
    ];
}
