<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyReview extends Model
{
    use HasFactory;

    protected $table = 'company_reviews';

    // public function company()
    // {
    //     return $this->belongsTo(Company::class, 'foreign_key');
    // }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function detailReview()
    {
        return $this->hasOne(DetailReview::class, 'review_id', 'id');
    }

    public function workExperience()
    {
        return $this->hasOne(CompanyReviewWorkExperience::class, 'review_id');
    }

    public function reportReviews()
    {
        return $this->hasMany(ReportReview::class, 'review_id', 'id');
    }

    public function helpfulReview()
    {
        return $this->hasMany(HelpfulReview::class, 'review_id', 'id');
    }

    public function userWorkExperiences()
    {
        return $this->hasMany(WorkExperience::class, 'user_id', 'user_id');
    }
}
