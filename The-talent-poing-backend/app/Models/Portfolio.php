<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Portfolio
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $title
 * @property string|null $portfolio_link
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $present
 * @property string|null $description
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio query()
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio wherePortfolioLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio wherePresent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Portfolio whereUserId($value)
 * @mixin \Eloquent
 */
class Portfolio extends Model
{
    protected $table = 'portfolio';
    use HasFactory;

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}