<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Src\FileManagement\Domain\File;

/**
 * App\Models\Company
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $company_name
 * @property string|null $company_slug
 * @property string|null $company_email
 * @property string|null $designation
 * @property string|null $company_website
 * @property string|null $company_location
 * @property string|null $company_sector
 * @property string|null $no_of_employees
 * @property string|null $company_description
 * @property string|null $company_logo
 * @property string|null $company_contact_no
 * @property int|null $available_resume_count
 * @property string|null $linkedin_link
 * @property string|null $twitter_link
 * @property string|null $instagram_link
 * @property string|null $facebook_link
 * @property string|null $background_banner_image
 * @property string|null $meta_tag
 * @property string|null $meta_desc
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|Company newModelQuery()
 * @method static Builder|Company newQuery()
 * @method static Builder|Company query()
 * @method static Builder|Company whereAvailableResumeCount($value)
 * @method static Builder|Company whereBackgroundBannerImage($value)
 * @method static Builder|Company whereCompanyContactNo($value)
 * @method static Builder|Company whereCompanyDescription($value)
 * @method static Builder|Company whereCompanyEmail($value)
 * @method static Builder|Company whereCompanyLocation($value)
 * @method static Builder|Company whereCompanyLogo($value)
 * @method static Builder|Company whereCompanyName($value)
 * @method static Builder|Company whereCompanySector($value)
 * @method static Builder|Company whereCompanySlug($value)
 * @method static Builder|Company whereCompanyWebsite($value)
 * @method static Builder|Company whereCreatedAt($value)
 * @method static Builder|Company whereDesignation($value)
 * @method static Builder|Company whereFacebookLink($value)
 * @method static Builder|Company whereId($value)
 * @method static Builder|Company whereInstagramLink($value)
 * @method static Builder|Company whereLinkedinLink($value)
 * @method static Builder|Company whereMetaDesc($value)
 * @method static Builder|Company whereMetaTag($value)
 * @method static Builder|Company whereNoOfEmployees($value)
 * @method static Builder|Company whereStatus($value)
 * @method static Builder|Company whereTwitterLink($value)
 * @method static Builder|Company whereUpdatedAt($value)
 * @method static Builder|Company whereUserId($value)
 * @mixin Eloquent
 * @mixin Builder
 * @property string|null $fk_logo_file_uuid
 * @method static Builder|Company whereFkLogoFileUuid($value)
 */
class Company extends Model
{
    protected $table = 'company';
    use HasFactory;

    public function membership(): HasOne
    {
        return $this->hasOne(Membership::class)->latestOfMany(); //newly add latest of many part
    }

    public function logo(): BelongsTo
    {
        return $this->belongsTo(File::class, 'fk_logo_file_uuid', 'uuid');
    }

    public function sector(): BelongsTo
    {
        return $this->belongsTo(Sector::class, 'company_sector', 'id');
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'company_location', 'id');
    }

    public function claim(): HasOne
    {
        return $this->hasOne(ClaimCompany::class)->latestOfMany(); //newly add latest of many part
    }

    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class, 'company_id', 'id')->with('country', 'company', 'company.logo', 'city')->active();
    }

    public function reviews()
    {
        return $this->hasMany(CompanyReview::class, 'company_id', 'id');
    }
}
