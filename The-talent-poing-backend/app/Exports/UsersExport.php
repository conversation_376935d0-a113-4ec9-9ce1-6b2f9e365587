<?php

namespace App\Exports;

use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Maatwebsite\Excel\Concerns\FromQuery;
// use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

// class UsersExport implements FromCollection, WithHeadings
class UsersExport implements FromQuery, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    // protected $userIds;

    // public function __construct($userIds = null)
    // {
    //     $this->userIds = $userIds;
    // }

    // public function collection()
    // {
    //     $columns = ['id', 'name', 'email', 'role', 'created_at', 'updated_at'];
    //     if ($this->userIds) {
    //        //dd("selected");
    //         // Fetch selected users
    //         return User::whereIn('id', $this->userIds)->select($columns)->get();
    //     }
    //    // dd("all");
    //     // Fetch all users
    //     return User::select($columns)->get();
    // }

    // public function headings(): array
    // {
    //     return [
    //         'ID', 'Name', 'Email', 'role', 'Created At', 'Updated At'
    //     ];
    //     // Fetch all column names from the 'users' table
    //     // return Schema::getColumnListing('users');
    // }

    protected $userIds;

    public function __construct($userIds = null)
    {
        $this->userIds = $userIds;
    }

    public function query()
    {
        $columns = ['id', 'name', 'email', 'role', 'created_at', 'updated_at'];

        if ($this->userIds) {
            return User::query()->whereIn('id', $this->userIds)->select($columns);
        }

        return User::query()->select($columns);
    }

    public function headings(): array
    {
        return [
            'id', 'name', 'email', 'role', 'created_at', 'updated_at'
        ];
    }
}
