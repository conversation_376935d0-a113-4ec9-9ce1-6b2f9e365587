<?php

namespace App\Helpers;

class Helper {
    public static function generateRandomPassword()
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomPassword = '';

        // Generate one uppercase character
        $randomPassword .= $characters[random_int(10, 35)]; // Uppercase characters start from index 10 to 35

        // Generate one lowercase character
        $randomPassword .= $characters[random_int(36, 61)]; // Lowercase characters start from index 36 to 61

        // Generate one number
        $randomPassword .= $characters[random_int(0, 9)]; // Numbers are from index 0 to 9

        // Generate 5 more random characters (to reach a total length of 8 characters)
        for ($i = 0; $i < 5; $i++) {
            $randomPassword .= $characters[random_int(0, $charactersLength - 1)];
        }

        // Shuffle the password to randomize the order of characters
        $randomPassword = str_shuffle($randomPassword);

        return $randomPassword;
    }
}