<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [

        Commands\ProfileCompletePercentage::class,
        Commands\SendBirthdayWishes::class,
        Commands\CompaniesProfileVisitorInSevenDays::class,
        Commands\EmployeeProfileVisitorInSevenDays::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        
        $schedule->command('birthday:wishes')->everyMinute();
        // $schedule->command('schedule:profilecompletepercentage')->everyMinute();
         // $schedule->command('schedule:companyprofilevisitinsevendays')->everyMinute();
        //  $schedule->command('schedule:employeeprofilevisitinsevendays')->everyMinute();
        // $schedule->command('emails:send')->everyMinute();

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
