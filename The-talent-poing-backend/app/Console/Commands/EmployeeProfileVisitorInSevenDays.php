<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class EmployeeProfileVisitorInSevenDays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
   
    protected $signature = 'schedule:employeeprofilevisitinsevendays';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Employee visitors within the last seven days';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $broadcast = new \App\Http\Controllers\Api\CronJobsController();
        $birthday_data = $broadcast->EmployeeProfileVisitedLastSevenDaysCronJobs();

        // \Log::info($birthday_data);
    }
}
