<?php
namespace App\Console\Commands;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use Carbon\Carbon;

class ProfileCompletePercentage extends Command
{ 
  /**

   * The name and signature of the console command.

   *

   * @var string

   */
  //protected $signature = 'email:profilecompletepercentage';
  protected $signature = 'schedule:profilecompletepercentage';
  /**

   * The console command description.

   *

   * @var string

   */
  protected $description = 'Profile Complete Percentage';
  /**

   * Create a new command instance.

   *

   * @return void

   */
  public function __construct()

  {
       parent::__construct();
  }
  /**

   * Execute the console command.

   *

   * @return mixed

   */
  public function handle()
  {
    $broadcast = new \App\Http\Controllers\Api\CronJobsController();
    $data = $broadcast->ProfileCompletePercentageCronJobs();

    \Log::info($data);

  }
}