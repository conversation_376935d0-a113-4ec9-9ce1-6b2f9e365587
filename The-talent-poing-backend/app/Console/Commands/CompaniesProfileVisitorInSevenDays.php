<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CompaniesProfileVisitorInSevenDays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
   
    protected $signature = 'schedule:companyprofilevisitinsevendays';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Company visitors within the last seven days';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $broadcast = new \App\Http\Controllers\Api\CronJobsController();
        $company_data = $broadcast->CompanyProfileVisitedLastSevenDaysCronJobs();

        \Log::info($company_data);
    }
}
