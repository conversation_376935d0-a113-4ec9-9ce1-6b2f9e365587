<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SendBirthdayWishes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'birthday:wishes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $broadcast = new \App\Http\Controllers\Api\CronJobsController();
        $birthday_data = $broadcast->BirthdayCronJobs();

        // \Log::info($birthday_data);
    }
}
