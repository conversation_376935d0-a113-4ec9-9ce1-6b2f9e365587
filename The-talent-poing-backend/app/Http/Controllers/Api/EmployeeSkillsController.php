<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\EmployeeSkills;
use App\Classes\ErrorsClass;
//use JWTAuth;
use DB;

class EmployeeSkillsController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getSingleUserSkill($id)
{ 
    try
    {
        $skills = DB::table('employee_skills')
            ->join('skills', 'employee_skills.skill_id', '=', 'skills.id')
            ->where('employee_skills.user_id', $id)
            ->where('employee_skills.status','=','active')
            ->get(['skills.*','employee_skills.*']);
            
        return response()->json(['status' => true, 'message' => 'Skills fetched', 'data' => $skills]);
    }
    catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
}

function addEmployeeSkills(Request $request) {
    try {
        $emp_skill = new EmployeeSkills();
        $emp_skill->user_id = $request->input('user_id');
        $emp_skill->skill_id = $request->input('skill_ids');
        $emp_skill->status = 'active';
        $emp_skill->save();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Employee skills added successfully'
        ], 200);
    } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
}
    // Route: DELETE /employee-skills/{id}
    function deleteEmployeeSkills($id) {
        try {
            DB::table('employee_skills')
                ->where('id', $id)
                ->update([
                    'status' => 'deleted',
                    'updated_at' => now()
                ]);
            return response()->json([
                'status' => 'success',
                'message' => 'Employee skill deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

}