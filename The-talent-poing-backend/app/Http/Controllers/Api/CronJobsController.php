<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Mail\WelcomeEmail;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\Interview;
use App\Models\Settings;
use Carbon\Carbon;
use App\Models\Notification;
//use JWTAuth;
use DB;

class CronJobsController extends Controller
{
    public function ProfileCompletePercentageCronJobs()
    {
        try {

            $profile_complete_percentage = User::where('profile_complete_percentage', '<', '100')->where('role', 'employee')->get();

            foreach ($profile_complete_percentage as $profile) {

                $notification = new Notification();
                $notification->notification = "Hi {$profile->name}, Complete your Talent Point profile and unlock your full potential. Employers are eager to discover your skills and experience. Update your profile ";
                $notification->notify_by = '1';
                $notification->notify_to = $profile->id;
                $notification->notification_type = "profile";
                $notification->save();
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function BirthdayCronJobs()
    {
        try {


            $today = Carbon::now()->format('m-d');
            $userbirthData = User::whereRaw("DATE_FORMAT(date_of_birth, '%m-%d') = '$today'")
                ->whereIn('role', ['employee', 'staff', 'employer'])
                ->get();

            foreach ($userbirthData as $users) {

                $notification = new Notification();
                $notification->notification = "Dear {$users->name}, Talent Point wishes you a very happy birthday! 🎉🎂🎈 May this special day be filled with joy, laughter, and wonderful moments that create beautiful memories. 🎁🎊 Have an amazing day! 😊";
                $notification->notify_by = '1';
                $notification->notify_to = $users->id;
                $notification->notification_type = "birthday";
                $notification->save();

                $data = [
                    'name' => $users->name,
                ];
                $settings = Settings::join('users','settings.user_id','users.id')
                ->where('settings.account_access','!=','0')
                ->first();

                if ( $settings) {
                Mail::to($users->email)->send(new WelcomeEmail());
                return "Email sent successfully!";
                }
          
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function CompanyProfileVisitedLastSevenDaysCronJobs()
    {
        try {

            $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();

            return  $sevenDaysAgo;

            $userData = User::where('role', 'employer')->where('status', 'active')->get();
            $visitedProfiles = '';
            foreach ($userData as $user_data) {
                $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();
                $visited_Profiles_data = DB::table('company_profile_view')
                    ->whereDate('created_at', '>=', $sevenDaysAgo)
                    ->where('company_id', $user_data->company_id)
                    ->count();
                $visitedProfiles .= $visited_Profiles_data;
            }
            return $visitedProfiles;
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function EmployeeProfileVisitedLastSevenDaysCronJobs()
    {
        try {
            $userData = User::where('role', 'employer')->where('status', 'active')->get();
            $visitedProfiles = '';
            foreach ($userData as $user_data) {
                $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();
                $visited_Profiles_data = DB::table('company_profile_view')
                    ->whereDate('created_at', '>=', $sevenDaysAgo)
                    ->where('company_id', $user_data->company_id)
                    ->count();
                $visitedProfiles .= $visited_Profiles_data;
            }
            return $visitedProfiles;
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
 
}
