<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Settings;
use App\Classes\ErrorsClass;
use Src\AppFramework\ApiController;

//use JWTAuth;

class SettingsController extends ApiController
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function createUserSetting(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'sometimes|required|exists:users,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors(),
                'status' => false,
            ], 200);
        }

        $setting = Settings::updateOrCreate(['user_id' => $request->id], $request->all());

        return response()->json(['success' => true, 'data' => $setting, 'message' => 'Setting updated successfully.'], 201);
    }


    public function getUserAccessSettings(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $settings = Settings::where('user_id', $user_id)->where('status', 'active')->orderBy('id', 'DESC')->first();
            return response()->json(['status' => true, 'data' => $settings], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateAccountAccessSettings(Request $request)
    {
        try {
            $settings = Settings::where('user_id', $request->user_id)->where('status', 'active')->first();
            if ($settings) {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['account_access'] = $request->account_access;
                $Input['status'] = 'active';
                Settings::where('user_id', $request->user_id)->update($Input);
            } else {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['account_access'] = $request->account_access;
                $Input['status'] = 'active';
                Settings::insert($Input);
            }
            return response()->json(['status' => true, 'message' => 'Settings saved successfully.'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateNewsLetterAccessSettings(Request $request)
    {
        try {

            $settings = Settings::where('user_id', $request->user_id)->where('status', 'active')->first();
            if ($settings) {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['newsletter_access'] = $request->news_letter;
                $Input['status'] = 'active';
                Settings::where('user_id', $request->user_id)->update($Input);
            } else {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['newsletter_access'] = $request->news_letter;
                $Input['status'] = 'active';
                Settings::insert($Input);
            }
            return response()->json(['status' => true, 'message' => 'Settings saved successfully.'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateRecommendationsAccessSettings(Request $request)
    {
        try {

            $settings = Settings::where('user_id', $request->user_id)->where('status', 'active')->first();
            if ($settings) {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['recommendations_access'] = $request->recommendations;
                $Input['status'] = 'active';
                Settings::where('user_id', $request->user_id)->update($Input);
            } else {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['recommendations_access'] = $request->recommendations;
                $Input['status'] = 'active';
                Settings::insert($Input);
            }
            return response()->json(['status' => true, 'message' => 'Settings saved successfully.'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateAnnouncementsAccessSettings(Request $request)
    {
        try {

            $settings = Settings::where('user_id', $request->user_id)->where('status', 'active')->first();
            if ($settings) {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['announcements_access'] = $request->announcements;
                $Input['status'] = 'active';
                Settings::where('user_id', $request->user_id)->update($Input);
            } else {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['announcements_access'] = $request->announcements;
                $Input['status'] = 'active';
                Settings::insert($Input);
            }
            return response()->json(['status' => true, 'message' => 'Settings saved successfully.'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateMessageFromCandidateAccessSettings(Request $request)
    {
        try {

            $settings = Settings::where('user_id', $request->user_id)->where('status', 'active')->first();
            if ($settings) {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['message_from_candidate_access'] = $request->message_from_recruiters;
                $Input['status'] = 'active';
                Settings::where('user_id', $request->user_id)->update($Input);
            } else {
                $Input = [];
                $Input['user_id'] = $request->user_id;
                $Input['message_from_candidate_access'] = $request->message_from_recruiters;
                $Input['status'] = 'active';
                Settings::insert($Input);
            }
            return response()->json(['status' => true, 'message' => 'Settings saved successfully.'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
