<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Industry;

/**
 * @group Industries
 */
class IndustriesController extends Controller
{


    public function getAllIndustries()
    {
        try {
            $industry = Industry::where('status', 'active')->orderBy('name', 'asc')->get();

            return response()->json([
                'success' => true,
                'data' => $industry
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllIndustriesForAdmin()
    {
        try {
            $industry = Industry::where('status', 'active')->orderByDesc('id')->get();

            return response()->json([
                'success' => true,
                'data' => $industry
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


     public function editAndSaveIndustries(Request $request)
    {
        try {

            if($request->id){

                $industry = Industry::findOrFail($request->id);
                $industry->name = $request->name;
                $industry->save();

                return response()->json([
                'status' => true,
                'message' => 'Industry updated successfully',
            ], 200);

            }else {

                $industry = new Industry;
                $industry->name = $request->name;
                $industry->status = $request->status ?? 'active';
                $industry->save();

                return response()->json([
                'status' => true,
                'message' => 'Industry added successfully',
                ], 200);
            }



        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteIndustries(Request $request)
    {
        try {

            $sector = Industry::find($request->id);
            $sector->status = 'deleted';
            $sector->save();

            return response()->json([
                'status' => true,
                'message' => 'industry has been deleted successfully!',
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
