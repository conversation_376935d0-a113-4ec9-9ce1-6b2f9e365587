<?php

namespace App\Http\Controllers\Api;

use App\Models\ClaimCompany;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Src\AppFramework\ApiController;
use Src\EmployerManagement\Infrastructure\Resources\EmployeeResource;
use Src\EmployerManagement\Infrastructure\Resources\EmployerResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Facades\Response;
use App\Models\WorkExperience;
use App\Models\Education;
use App\Models\EmployeeSkills;
use App\Models\Portfolio;
use App\Models\Languages;
use App\Models\Country;
use Carbon\Carbon;
use DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\UsersExport;
use Image;
use Illuminate\Support\Str;
use App\Models\PasswordReset;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Jobs\UserExportJob;
use App\Helpers\Helper;
use App\Mail\StaffMemberAdded;
use Illuminate\Support\Facades\Mail;


class UserController extends ApiController
{
    public function isFirstLogin(Request $request, $user_id)
    {
        try {
            $user = User::find($user_id);
            if ($user && $user->first_login == '0') {
                $user->first_login = '1';
                $user->save();
                return response()->json([
                    'status' => true,
                    'message' => 'first time login status update successfully!',
                    'data' => ''
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'first time login status  not update successfully!',
                    'data' => ''
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateUnlockInstantApply(Request $request, $user_id)
    {
        try {
            $user = User::findOrFail($user_id);
            $user->unlock_instant_apply = $request->input('unlock_instant_apply');
            $user->save();

            if ($user) {

                return response()->json([
                    'status' => true,
                    'message' => 'Unlock Instant Apply updated successfully',
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Something went wrong!',
                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getJobPreferences($user_id)
    {
        try {
            $user = User::findOrFail($user_id);
            $job_preferences = [
                'where_job_search' => $user->where_job_search,
                'job_type' => $user->job_type,
            ];
            return response()->json([
                'status' => 'success',
                'data' => $job_preferences,
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserDetails($id)
    {
        try {
            $user = User::with('company', 'company.logo')->leftJoin('resumes', function ($join) {
                $join->on('users.id', '=', 'resumes.user_id')
                    ->where(function ($query) {
                        $query->where('resumes.default_resume', 1)
                            ->where('resumes.status', '=', 'active');
                    });
            })
                ->leftJoin('countries', 'users.where_currently_based', '=', 'countries.id')
                ->leftJoin('work_experience', function ($join) {
                    $join->on('users.id', '=', 'work_experience.user_id')
                        ->where('work_experience.currently_work_here', 1);
                })
                ->leftJoin('education', function ($join) {
                    $join->on('users.id', '=', 'education.user_id');
                })
                ->where('users.id', $id)
                ->select('users.*', 'resumes.resume_pdf_path', 'resumes.default_resume', 'resumes.created_at as resumedate', 'countries.country_name', 'work_experience.company', 'education.degree')
                ->first();

            if ($user) {
                if ($user->company_id) {
                    $company_data = Company::with('logo')->findOrFail($user->company_id);
                } else {
                    $company_data = [];
                }
                if ($user->role == 'employer' || $user->role == 'staff') {

                    if ($user->role == 'employer') {

                        $membership = DB::table('membership')->select('expire_at', 'plan_id')->where('user_id', $user->id)->latest('id')->first();
                    } else {
                        $membership = DB::table('membership')->select('expire_at', 'plan_id')->where('user_id', $company_data->user_id)->latest('id')->first();
                    }


                    // $expireAt = Carbon::parse($membership->expire_at);
                    // $now = Carbon::now();

                    $hasExpired = 2; // Initialize the variable
                    $plan = ''; // Initialize $plan as empty by default

                    if ($membership) {
                        $expireAt = Carbon::parse($membership->expire_at);
                        $now = Carbon::now();

                        //if ($expireAt->isPast()) {
                        //    $hasExpired = true; // Membership has expired
                        //}
                        $plan = $membership->plan_id; // Set $plan only when membership is available
                    }
                } else {
                    $hasExpired = false; // Initialize the variable
                    $plan = '';
                }
            } else {
                $company_data = [];
                $hasExpired = false;
                $plan = '';
            }


            if ($user) {
                return response()->json([
                    'status' => true,
                    'user' => $user,
                    'company_data' => $company_data,
                    'membership' => $hasExpired,
                    'plan' => $plan,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'user' => '',
                    'company_data' => '',
                    'membership' => $hasExpired,
                    'plan' => $plan
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserDetailsslug($slug)
    {
        try {

            $userid = User::select("id")->where("slug", $slug)->first();

            $user = User::leftJoin('resumes', function ($join) {
                $join->on('users.id', '=', 'resumes.user_id')
                    ->where(function ($query) {
                        $query->where('resumes.default_resume', 1)
                            ->where('resumes.status', 'active');
                    });
            })
                ->leftJoin('countries', 'users.where_currently_based', '=', 'countries.id')
                ->leftJoin('work_experience', function ($join) {
                    $join->on('users.id', '=', 'work_experience.user_id')
                        ->where('work_experience.currently_work_here', 1);
                })
                ->leftJoin('education', function ($join) {
                    $join->on('users.id', '=', 'education.user_id');
                })
                ->where('users.id', $userid->id)
                ->select('users.*', 'resumes.resume_pdf_path', 'resumes.default_resume', 'countries.country_name', 'work_experience.company', 'education.degree')
                ->first();

            // if($user->company_id){
            //     $company_data = Company::findOrFail($user->company_id);
            // } else {
            //     $company_data = [];
            // }

            if ($userid) {
                $work_experience = WorkExperience::where('user_id', $userid->id)->get();
            } else {
                $work_experience = [];
            }

            if ($userid) {
                $Education = Education::where('user_id', $userid->id)->get();
            } else {
                $Education = [];
            }

            if ($userid) {

                //$skills = EmployeeSkills::where('user_id',$userid->id)->where('status','=','active')->get();
                $skills = EmployeeSkills::join('skills', 'employee_skills.skill_id', '=', 'skills.id')
                    ->where('employee_skills.user_id', $userid->id)
                    ->where('employee_skills.status', '=', 'active')
                    ->get(['skills.*', 'employee_skills.*']);
            } else {
                $skills = [];
            }
            if ($userid) {
                $portfolio = Portfolio::where('user_id', $userid->id)->where('status', '=', 'active')->get();
            } else {
                $portfolio = [];
            }
            if ($userid) {
                $language = Languages::where('user_id', $userid->id)->where('status', '=', 'active')->get();
            } else {
                $Languages = [];
            }

            if ($user) {
                return response()->json([
                    'status' => true,
                    'user' => $user,
                    //'company_data' => $company_data
                    'work_experience' => $work_experience,
                    'education' => $Education,
                    'skills' => $skills,
                    'portfolio' => $portfolio,
                    'language' => $language
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'user' => '',
                    //'company_data' => '',
                    'Education' => '',
                    'work_experience' => '',
                    'skills' => '',
                    'portfolio' => '',
                    'language' => ''
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteUser($id)
    {
        try {
            $user = User::findOrFail($id);
            //$user->status();
            $user->status = 'deleted';
            // DB::table('education')->where('user_id', $id)->delete()
            // DB::table('portfolio')->where('user_id', $id)->delete()
            // DB::table('work_experience')->where('user_id', $id)->delete()
            // DB::table('employee_skills')->where('user_id', $id)->delete()
            // DB::table('applications')->where('user_id', $id)->delete()
            // DB::table('resumes')->where('user_id', $id)->delete()
            // DB::table('company')->where('user_id', $id)->delete()
            // DB::table('jobs')->where('user_id', $id)->update(['job_status' => 'deleted']);
            DB::table('education')->where('user_id', $id)->delete();
            DB::table('portfolio')->where('user_id', $id)->delete();
            DB::table('work_experience')->where('user_id', $id)->delete();
            DB::table('employee_skills')->where('user_id', $id)->delete();
            DB::table('applications')->where('user_id', $id)->delete();
            DB::table('resumes')->where('user_id', $id)->delete();
            DB::table('company')->where('user_id', $id)->delete();
            DB::table('jobs')->where('user_id', $id)->update(['job_status' => 'deleted']);
            $applicationUserIds = DB::table('applications')->where('jobpost_by_userId', $id)->pluck('user_id');
            $user->delete();
            foreach ($applicationUserIds as $userId) {
                $user = User::find($userId);
                // Mail::to($user->email)->send('applicationdelete');
                $message = 'Dear ' . $user->name . ', Your job application has been deleted due to user account deletion.';

                // Mail::raw($message, function ($message) use ($user) {
                //     $message->to($user->email)->subject('Job Application Deleted');
                // });
            }
            return response()->json([
                'status' => true,
                'message' => 'User deleted successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteUsers(Request $request)
    {
        try {

            // Retrieve user IDs from the request payload
            $userIds = $request->input('usersId');

            // dd($userIds);


            // Update the status for each user to 'deleted'
            User::whereIn('id', $userIds)->update(['status' => 'deleted']);

            // Delete related records in bulk for the provided user IDs
            DB::table('education')->whereIn('user_id', $userIds)->delete();
            DB::table('portfolio')->whereIn('user_id', $userIds)->delete();
            DB::table('work_experience')->whereIn('user_id', $userIds)->delete();
            DB::table('employee_skills')->whereIn('user_id', $userIds)->delete();
            DB::table('applications')->whereIn('user_id', $userIds)->delete();
            DB::table('resumes')->whereIn('user_id', $userIds)->delete();
            DB::table('company')->whereIn('user_id', $userIds)->delete();

            // Set job status to 'deleted' for jobs posted by these users
            DB::table('jobs')->whereIn('user_id', $userIds)->update(['job_status' => 'deleted']);

            // Notify applicants for jobs posted by these users
            $applicationUserIds = DB::table('applications')
                ->whereIn('jobpost_by_userId', $userIds)
                ->pluck('user_id')
                ->unique();

            // foreach ($applicationUserIds as $userId) {
            //     $user = User::find($userId);
            //     if ($user) {
            //         $message = 'Dear ' . $user->name . ', Your job application has been deleted due to user account deletion.';

            //         // Uncomment the following lines to send an email notification
            //         /*
            //     Mail::raw($message, function ($mail) use ($user) {
            //         $mail->to($user->email)->subject('Job Application Deleted');
            //     });
            //     */
            //     }
            // }

            // Delete the users from the database
            User::whereIn('id', $userIds)->delete();

            return response()->json([
                'status' => true,
                'message' => 'Users deleted successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteEmployers(Request $request)
    {
        try {

            // Retrieve user IDs from the request payload
            $userIds = $request->input('usersId');

            // dd($userIds);


            // Update the status for each user to 'deleted'
            User::whereIn('id', $userIds)->update(['status' => 'deleted']);

            // Delete related records in bulk for the provided user IDs
            // DB::table('education')->whereIn('user_id', $userIds)->delete();
            // DB::table('portfolio')->whereIn('user_id', $userIds)->delete();
            // DB::table('work_experience')->whereIn('user_id', $userIds)->delete();
            // DB::table('employee_skills')->whereIn('user_id', $userIds)->delete();
            // DB::table('applications')->whereIn('user_id', $userIds)->delete();
            // DB::table('resumes')->whereIn('user_id', $userIds)->delete();
            // DB::table('company')->whereIn('user_id', $userIds)->delete();

            // Set job status to 'deleted' for jobs posted by these users
            // DB::table('jobs')->whereIn('user_id', $userIds)->update(['job_status' => 'deleted']);

            // Notify applicants for jobs posted by these users
            // $applicationUserIds = DB::table('applications')
            //     ->whereIn('jobpost_by_userId', $userIds)
            //     ->pluck('user_id')
            //     ->unique();

            // foreach ($applicationUserIds as $userId) {
            //     $user = User::find($userId);
            //     if ($user) {
            //         $message = 'Dear ' . $user->name . ', Your job application has been deleted due to user account deletion.';

            //         // Uncomment the following lines to send an email notification
            //         /*
            //     Mail::raw($message, function ($mail) use ($user) {
            //         $mail->to($user->email)->subject('Job Application Deleted');
            //     });
            //     */
            //     }
            // }

            // Delete the users from the database
            User::whereIn('id', $userIds)->delete();

            return response()->json([
                'status' => true,
                'message' => 'Employers deleted successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateEmployee(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            $user->name = $request->input('name');
            $user->contact_no = $request->input('contact_no');
            $user->gender = $request->input('gender');
            $user->bio = $request->input('bio');
            $user->date_of_birth = $request->input('date_of_birth');
            $user->years_of_experience = $request->input('years_of_experience');
            $user->currency = $request->input('currency');
            $user->current_salary = $request->input('current_salary');
            $user->desired_salary = $request->input('desired_salary');
            $user->nationality = $request->input('nationality');
            // $user->where_currently_based = $request->input('where_currently_based'); // new changes
            $user->countries = $request->input('countries'); // new chnages
            // $user->where_job_search = $request->input('where_job_search'); // new changes
            $user->cities = $request->input('cities'); // new chnages
            $user->current_position = $request->input('current_position');
            $user->linked_id = $request->input('linked_id');
            $user->google_id = $request->input('google_id');
            $user->contact_no = $request->input('contact_no');
            $user->updated_at = Carbon::now();

            if ($request->hasFile('profileImage')) {
                $image = $request->file('profileImage');
                $fileName = time() . '.' . $image->getClientOriginalExtension();
                $img = Image::make($image->getRealPath());
                $img->stream();
                Storage::disk('public')->put('images/userprofileImg/' . $fileName, $img);
                $user->profile_image = $fileName;
            }

            $user->save();
            return response()->json([
                'status' => 'success',
                'message' => 'User updated successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateSocialLinks(Request $request, $id)
    {
        $user = User::findOrFail($id);

        // Update the social links
        $user->facebook_link = $request->input('facebook_link');
        $user->twitter_link = $request->input('twitter_link');
        $user->linkedin_link = $request->input('linkedin_link');
        $user->website_url = $request->input('website_url');
        $user->instagram_link = $request->input('instagram_link');

        $user->save();

        return response()->json([
            'message' => 'Social links updated successfully',
            'user' => $user
        ]);
    }

    public function getSocialLinks($id)
    {
        $user = User::findOrFail($id);
        return response()->json(['status' => true, 'message' => 'Data Fetched', 'data' => $user]);
    }

    public function updateJobStatus($user_id, $value)
    {
        try {
            $user = User::find($user_id);
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found',
                ], 404);
            }
            $user->job_status = $value;
            $user->save();
            return response()->json([
                'status' => true,
                'message' => 'User status updated successfully',
                'data' => $user,
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateJobPref(Request $request, $id)
    {
        try {
            $job_type = $request->input('job_type');
            $job_status = $request->input('job_status');
            if ($job_status) {
                $jobStatus = $job_status;
            } else {
                $jobStatus = 'ready_to_interview';
            }

            // Update the job information for the user

            User::where('id', $id)->update([
                'job_type' => $job_type,
                'job_status' => $jobStatus,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Job information updated successfully',
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function deleteJobType(Request $request, $id)
    {
        try {
            $jobTypeToDelete = $request->input('job_type');
            $user = User::findOrFail($id);
            $currentJobTypes = json_decode($user->job_type, true);
            $indexToDelete = array_search($jobTypeToDelete, $currentJobTypes);

            if ($indexToDelete !== false) {
                array_splice($currentJobTypes, $indexToDelete, 1);
                $user->update([
                    'job_type' => json_encode($currentJobTypes),
                ]);

                return response()->json([
                    'status' => true,
                    'message' => 'Job type deleted successfully',
                ], 200);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Job type not found in user\'s job types',
                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateUserRole(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'role' => ['required', Rule::in(['employee', 'employer'])],
            ]);

            $user = (new User)->find(Auth::id());
            $user->role = $request->get('role');
            $user->save();
            return $this->respondWithSuccess();
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function updateEmployeeTellUsInfo(Request $request): JsonResponse
    {
        try {
            $user = (new User)->find(Auth::id());
            // $user->where_currently_based = $request->input('where_currently_based'); // new chnages
            $user->countries = $request->input('countries'); // new chnages
            $user->current_position = $request->input('current_position');
            // $user->where_job_search = $request->input('where_job_search'); // new changes
            $user->cities = $request->input('cities'); // new chnages
            $user->nationality = $request->input('nationality');

            $selectedCountry = (new Country)->where('id', $request->input('where_job_search'))->first();
            if ($selectedCountry) {
                $user->currency = $selectedCountry->currency;
            }
            $user->save();
            return $this->respondWithSuccess();
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function updateUserAccountInfo(Request $request): JsonResponse
    {
        try {
            Validator::make($request->all(), [
                'password' => 'required|string|min:8',
                'phone' => 'required|string|min:8',
                'role' => 'required',
            ]);
            $user = (new User)->find(Auth::id());
            $user->contact_no = $request->get('phone');
            $user->role = $request->get('role');
            //TODO: consider saver the password only if it's null
            $user->password = Hash::make($request->get('password'));
            $user->save();
            return $this->respondWithSuccess();
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return $this->respondError($e->getMessage());
        }
    }

    public function addTeamMembers(Request $request)
    {
        try {

            $team_members_email = $request['team_members_email'];
            $company_id = $request['company_id'];
            $created_by_id = $request['created_by_id'];
            $available_resume_count = $request['available_resume_count'];

            $membership = DB::table('membership')
                ->select('plan_id')
                ->where('user_id', $created_by_id)
                ->latest('id')
                ->first();

            if ($membership) {
                if ($membership->plan_id != 1) {
                    $available_resume_count = 500;
                } else {
                    $available_resume_count = 0;
                }
            } else {
                $available_resume_count = 0;
            }


            $role = $request['role'];
            $password = Helper::generateRandomPassword();
            foreach ($team_members_email as $team_members_email_data) {
                $team_members = User::where('email', $team_members_email_data['team_member_email'])->where('role', 'staff')->count();
                if ($team_members > 0) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Team member already exits',
                        'data' => '',
                    ]);
                } else {
                    $email_parts = explode('@', $team_members_email_data['team_member_email']);
                    $name = $email_parts[0];

                    $user = User::create([
                        'name' => $name,
                        'email' => $team_members_email_data['team_member_email'],
                        'password' => Hash::make($password),
                        'role' => $role,
                        'available_resume_count' => $available_resume_count,
                        'company_id' => $company_id,
                        'created_by_id' => $created_by_id,
                        'status' => 'active'

                    ]);

                    Mail::to($team_members_email_data['team_member_email'])->send(
                        new StaffMemberAdded(
                            $name,
                            $password,
                            $team_members_email_data['team_member_email'],
                            $available_resume_count
                        )
                    );
                }
            }
            return response()->json([
                'status' => true,
                'message' => 'Team member has been added successfully',
                'data' => '',
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function social_data_save(Request $request)
    {
        try {
            $user = User::where('email', $request->email)->where('status', 'active')->first();
            if ($user) {
                //$credentials = $request->only('email', 'password');
                //$token = Auth::attempt($credentials);
                // if (!$token) {
                //     return response()->json([
                //         'status' => false,
                //         'message' => 'Please enter correct credentials!.',
                //         //'message' => 'Unauthorized',
                //     ], 401);
                // }
                //$user = Auth::user();
                return response()->json([
                    'status' => true,
                    'message' => 'user Loggedin successfully',
                    'user' => $user,
                    // 'authorisation' => [
                    //     'token' => $token,
                    //     'type' => 'bearer',
                    // ]
                ]);
            } else {
                $deleted_user = User::where('email', $request->email)->where('status', 'deleted')->first();
                $slug = $this->generateUniqueSlug($request->name);
                if ($deleted_user) {
                    User::where('email', $request->email)->update(['email' => $request->email . '1']);
                    $data = $request->all();
                    $data['password'] = Hash::make($request->password);
                    if ($request->login_type == 'google') {
                        $data['google_id'] = 'google';
                    } else if ($request->login_type = 'linkedin') {
                        $data['linked_id'] = 'linkedin';
                    }
                    $data['profile_image'] = $request->profile_image;
                    $data['slug'] = $slug;
                    $user = new User();
                    $register = $user->create($data);
                } else {
                    $data = $request->all();
                    $data['password'] = Hash::make($request->password);
                    if ($request->login_type == 'google') {
                        $data['google_id'] = 'google';
                    } else if ($request->login_type = 'linkedin') {
                        $data['linked_id'] = 'linkedin';
                    }
                    $data['profile_image'] = $request->profile_image;
                    $data['slug'] = $slug;
                    $user = new User();
                    $register = $user->create($data);
                }
                if ($register) {
                    //$token = Auth::login($register);
                    return response()->json([
                        'status' => true,
                        'message' => 'User created successfully',
                        'user' => $register,
                        // 'authorisation' => [
                        //     'token' => $token,
                        //     'type' => 'bearer',
                        // ]
                    ]);
                } else {
                    return response()->json(['message' => "There has been error for to register the user"], 200);
                }
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateUserAccountDetails(Request $request, $id)
    {
        try {
            $validator = Validator::make(
                $request->all(),
                [
                    'fk_profile_file_uuid' => 'sometimes|required|uuid',
                    'name' => 'sometimes|required|string',
                    'email' => 'sometimes|required|email',
                ],
                [
                    'fk_profile_file_uuid.required' => 'Profile UUID is required.',
                    'fk_profile_file_uuid.uuid' => 'Invalid UUID format for logo.',
                ]
            );
            if ($validator->fails()) {
                return response()->json([
                    'errors' => $validator->errors(),
                    'status' => false,
                ], 200);
            }

            $user = User::findOrFail($id);

            if (!$user) {
                return response()->json(['message' => 'User not found'], 404);
            }
            if ($request->has('fk_profile_file_uuid')) {

                $user->profile_image = $request->fk_profile_file_uuid;
                $user->save();
            } else {
                $user->update($request->all());
            }

            return response()->json([
                'status' => true,
                'message' => 'User updated successfully',
                'data' => $user->profile_image,
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateAdminLogo(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            if ($request->hasFile('profile_image')) {
                $image = $request->file('profile_image');
                $fileName = time() . '.' . $image->getClientOriginalExtension();
                $img = Image::make($image->getRealPath());
                $img->stream();
                Storage::disk('public')->put('images/userprofileImg' . '/' . $fileName, $img);
                $user->profile_image = $fileName;

                // $file = $request->file('profile_image');
                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $imageName = $randomNumber .'.'. $file->getClientOriginalName();
                // //$file->move('public/images/userprofileImg', $imageName);
                // $file->move(public_path('images/userprofileImg'), $imageName);
                // $user->profile_image = $imageName;
            }
            $user->save();
            return response()->json(['status' => true, 'message' => 'Admin Image updated successfully', 'data' => $user], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function changepassowrd(Request $request)
    {
        try {
            $user_data = User::where('id', $request->user_id)->first();
            if ($user_data && Hash::check($request->currentPassword, $user_data->password)) {
                $update_passowrd = User::where('id', $request->user_id)->update(['password' => Hash::make($request->newPassword)]);
                return response()->json([
                    'status' => true,
                    'message' => 'password changed successfully',
                    'data' => '',
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Current password is wrong',
                    'data' => '',
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateShowContact(Request $request, $id)
    {
        $user = User::find($id);
        $user->showcontact_no = $request->input('showcontact_no', 1);
        $user->save();

        return response()->json(['message' => 'Show contact updated successfully', 'status' => true, 'data' => $user]);
    }

    public function updateShowEmail(Request $request, $id)
    {
        $user = User::find($id);
        $user->isShowEmail = $request->input('isShowEmail', 1);
        $user->save();

        return response()->json(['message' => 'Show contact updated successfully', 'status' => true, 'data' => $user]);
    }

    public function update2fa(Request $request, $id)
    {
        $user = User::find($id);
        $user->is2FA = $request->input('is2FA', 1);
        $user->save();

        return response()->json(['message' => 'Two Factor updated successfully', 'status' => true, 'data' => $user]);
    }

    public function showCompletionPercentage(Request $request, $userId)
    {
        $completionPercentage = 0;

        $user = DB::table('users')->where('id', $userId)->first();
        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }
        $completionPercentage += 20;

        $resume = DB::table('resumes')->where('user_id', $userId)->where('status', 'active')->first();
        if ($resume) {
            $completionPercentage += 20;
            $resumeCheckbox = true;
        } else {
            $resumeCheckbox = false;
        }

        $userDetails = DB::table('users')->where('id', $userId)->whereNotNull('date_of_birth')
            ->whereNotNull('gender')
            ->whereNotNull('contact_no')
            ->whereNotNull('bio')
            ->whereNotNull('current_salary')
            ->first();
        if ($userDetails) {
            $completionPercentage += 20;
            $updateInfoCheckbox = true;
        } else {
            $updateInfoCheckbox = false;
        }

        $education = DB::table('education')->where('user_id', $userId)->first();
        $portfolio = DB::table('portfolio')->where('user_id', $userId)->first();
        $experience = DB::table('work_experience')->where('user_id', $userId)->first();

        if ($education || $portfolio || $experience) {
            $completionPercentage += 20;
            $professionalInfoCheckbox = true;
        } else {
            $professionalInfoCheckbox = false;
        }

        $application = DB::table('applications')->where('user_id', $userId)->first();
        if ($application) {
            $completionPercentage += 20;
            $applyopeningcheckbox = true;
        } else {
            $applyopeningcheckbox = false;
        }

        $data = DB::table('users')
            ->where('id', $userId)
            ->update(['profile_complete_percentage' => $completionPercentage]);

        return response()->json([
            'data' => $completionPercentage,
            'status' => true,
            'message' => 'Data fetch',
            'checkboxes' => [
                'resume' => $resumeCheckbox,
                'updateInfo' => $updateInfoCheckbox,
                'professionalInfo' => $professionalInfoCheckbox,
                'applyOpening' => $applyopeningcheckbox
                //'uploadResume' => ($completionPercentage >= 80),
            ],
        ]);
    }

    public function getEmployerUserDetails($id)
    {
        try {
            $user_data = User::select('users.*', 'company.company_name', 'countries.country_name')
                ->join('company', 'users.company_id', '=', 'company.id')
                ->join('countries', 'company.company_location', '=', 'countries.id')
                ->where('users.id', $id)
                ->first();
            if ($user_data) {
                return response()->json([
                    'status' => true,
                    'user' => $user_data
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'user' => ''
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getActiveEmployer()
    {
        $activeEmployerCount = User::where('role', 'employer')->where('status', 'active')->count();

        return response()->json([
            'data' => $activeEmployerCount,
            'message' => 'Employer Fetched',
            'status' => true
        ]);
    }

    public function getTestAllUsers()
    {
        $activeEmployerCount = User::get();

        return response()->json([
            'data' => $activeEmployerCount,
            'message' => 'Employer Fetched',
            'status' => true
        ]);
    }

    public function getActiveEmployee(): JsonResponse
    {
        $activeEmployeeCount = (new User)->where(function (Builder $query) {
            $query->where('role', User::$ROLE_EMPLOYEE);
        })
            ->where('status', 'active')
            ->count();

        return response()->json([
            'data' => $activeEmployeeCount,
            'message' => 'Employees Fetched',
            'status' => true
        ]);
    }

    public function getInActiveEmployee()
    {
        $activeEmployeeCount = User::where('role', 'employee')->where('status', 'deleted')->count();

        return response()->json([
            'data' => $activeEmployeeCount,
            'message' => 'Employees Fetched',
            'status' => true
        ]);
    }
    // public function getEmployers(Request $request): AnonymousResourceCollection
    // {
    //     $sortBy = $request->get('sort_by');
    //     $name = $request->get('name', '');
    //     $pageSize = $request->get('pageSize');

    //     $companyIds = ClaimCompany::pluck('company_id')->toArray();

    //     $employers = User::with([
    //         'company.membership',
    //         'company.logo',
    //         'company.reviews' => function ($query) {
    //             $query->selectRaw('company_id, AVG(CAST(rating AS DECIMAL(10,2))) as average_rating, COUNT(id) as total_reviews')
    //                 // Fix with casting
    //                 ->groupBy('company_id'); // Group by company for calculations
    //         }
    //     ])
    //         ->where('role', 'employer')
    //         ->where('status', '!=', 'deleted')
    //         ->whereHas('company', function (Builder $query) use ($name, $sortBy) {
    //             $query->where('company_name', 'like', '%' . $name . '%');
    //             if ($sortBy) {
    //                 $query->where('status', $sortBy);
    //             }
    //         })
    //         ->orderByDesc('id')
    //         ->paginate($pageSize);

    //     $claimedCompanies = User::with([
    //         'company.membership',
    //         'company.logo',
    //         'claim',
    //         'company.reviews' => function ($query) {
    //             $query->selectRaw('company_id, AVG(CAST(rating AS DECIMAL(10,2))) as average_rating, COUNT(id) as total_reviews') // Fix with casting
    //                 ->groupBy('company_id'); // Group by company for calculations
    //         }
    //     ])
    //         ->where('role', 'employer')
    //         ->where('status', '!=', 'deleted')
    //         ->whereHas('company', function (Builder $query) use ($name, $sortBy, $companyIds) {
    //             $query->where('company_name', 'like', '%' . $name . '%');
    //             $query->whereIn('id', $companyIds);
    //             if ($sortBy) {
    //                 $query->where('status', $sortBy);
    //             }
    //         })
    //         ->orderByDesc('id')
    //         ->paginate();

    //     $combineData = $employers->concat($claimedCompanies);

    //     return EmployerResource::collection($employers);
    // }


    public function getEmployers(Request $request): AnonymousResourceCollection
    {
        $pageSize = $request->get('pageSize', 10);

        // Retrieve filter inputs

        // new addition name filter top of page
        $name = $request->get('name');

        $accountTypes = $request->get('account_type'); // e.g., ["Free", "PRO"]
        $status = $request->get('status'); // e.g., "requested"
        $locations = $request->get('location', []); // e.g., ["Dubai", "Abu Dhabi"]
        $activity = $request->get('activity'); // e.g., "last_24_hours"
        $ratingFilter = $request->get('rating'); // e.g., "3_and_above", "4_and_above"
        $teamMemberSort = $request->get('team_member'); // e.g., "low_to_high", "high_to_low"
        $jobPostSort = $request->get('job_post'); // e.g., "low_to_high", "high_to_low"

        $companyIds = ClaimCompany::pluck('company_id')->toArray();

        $subQuery = DB::table('company_reviews')
            ->selectRaw('company_id, AVG(rating) as avg_rating')
            ->groupBy('company_id');

        // // Filter employers
        // $employers = User::with([
        //     'company.membership',
        //     'company.logo',
        //     'company.reviews' => function ($query) {
        //         $query->selectRaw('company_id, AVG(CAST(rating AS DECIMAL(10,2))) as average_rating, COUNT(id) as total_reviews')
        //             ->groupBy('company_id');
        //     },
        //     'teamMembers', // Relation to fetch team members
        //     'membership.plan', // Relation to fetch plan details
        //     'company.claim',
        //     'company.jobs',
        //     'company.sector',
        //     'company.location',
        // ])
        //     ->where('role', 'employer')
        //     ->where('status', '!=', 'deleted')
        //     // new addition name filter top of page
        //     ->when($name, function ($query) use ($name) {
        //         $query->whereHas('company', function ($nameQuery) use ($name) {
        //             $nameQuery->where('company_name', 'LIKE', '%' . $name . '%');
        //         });
        //     })
        //     ->when($accountTypes, function ($query) use ($accountTypes) {
        //         $query->whereHas('company.membership', function ($membershipQuery) use ($accountTypes) {
        //             $membershipQuery->where('plan_id', $accountTypes); // Filter by specific plan_id
        //         });
        //     })
        //     ->when(!empty($locations), function ($query) use ($locations) {
        //         $query->whereHas('company', function ($companyQuery) use ($locations) {
        //             $companyQuery->whereIn('company_location', $locations); // Filter by location
        //         });
        //     })
        //     ->when($status, function ($query) use ($status) {
        //         if ($status === 'unclaimed') {
        //             // Filter companies that do not have a claim record
        //             $query->whereDoesntHave('company.claim');
        //         } else {
        //             // Filter companies with claim records that match the status
        //             $query->whereHas('company.claim', function ($claimQuery) use ($status) {
        //                 $claimQuery->where('status', $status);
        //             });
        //         }
        //     })
        //     // not clear till now
        //     ->when($activity === 'last_24_hours', function ($query) {
        //         $query->where('last_login', '>=', now()->subDay()); // Filter by last 24 hours activity
        //     })
        //     // Updated rating filter logic
        //     ->when($ratingFilter, function ($query) use ($subQuery, $ratingFilter) {
        //         $query->whereHas('company', function ($companyQuery) use ($subQuery, $ratingFilter) {
        //             $companyQuery->joinSub($subQuery, 'ratings', function ($join) {
        //                 $join->on('ratings.company_id', '=', 'company.id');
        //             });
        //             $companyQuery->whereRaw(match ($ratingFilter) {
        //                 '1_and_above' => 'ratings.avg_rating >= 1',
        //                 '2_and_above' => 'ratings.avg_rating >= 2',
        //                 '3_and_above' => 'ratings.avg_rating >= 3',
        //                 '4_and_above' => 'ratings.avg_rating >= 4',
        //                 '5' => 'ratings.avg_rating = 5',
        //             });
        //         });
        //     })
        //     ->when($teamMemberSort, function ($query) use ($teamMemberSort) {
        //         $query->withCount('teamMembers as team_member_count');
        //         if ($teamMemberSort === 'low_to_high') {
        //             $query->orderBy('team_member_count', 'asc');
        //         } elseif ($teamMemberSort === 'high_to_low') {
        //             $query->orderBy('team_member_count', 'desc');
        //         }
        //     })
        //     ->when($jobPostSort, function ($query) use ($jobPostSort) {
        //         $query->whereHas('company', function ($companyQuery) use ($jobPostSort) {
        //             $companyQuery->withCount(['jobs as active_job_count' => function ($jobQuery) {
        //                 $jobQuery->where('job_status', 'active');
        //             }]);
        //             if ($jobPostSort === 'low_to_high') {
        //                 $companyQuery->orderBy('active_job_count', 'asc');
        //             } elseif ($jobPostSort === 'high_to_low') {
        //                 $companyQuery->orderBy('active_job_count', 'desc');
        //             }
        //         });
        //     })
        //     ->orderByDesc('id')
        //     ->paginate($pageSize);

        // newly added portion 
        
        $employers = User::with([
            'company' => function ($query) {
                $query->orderByDesc('created_at'); // ✅ Ensure latest company is fetched
            },
            'company.membership', // ✅ Fetch latest membership (handled by latestOfMany())
            'company.claim', // ✅ Fetch latest claim (handled by latestOfMany())
            'company.jobs' => function ($query) {
                $query->where('job_status', 'active')->orderByDesc('created_at'); // ✅ Fetch latest active jobs
            },
            'company.reviews' => function ($query) {
                $query->orderByDesc('created_at')->limit(1); // ✅ Fetch only the latest review
            }
        ])
            ->when($accountTypes, function ($query) use ($accountTypes) {
                $query->whereHas('company.membership', function ($membershipQuery) use ($accountTypes) {
                    $membershipQuery->whereIn('plan_id', (array) $accountTypes);
                });
            })
            ->when(!empty($locations), function ($query) use ($locations) {
                $query->whereHas('company', function ($companyQuery) use ($locations) {
                    $companyQuery->whereIn('company_location', $locations);
                });
            })
            ->when($status, function ($query) use ($status) {
                if ($status === 'unclaimed') {
                    $query->whereDoesntHave('company.claim');
                } else {
                    $query->whereHas('company.claim', function ($claimQuery) use ($status) {
                        $claimQuery->where('status', $status);
                    });
                }
            })
            ->when($activity === 'last_24_hours', function ($query) {
                $query->where('last_login', '>=', now()->subDay());
            })
            ->when($ratingFilter, function ($query) use ($subQuery, $ratingFilter) {
                $query->whereHas('company', function ($companyQuery) use ($subQuery, $ratingFilter) {
                    $companyQuery->joinSub($subQuery, 'ratings', function ($join) {
                        $join->on('ratings.company_id', '=', 'company.id');
                    });
                    $companyQuery->whereRaw(match ($ratingFilter) {
                        '1_and_above' => 'ratings.avg_rating >= 1',
                        '2_and_above' => 'ratings.avg_rating >= 2',
                        '3_and_above' => 'ratings.avg_rating >= 3',
                        '4_and_above' => 'ratings.avg_rating >= 4',
                        '5' => 'ratings.avg_rating = 5',
                    });
                });
            })
            ->where('role', 'employer')
            ->orderByDesc('id')
            ->paginate($pageSize);


        // Return results as resources
        return EmployerResource::collection($employers);
    }


    public function getEmployees(Request $request): AnonymousResourceCollection
    {
        $status = $request->get('status');
        $name = $request->get('name', '');
        $pageSize = $request->get('pageSize');

        $employees = (new User)
            ->with('company', 'company.logo', 'country', 'resumes', 'languages', 'work_experience', 'education', 'portfolio', 'profile')
            ->where('role', 'employee');
        // ->where(function (Builder $query) {
        //     $query->where('role', 'employee')
        //         ->orWhereNull('role');
        // });

        if ($status) {
            $employees->where('status', "active");
        }
        $employees->orderBy('id', 'desc');

        return EmployeeResource::collection($employees->paginate($pageSize));
    }

    public function searchCurrentPositions(Request $request)
    {
        try {
            $positions = User::where('current_position', 'LIKE', '%' . $request->search_term . '%')
                ->distinct()
                ->pluck('current_position');

            return response()->json([
                'success' => true,
                'message' => 'Positions fetched successfully.',
                'data' => $positions
            ], 200);
        } catch (\Exception $e) {
            // Catch any errors and return a failure response
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching positions.',
                'error' => $e->getMessage()
            ], 500); // 500 status code for server errors
        }
    }

    /* filter for employee */
    public function getEmployeesFilter(Request $request): AnonymousResourceCollection
    {


        $employees = (new User)
            ->with('company', 'company.logo', 'country', 'resumes', 'languages', 'work_experience', 'education', 'portfolio')
            ->where('role', 'employee');

        if ($request->current_position) {
            $employees->where('current_position', 'LIKE', '%' . $request->current_position . '%');
        }

        if ($request->job_status) {
            if (is_array($request->job_status)) {
                $employees->whereIn('job_status', $request->job_status);
            } else {
                $employees->where('job_status', $request->job_status);
            }
        }

        if ($request->available_resume_count) {
            $employees->where('available_resume_count', '>', 0);
        }

        // if ($request->location) {
        //     if (is_array($request->location)) {
        //         $employees->whereIn('cities', $request->location);
        //     } else {
        //         $employees->where('cities', $request->location);
        //     }
        // }

        // if ($request->location) {
        //     if (is_array($request->location)) {
        //         // First, try to filter based on the 'cities' column
        //         $employees->where(function ($query) use ($request) {
        //             $query->whereIn('cities', $request->location)
        //                   ->orWhereIn('where_location', $request->location);
        //         });
        //     } else {
        //         // For single location, first check 'cities', then 'where_location' if no match in 'cities'
        //         $employees->where(function ($query) use ($request) {
        //             $query->where('cities', $request->location)
        //                   ->orWhere('where_location', $request->location);
        //         });
        //     }
        // }

        if ($request->location) {
            if (is_array($request->location)) {
                $employees->whereIn('cities', $request->location);
                $employees->whereIn('countries', $request->location);
            } else {
                $employees->where('cities', $request->location);
                $employees->where('countries', $request->location);
            }
        }

        if ($request->country) {
            if (is_array($request->country)) {
                $employees->whereIn('countries', $request->country);
            } else {
                $employees->where('countries', $request->country);
            }
        }


        if ($request->experience) {

            $experienceRange = $request->experience;

            if (strpos($experienceRange, '-') !== false) {
                // Handle range input like '1-8'
                [$minExperience, $maxExperience] = explode('-', $experienceRange);
            } elseif ($experienceRange == "20") {
                // Handle input like '20+'
                $minExperience = intval($experienceRange); // 20
                $maxExperience = null; // There's no upper limit
            } else if ($experienceRange == "fresher") {
                $employees->where('years_of_experience', "fresher");
            } else {
                // Handle single value input 
                $minExperience = 0;
                $maxExperience = $experienceRange;
            }

            // If the input is '20+', we need to include both exact matches and ranges like "20+"
            if ($maxExperience === null) {
                $employees->where(function ($query) use ($minExperience) {
                    $query->whereRaw('CAST(SUBSTRING_INDEX(years_of_experience, "-", -1) AS UNSIGNED) >= ?', [$minExperience])
                        ->orWhere('years_of_experience', 'LIKE', '20+');
                });
            } else {
                // For range inputs like '1-8' or single values 
                $employees->whereRaw('CAST(SUBSTRING_INDEX(years_of_experience, "-", 1) AS UNSIGNED) <= ?', [$maxExperience])
                    ->whereRaw('CAST(SUBSTRING_INDEX(years_of_experience, "-", -1) AS UNSIGNED) >= ?', [$minExperience]);
            }
        }

        if ($request->salary && $request->currency) {
            // dd($request->salary[1]);
            $employees->where('currency', 'LIKE', "%($request->currency)%")
                ->whereBetween(DB::raw('CAST(current_salary AS UNSIGNED)'), [$request->salary[0], $request->salary[1]]);
        }

        if ($request->skills) {
            $skillInput = $request->skills;
            // Check if user input is an array
            if (is_array($skillInput)) {
                // Multiple skills check
                $employees->where(function ($query) use ($skillInput) {
                    foreach ($skillInput as $skill) {
                        $query->orWhereRaw("FIND_IN_SET(?, skills) > 0", [$skill]);
                    }
                });
            } else {
                // Single skill check
                $employees->whereRaw("FIND_IN_SET(?, skills) > 0", [$skillInput]);
            }
        }

        if ($request->sector) {
            if (is_array($request->sector)) {
                $employees->whereIn('sector', $request->sector);
            } else {
                $employees->where('sector', $request->sector);
            }
        }

        if ($request->industry) {
            if (is_array($request->industry)) {
                $employees->whereIn('industry', $request->industry);
            } else {
                $employees->where('industry', $request->industry);
            }
        }

        if ($request->activity) {
            $time = (int) $request->activity;
            if ($time == 24) {
                $employees->where('last_login', '>=', Carbon::now()->subHours($time));
            } else {
                $employees->where('last_login', '>=', Carbon::now()->subDays($time));
            }
        }

        $employees->where('status', 'active');

        $employees->orderBy('id', 'desc');

        return EmployeeResource::collection($employees->paginate(10));
    }

    /* EXPORT USER DATA */
    public function userExport(Request $request)
    {
        // Get parameters from request
        $userIds = $request->input('user_ids'); // Get user_id as an array, default to empty array
        $format = $request->input('format'); // Get format, default to 'csv'

        // Fetch users based on the user_ids provided and role
        if (!empty($userIds)) {
            // Fetch specified users with user_ids and role = 'employee'
            $users = User::whereIn('id', $userIds)
                ->where('role', 'employee')
                ->cursor(); // Use cursor for memory efficiency
        } else {
            // Fetch all users with role = 'employee'
            $users = User::where('role', 'employee')
                ->cursor(); // Use cursor for memory efficiency
        }

        if ($format === 'xls') {
            // Create a new Spreadsheet for XLS
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Define Excel column headers
            $sheet->setCellValue('A1', 'id');
            $sheet->setCellValue('B1', 'name');
            $sheet->setCellValue('C1', 'email');
            $sheet->setCellValue('D1', 'role');
            $sheet->setCellValue('E1', 'created_at');
            $sheet->setCellValue('F1', 'updated_at');

            // Write each user to the Excel sheet
            $row = 2; // Starting row for user data
            foreach ($users as $user) {
                $sheet->setCellValue('A' . $row, $user->id);
                $sheet->setCellValue('B' . $row, $user->name);
                $sheet->setCellValue('C' . $row, $user->email);
                $sheet->setCellValue('D' . $row, $user->role);
                $sheet->setCellValue('E' . $row, $user->created_at);
                $sheet->setCellValue('F' . $row, $user->updated_at);
                $row++;
            }

            // Define response headers for XLS
            $headers = [
                "Content-Type" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "Content-Disposition" => "attachment; filename=users.xlsx",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            ];

            // Write the file to output
            $writer = new Xlsx($spreadsheet);
            return response()->stream(function () use ($writer) {
                $writer->save('php://output');
            }, 200, $headers);
        } else {
            // Define the response headers for CSV
            $headers = [
                "Content-Type" => "text/csv",
                "Content-Disposition" => "attachment; filename=users.csv",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            ];

            // Callback function to handle the file creation and data output for CSV
            $callback = function () use ($users) {
                // Open output stream
                $file = fopen('php://output', 'w');

                // Define CSV column headers
                fputcsv($file, ['id', 'name', 'email', 'role', 'created_at', 'updated_at']);

                // Write each user to the CSV
                foreach ($users as $user) {
                    fputcsv($file, [
                        $user->id,
                        $user->name,
                        $user->email,
                        $user->role,
                        $user->created_at,
                        $user->updated_at
                    ]);
                }

                fclose($file);
            };

            // Return the CSV response
            return response()->stream($callback, 200, $headers);
        }
    }

    /* EXPORT EMPLOYER DATA */
    public function employerExport(Request $request)
    {
        // Get parameters from request
        $userIds = $request->input('user_ids'); // Get user_id as an array, default to empty array
        $format = $request->input('format'); // Get format, default to 'csv'

        // Fetch users based on the user_ids provided and role
        if (!empty($userIds)) {
            // Fetch specified users with user_ids and role = 'employee'
            $users = User::whereIn('id', $userIds)
                ->where('role', 'employer')
                ->cursor(); // Use cursor for memory efficiency
        } else {
            // Fetch all users with role = 'employee'
            $users = User::where('role', 'employer')
                ->cursor(); // Use cursor for memory efficiency
        }

        if ($format === 'xls') {
            // Create a new Spreadsheet for XLS
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Define Excel column headers
            $sheet->setCellValue('A1', 'id');
            $sheet->setCellValue('B1', 'name');
            $sheet->setCellValue('C1', 'email');
            $sheet->setCellValue('D1', 'role');
            $sheet->setCellValue('E1', 'created_at');
            $sheet->setCellValue('F1', 'updated_at');

            // Write each user to the Excel sheet
            $row = 2; // Starting row for user data
            foreach ($users as $user) {
                $sheet->setCellValue('A' . $row, $user->id);
                $sheet->setCellValue('B' . $row, $user->name);
                $sheet->setCellValue('C' . $row, $user->email);
                $sheet->setCellValue('D' . $row, $user->role);
                $sheet->setCellValue('E' . $row, $user->created_at);
                $sheet->setCellValue('F' . $row, $user->updated_at);
                $row++;
            }

            // Define response headers for XLS
            $headers = [
                "Content-Type" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "Content-Disposition" => "attachment; filename=employers.xlsx",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            ];

            // Write the file to output
            $writer = new Xlsx($spreadsheet);
            return response()->stream(function () use ($writer) {
                $writer->save('php://output');
            }, 200, $headers);
        } else {
            // Define the response headers for CSV
            $headers = [
                "Content-Type" => "text/csv",
                "Content-Disposition" => "attachment; filename=employers.csv",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            ];

            // Callback function to handle the file creation and data output for CSV
            $callback = function () use ($users) {
                // Open output stream
                $file = fopen('php://output', 'w');

                // Define CSV column headers
                fputcsv($file, ['id', 'name', 'email', 'role', 'created_at', 'updated_at']);

                // Write each user to the CSV
                foreach ($users as $user) {
                    fputcsv($file, [
                        $user->id,
                        $user->name,
                        $user->email,
                        $user->role,
                        $user->created_at,
                        $user->updated_at
                    ]);
                }

                fclose($file);
            };

            // Return the CSV response
            return response()->stream($callback, 200, $headers);
        }
    }

    public function showEmployee(Request $request, $id): JsonResponse
    {
        $employee = (new User)
            ->with('country', 'resumes', 'profile')
            ->where('id', $id)
            ->orWhere('slug', $id)
            ->where(function (Builder $query) {
                $query->where('role', 'employee')
                    ->orWhereNull('role');
            })->first();

        return $this->respondWithSuccess(new EmployeeResource($employee));
    }

    /*public function getAllUsers()
    {
        $users = DB::table('users')
            ->leftJoin('resumes', function ($join) {
                $join->on('users.id', '=', 'resumes.user_id')
                    ->where('resumes.default_resume', 1)
                    ->where('resumes.status', '=', 'active');
            })
            ->leftJoin('countries', 'users.where_currently_based', '=', 'countries.id')
            ->leftJoin('work_experience', function ($join) {
                $join->on('users.id', '=', 'work_experience.user_id')
                    ->where('work_experience.currently_work_here', 1);
            })
            ->where('users.role', '=', 'employee')
            ->where('users.status', '=', 'active')
            ->select('users.*', 'resumes.resume_pdf_path', 'countries.country_name', 'work_experience.company')
            ->orderBy('id', 'desc')
            ->get();

        return response()->json(['data' => $users, 'status' => true, 'message' => 'All users data fetched']);
    }*/

    public function getAllinactiveUsers()
    {
        $users = DB::table('users')
            ->leftJoin('resumes', function ($join) {
                $join->on('users.id', '=', 'resumes.user_id')
                    ->where('resumes.default_resume', 1)
                    ->where('resumes.status', '=', 'active');
            })
            ->leftJoin('countries', 'users.where_currently_based', '=', 'countries.id')
            ->leftJoin('work_experience', function ($join) {
                $join->on('users.id', '=', 'work_experience.user_id')
                    ->where('work_experience.currently_work_here', 1);
            })
            ->where('users.role', '=', 'employee')
            ->where('users.status', '=', 'deleted')
            ->select('users.*', 'resumes.resume_pdf_path', 'countries.country_name', 'work_experience.company')
            ->get();

        return response()->json(['data' => $users, 'status' => true, 'message' => 'All users data fetched']);
    }


    public function forgetpassword(Request $request)
    {
        // return $request->all();
        try {
            $user = User::where('email', $request->email)->get();
            if (count($user) > 0) {
                $token = Str::random(40);
                $domain = env('FRONTEND_URL', 'https://www.thetalentpoint.com');
                $url = $domain . '/auth/resetpassword?token=' . $token;
                $data['url'] = $url;
                $data['name'] = $request->name;
                $data['email'] = $request->email;
                $data['title'] = "Password reset";
                $data['body'] = "Please click on below link to reset your password";
                Mail::send('forgotpassword', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'])->subject($data['title']);
                });
                $datetime = Carbon::now()->format('Y-m-d H:i:s');


                PasswordReset::where('email', $request->email)->delete();

                PasswordReset::updateOrCreate(
                    ['email' => $request->email],
                    [
                        'email' => $request->email,
                        'token' => $token,
                        'created_at' => $datetime,
                        'updated_at' => $datetime // You can include this line if you want to update the 'updated_at' column
                    ]
                );

                return response()->json(['success' => true, 'msg' => 'Please check your email!']);
            } else {
                return response()->json(['success' => false, 'msg' => 'user not found!']);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function resetPassword(Request $request)
    {
        // try {
        $request->validate([
            'password' => 'required|string|min:8',
        ]);
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            return response()->json(['success' => true, 'msg' => 'User not found'], 404);
        }
        $user->password = Hash::make($request->password);
        $user->save();
        PasswordReset::where('email', $user->email)->delete();
        return response()->json(['success' => true, 'msg' => 'Password reset successful'], 200);
        //  } catch (\Exception $e) {
        //     return response()->json(['success' => true, 'msg' => 'Password reset failed'], 500);
        // }
    }

    // function updateSingleUserBackgroundBannerImage(Request $request)
    // {
    //     try {
    //         $user = User::find($request->id);

    //          if ($request->hasFile('user_background_banner_img')) {
    //             $image = $request->file('user_background_banner_img');
    //             $fileName = time() . '.' . $image->getClientOriginalExtension();
    //             $img = Image::make($image->getRealPath());
    //             $img->resize(1323, 227);
    //             $img->stream();
    //             Storage::disk('local')->put('public/images/userbannerImage'.'/'.$fileName, $img, 'public');
    //             $user->background_banner_image = $fileName;
    //         }
    //         $user->save();
    //         return response()->json(['message' => 'background banner image updated successfully', 'status' => true], 200);
    //     } catch (\Exception $e) {
    //         return response()->json(['message' => 'Failed to update background banner image', 'status' => false], 400);
    //     }
    // }

    public function updateSingleUserBackgroundBannerImage(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            $validator = Validator::make(
                request()->all(),
                [
                    'profile_image' => 'required|mimes:png,jpg,jpeg|max:1024',
                ],
                [
                    'profile_image.required' => 'Please select any banner image.',
                    'profile_image.mimes' => 'Only Allowed file type: png, jpg, jpeg.',
                    'profile_image.max' => 'Banner image not allowed greater than 1MB.',
                ]
            );
            if ($validator->fails()) {
                return response()->json([
                    'errors' => $validator->errors(),
                    'status' => false,
                ], 200);
            }
            if ($request->hasFile('profile_image')) {
                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $imagePath = $request->file('profile_image');
                // $imageName = $randomNumber . $imagePath->getClientOriginalName();
                // //$imagePath->move('public/images/userbannerImage', $imageName);
                // $file->move(public_path('images/userbannerImage'), $imageName);
                // $jobs->background_banner_image = $imageName;

                $image = $request->file('profile_image');
                $fileName = time() . '.' . $image->getClientOriginalExtension();
                $img = Image::make($image->getRealPath());
                $img->resize(1323, 227);
                $img->stream();
                Storage::disk('public')->put('images/userbannerImage/' . $fileName, $img);
                $user->background_banner_image = $fileName;
            }
            $user->save();
            return response()->json(['status' => true, 'message' => 'background banner Image updated successfully', 'data' => $user], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateUserCardDetails(Request $request, $user_id)
    {
        try {

            $user = User::findOrFail($user_id);
            $user->card_number = $request->input('card_number');
            $user->card_exp_month = $request->input('card_exp_month');
            $user->card_exp_year = $request->input('card_exp_year');
            $user->card_cvv = $request->input('card_cvv');
            $user->card_type = $request->input('card_type');
            $user->save();

            return response()->json([
                'status' => true,
                'message' => 'Card details updated successfully',
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function experiance()
    {
        try {
            $experiences = DB::table('default_experience')->where('status', 'active')->get();

            if ($experiences->isEmpty()) {
                return response()->json(['message' => 'No active experiences found.'], 404);
            } else {
                return response()->json(['status' => true, 'message' => 'experiences fetched', 'data' => $experiences], 200);
            }

            // return response()->json($experiences, 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateUserSectorandIndustry(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            $user->industry = $request->input('industry');
            $user->sector = $request->input('sector');
            $user->save();
            return response()->json([
                'status' => true,
                'data' => $user,
                'message' => 'User Industry & Sector updated successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function get_staff_member(Request $request)
    {
        try {
            $user = User::where('created_by_id', $request->id)
                ->where('status', 'active')
                ->where('role', 'staff')
                ->get();
            return response()->json([
                'status' => true,
                'data' => $user
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function get_single_staff_member(Request $request)
    {
        try {
            $user = User::select('id', 'email', 'name')->where('id', $request->id)->first();
            return response()->json([
                'status' => true,
                'data' => $user
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function update_single_staff_member(Request $request)
    {
        try {
            $user = User::find($request->id);
            $user->email = $request->email;
            $user->save();

            return response()->json([
                'status' => true,
                'data' => $user,
                'message' => 'Staff Updated Succesfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserAllTeamMembers(Request $request, $id)
    {
        try {
            $team_members = User::where('created_by_id', $id)->where('status', 'active')->where('role', 'staff')->get();
            if ($team_members) {
                return response()->json([
                    'status' => true,
                    'message' => 'Team member list',
                    'data' => $team_members,
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Team member not found',
                    'data' => '',
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleTeamMember(Request $request, $id)
    {
        try {
            $team_members = User::where('id', $id)->where('status', 'active')->where('role', 'staff')->first();
            if ($team_members) {
                return response()->json([
                    'status' => true,
                    'message' => 'Team member data',
                    'data' => $team_members,
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Team member data not found',
                    'data' => '',
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateTeamMember(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            $user->name = $request->input('team_member_name');
            $user->save();
            return response()->json([
                'status' => true,
                'data' => $user,
                'message' => 'Team Member updated successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteTeamMember($id)
    {
        try {
            $user = User::findOrFail($id);
            $user->status = 'deleted';
            $user->save();
            return response()->json(['message' => 'Team Member deleted successfully', 'status' => true]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function activateUser($id)
    {
        try {
            $user = User::findOrFail($id);
            $user->status = 'active';
            DB::table('education')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('portfolio')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('work_experience')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('employee_skills')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('applications')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('resumes')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('company')->where('user_id', $id)->update(['status' => 'active']);
            DB::table('jobs')->where('user_id', $id)->update(['job_status' => 'active']);
            $applicationUserIds = DB::table('applications')->where('jobpost_by_userId', $id)->pluck('user_id');
            $user->save();
            foreach ($applicationUserIds as $userId) {
                $user = User::find($userId);
                // Mail::to($user->email)->send('applicationdelete');
                $message = 'Dear ' . $user->name . ', Your job application has been Active due to user account Active.';

                // Mail::raw($message, function ($message) use ($user) {
                //     $message->to($user->email)->subject('Job Application Deleted');
                // });
            }
            return response()->json([
                'status' => true,
                'message' => 'User Active successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserDetailsByEmail(Request $request)
    {
        try {
            $user = User::where('email', $request->email)->first();
            if ($user) {
                return response()->json([
                    'status' => true,
                    'message' => 'User data fetch successfully',
                    'user' => $user
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'User data not fetch successfully',
                    'user' => ''
                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    private function generateUniqueSlug($name)
    {
        $slug = Str::slug($name);
        $count = 1;


        while (User::where('slug', $slug)->exists()) {
            $slug = Str::slug($name) . '_' . $count;
            $count++;
        }

        return $slug;
    }
}
