<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\Request;

class CompanySearchController extends Controller
{
    // public function companySearchSuggestion(Request $request)
    // {
    //     $query = $request->query('query'); // Retrieve the 'query' parameter from the URL

    //     if (!$query) {
    //         return response()->json([
    //             'status' => 'error',
    //             'message' => 'Query parameter is required',
    //             'data' => []
    //         ], 400); // HTTP 400 Bad Request
    //     }

    //     // Fetch company names that match the search query
    //     $companies = Company::where('company_slug', 'LIKE', '%' . $query . '%')
    //         ->limit(10) // Limit the number of suggestions
    //         ->pluck('company_name'); // Only get the names

    //     if ($companies->isEmpty()) {
    //         return response()->json([
    //             'status' => 'success',
    //             'message' => 'No companies found for the given query',
    //             'data' => []
    //         ], 200); // HTTP 200 OK
    //     }

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Companies retrieved successfully',
    //         'data' => $companies
    //     ], 200); // HTTP 200 OK
    // }

    // public function companySearchView(Request $request)
    // {
    //     $query = $request->query('query'); // Retrieve the query parameter

    //     if (!$query) {
    //         return response()->json([
    //             'status' => 'error',
    //             'message' => 'Query parameter is required',
    //             'data' => []
    //         ], 400); // Bad Request status code
    //     }

    //     // Fetch companies with their reviews
    //     $companies = Company::where('company_slug', 'LIKE', '%' . $query . '%')
    //         ->leftJoin('company_reviews', 'company.id', '=', 'company_reviews.company_id')
    //         ->select('company.id', 'company.company_logo', 'company.company_name', 'company.company_sector')
    //         ->selectRaw('COUNT(company_reviews.id) as total_reviews, ROUND(AVG(company_reviews.rating), 1) as average_rating')
    //         ->groupBy('company.id', 'company.company_logo', 'company.company_name', 'company.company_sector')
    //         ->get();

    //     if ($companies->isEmpty()) {
    //         return response()->json([
    //             'status' => 'success',
    //             'message' => 'No companies found for the given query',
    //             'data' => []
    //         ], 200); // OK status code
    //     }

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Companies retrieved successfully',
    //         'data' => $companies
    //     ], 200); // OK status code
    // }

    public function companySearch(Request $request)
    {
        $query = $request->query('query'); // Retrieve the 'query' parameter from the URL
        $type = $request->query('type', 'suggestion'); // Determine the request type (default is 'suggestion')

        if (!$query) {
            return response()->json([
                'status' => 'error',
                'message' => 'Query parameter is required',
                'data' => []
            ], 400); // HTTP 400 Bad Request
        }

        // If type is 'suggestion', fetch company names only
        if ($type === 'suggestion') {
            $companies = Company::where('company_slug', 'LIKE', '%' . $query . '%')
                ->limit(10)
                ->pluck('company_name');

            $message = $companies->isEmpty() ? 'No companies found for the given query' : 'Companies retrieved successfully';

            return response()->json([
                'status' => 'success',
                'message' => $message,
                'data' => $companies
            ], 200);
        }

        // If type is 'view', fetch detailed company info with reviews
        if ($type === 'view') {
            $companies = Company::where('company_slug', 'LIKE', '%' . $query . '%')
                ->leftJoin('company_reviews', 'company.id', '=', 'company_reviews.company_id')
                ->select('company.id', 'company.company_logo', 'company.company_name', 'company.company_sector')
                ->selectRaw('COUNT(company_reviews.id) as total_reviews, ROUND(AVG(company_reviews.rating), 1) as average_rating')
                ->groupBy('company.id', 'company.company_logo', 'company.company_name', 'company.company_sector')
                ->get();

            $message = $companies->isEmpty() ? 'No companies found for the given query' : 'Companies retrieved successfully';

            return response()->json([
                'status' => 'success',
                'message' => $message,
                'data' => $companies
            ], 200);
        }

        // If an invalid type is provided
        return response()->json([
            'status' => 'error',
            'message' => 'Invalid type parameter. Use "suggestion" or "view".',
            'data' => []
        ], 400);
    }
}
