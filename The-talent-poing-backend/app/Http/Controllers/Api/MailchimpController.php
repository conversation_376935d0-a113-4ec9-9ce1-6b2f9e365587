<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Spatie\Newsletter\Facades\Newsletter;
use App\Mail\SubscriptionConfirmation;
use Illuminate\Support\Facades\Mail;

use Illuminate\Http\Request;

class MailchimpController extends Controller
{
    public function subscribe(Request $request)
    {
        $email = $request->input('email');

        if (Newsletter::isSubscribed($email)) {
            return response()->json(['status' => false, 'message' => 'Email is already subscribed please choose different email.'], 200);
        } else {
            Newsletter::subscribe($request->email);
            // Mail::to($email)->send(new SubscriptionConfirmation($email));

            return response()->json(['status' => true, 'message' => 'Email is subscribed.'], 200);
        }
    }
}
