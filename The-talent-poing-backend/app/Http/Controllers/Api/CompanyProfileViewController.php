<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\CompanyProfileView;
use App\Classes\ErrorsClass;
use Carbon\Carbon;
use App\Models\Company;
//use JWTAuth;

class CompanyProfileViewController extends Controller
{
    public function getCompanyProfileViewUserViewCount(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;
            $views_count = CompanyProfileView::where('user_id', $user_id)->where('company_id', $company_id)->where('status', 'active')->count();
            return response()->json(['status' => true, 'user_views_count' => $views_count]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getCompanyProfileAllUserViewCount(Request $request)
    {
        try {
            $company_id = $request->company_id;
            $count = CompanyProfileView::where('company_id', $company_id)
                ->where('status', 'active')
                ->count();
            $currentDate = Carbon::now();
            $lastWeekStartDate = $currentDate->subWeek();
            $lastWeekEndDate = Carbon::now();
            $companyprofileViewCountslastweek = CompanyProfileView::whereBetween('created_at', [$lastWeekStartDate, $lastWeekEndDate])
                ->where('company_id', $company_id)
                ->where('status', 'active')
                ->count();
            $last_Week_Start_Date = Carbon::now()->subWeek();
            $lastTwoWeekStartDate = $currentDate->subWeek(1);
            $lastTwoWeekEndDate = $last_Week_Start_Date;
            $companyprofileViewCountslasttwoweek = CompanyProfileView::whereBetween('created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                ->where('company_id', $company_id)
                ->where('status', 'active')
                ->count();
            $Impressions_First_Week = $companyprofileViewCountslasttwoweek;
            $Impressions_Last_Week = $companyprofileViewCountslastweek;
            if ($Impressions_First_Week > 0 && $Impressions_Last_Week > 0) {
                $impression_count = (($Impressions_First_Week - $Impressions_Last_Week) / $Impressions_Last_Week) * 100;
                $companyprofileViewimpressionpercentage = number_format($impression_count, 2) . '%';
            } else {
                $companyprofileViewimpressionpercentage = '0%';
            }

            $company_profileView_Counts_lasttwoweek = $companyprofileViewCountslasttwoweek;
            $company_profileView_Counts_lastweek = $companyprofileViewCountslasttwoweek;
            $company_profileView_Impression_percentage = $companyprofileViewimpressionpercentage;

            return response()->json(['status' => true, 'company_profile_views_count' => $count, 'company_profileView_Counts_lasttwoweek' => $company_profileView_Counts_lasttwoweek, 'company_profileView_Counts_lastweek' => $company_profileView_Counts_lastweek, 'company_profileView_Impression_percentage' => $company_profileView_Impression_percentage]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function insertCompanyView(Request $request)
    {
        try {

            $getcompanyid = Company::select('id')->where('company_slug', $request->company_id)->first();


            if ($getcompanyid) {

                $companyProfileView = new CompanyProfileView;
                $companyProfileView->company_id = $getcompanyid->id;
                $companyProfileView->user_id = $request->user_id;
                $companyProfileView->status = 'active';
                $companyProfileView->save();


                return response()->json(['status' => true, 'message' => 'Company view inserted successfully']);
            } else {
                return response()->json(['status' => true, 'message' => 'Company view data missing']);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    // public function getPopularCompany()
    // {
    //     $popularCompanies = CompanyProfileView::select('company_id')
    //         ->selectRaw('COUNT(*) as view_count')
    //         ->where('status', 'active')
    //         ->groupBy('company_id')
    //         ->orderBy('view_count', 'desc')
    //         ->limit(9)
    //         ->with('company') // Eager load the related Company model if needed
    //         ->get();

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Popular companies retrieved successfully',
    //         'data' => $popularCompanies
    //     ]);
    // }
    // public function getPopularCompany()
    // {
    //     $popularCompanies = CompanyProfileView::select('company_id')
    //         ->selectRaw('COUNT(*) as view_count')
    //         ->where('status', 'active')
    //         ->groupBy('company_id')
    //         ->orderBy('view_count', 'desc')
    //         ->limit(9)
    //         ->with([
    //             'company',              // Load the related Company model
    //             'company.sector',       // Load the Sector model via the Company model
    //             'company.location',     // Load the Country model via the Company model
    //             'company.logo',         // Load the logo via the Company model
    //             'company.fk_logo_file_uuid',         // Load the logo via the Company model
    //         ])
    //         ->get();

    //     $data = $popularCompanies->map(function ($profileView) {
    //         $company = $profileView->company;

    //         return [
    //             'company_id' => $profileView->company_id,
    //             'view_count' => $profileView->view_count,
    //             'company' => [
    //                 'id' => $company->id,
    //                 'name' => $company->name,
    //                 'sector' => $company->sector->name ?? null,  // Sector name
    //                 'location' => $company->location->name ?? null,  // Location (country) name
    //                 'logo' => $company->logo->path ?? null,  // Assuming the logo has a 'path' field
    //             ],
    //         ];
    //     });

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Popular companies retrieved successfully',
    //         'data' => $data
    //     ]);
    // }
    // public function getPopularCompany()
    // {
    //     $popularCompanies = CompanyProfileView::select('company_id')
    //         ->selectRaw('COUNT(*) as view_count')
    //         ->where('status', 'active')
    //         ->groupBy('company_id')
    //         ->orderBy('view_count', 'desc')
    //         ->limit(9)
    //         ->with([
    //             'company',              // Load the related Company model
    //             'company.sector',       // Load the Sector model via the Company model
    //             'company.location',     // Load the Country model via the Company model
    //             'company.logo',         // Load the logo via the Company model
    //             'company.reviews'       // Load the reviews via the Company model
    //         ])
    //         ->get();

    //     $data = $popularCompanies->map(function ($profileView) {
    //         $company = $profileView->company;

    //         // Calculate total reviews and average rating
    //         $totalReviews = $company->reviews->count();
    //         $averageRating = $totalReviews > 0
    //             ? round($company->reviews->avg('rating'), 1)
    //             : 0;

    //         return [
    //             'company_id' => $profileView->company_id,
    //             'view_count' => $profileView->view_count,
    //             'company' => [
    //                 'id' => $company->id,
    //                 'name' => $company->company_name,
    //                 'sector' => $company->sector->sector_name ?? null,  // Sector name
    //                 'location' => $company->location->country_name ?? null,  // Location (country) name
    //                 'logo' => $company->logo->source ?? null,  // Assuming the logo has a 'path' field
    //                 'total_reviews' => $totalReviews, // Total number of reviews
    //                 'average_rating' => $averageRating // Average rating
    //             ],
    //         ];
    //     });

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Popular companies retrieved successfully',
    //         'data' => $data
    //     ]);
    // }
    public function getPopularCompany()
    {
        $popularCompanies = CompanyProfileView::select('company_id')
            ->selectRaw('COUNT(*) as view_count')
            ->where('status', 'active')
            ->groupBy('company_id')
            ->orderBy('view_count', 'desc')
            ->limit(9)
            ->with([
                'company',              // Load the related Company model
                'company.sector',       // Load the Sector model via the Company model
                'company.location',     // Load the Country model via the Company model
                'company.logo',         // Load the logo via the Company model
                'company.reviews'       // Load the reviews via the Company model
            ])
            ->get();

        $data = $popularCompanies->map(function ($profileView) {
            $company = $profileView->company;

            if (!$company) {
                return null; // Skip this record if no company is associated
            }

            // Calculate total reviews and average rating
            $totalReviews = $company->reviews ? $company->reviews->count() : 0;
            $averageRating = $totalReviews > 0
                ? round($company->reviews->avg('rating'), 1)
                : 0;

            return [
                'company_id' => $profileView->company_id,
                'view_count' => $profileView->view_count,
                'company' => [
                    'id' => $company->id,
                    'name' => $company->company_name,
                    'sector' => $company->sector->sector_name ?? null,  // Sector name
                    'location' => $company->location->country_name ?? null,  // Location (country) name
                    'logo' => $company->logo->source ?? null,  // Assuming the logo has a 'source' field
                    'total_reviews' => $totalReviews, // Total number of reviews
                    'average_rating' => $averageRating // Average rating
                ],
            ];
        })->filter(); // Remove any null results

        return response()->json([
            'status' => 'success',
            'message' => 'Popular companies retrieved successfully',
            'data' => $data->values() // Reset array keys
        ]);
    }
}
