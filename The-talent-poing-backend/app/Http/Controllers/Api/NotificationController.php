<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Src\AppFramework\ApiController;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Notification;
use App\Models\User;
use App\Classes\ErrorsClass;

//use App\Notifications\CustomNotification;
//use JWTAuth;
//use Notification;

class NotificationController extends ApiController
{
    // public function getNotifications(Request $request): JsonResponse
    // {
    //     try {
    //         $notifications = Notification::read($request->has('isRead') ? $request->isRead : null)->with('userBy')->get();
    //         return $this->respondWithSuccess($notifications);
    //     } catch (Exception $e) {
    //         return $this->respondError($e->getMessage());
    //     }
    // }
    public function getNotifications(Request $request)
    {
        try {
            $notifications = Notification::read($request->has('isRead') ? $request->isRead : null)->with('userBy')->get();
            return response()->json([
                'status' => true,
                'message' => 'Notifications retrieved successfully.',
                'data' => $notifications
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getReadNotifications(Request $request)
    {
        try {
            $notifications = Notification::where('notify_to', $request->notify_to)->where('is_read', '=', '1')->where('status', '!=', 'deleted')
                ->orderBy('id', 'desc')
                ->get();

            return response()->json([
                'status' => true,
                'message' => 'Notifications retrieved successfully.',
                'data' => $notifications
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function saveNotification($userId, $adminId, $notification, $link, $type)
    {
        try {
            $newNotification = new Notification();
            $newNotification->notify_by = $userId;
            $newNotification->notify_to = $adminId;
            $newNotification->notification = $notification;
            $newNotification->link = $link;
            $newNotification->notification_type = $type;
            $newNotification->status = 'active';
            $save_notification = $newNotification->save();
            if ($save_notification) {
                return response()->json([
                    'status' => true,
                    'message' => 'Notification saved successfully'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Notification not saved successfully'
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateReadUnreadNotificationStatus(Request $request)
    {
        try {
            $Input = [];
            $Input['is_read'] = '1';
            $update = Notification::where('notify_to', $request->notify_to)->update($Input);
            if ($update) {
                return response()->json([
                    'status' => true,
                    'message' => 'Notification status updated successfully.',
                    'data' => ''
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Notification status not updated successfully.',
                    'data' => ''
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updatenotificationstatus(Request $request)
    {
        try {
            $notifications = Notification::where('notify_to', $request->id)
                ->where('is_read', 0)
                ->update(['is_read' => 1]);

            return response()->json([
                'status' => true,
                'message' => 'Notification update successfully',
                'data' => ''

            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
