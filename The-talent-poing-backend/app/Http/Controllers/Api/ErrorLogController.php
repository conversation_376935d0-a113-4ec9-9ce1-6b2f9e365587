<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Models\User;
use App\Models\Errorlogs;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon; 
//use JWTFactory;
//use JWTAuth;
use Validator;
use Config;
use Log;
use Event;
use App\Events\UserRegistered;
use DB;
use Illuminate\Support\Str;
//use Tymon\JWTAuth\Exceptions\JWTException;
use App\Classes\ErrorsClass;
use Image;

class ErrorLogController extends Controller
{   
    public function getDbStructure(Request $request)
    {
        try {
     
            $databaseName = env('DB_DATABASE');
            // return $databaseName;
            $tablesQuery = "SELECT 
            'Table' AS type,
            c.table_name AS name,
            GROUP_CONCAT(column_name, ' ', data_type ORDER BY ordinal_position) AS structure,
            MAX(create_time) AS created_at
        FROM
            information_schema.columns c
        LEFT JOIN information_schema.tables t ON c.table_name = t.table_name
        WHERE
            c.table_schema = '$databaseName'
        GROUP BY
            c.table_name, create_time";

            $proceduresQuery = "SELECT 
                'Procedure' AS type,
                routine_name AS name,
                routine_definition AS structure,
                created AS created_at
            FROM
                information_schema.routines
            WHERE
                routine_schema = '$databaseName'";

            $triggersQuery = "SELECT 
                'Trigger' AS type,
                trigger_name AS name,
                action_statement AS structure,
                created AS created_at
            FROM
                information_schema.triggers
            WHERE
                trigger_schema = '$databaseName'";

            $tablesResult = DB::select($tablesQuery);
            $proceduresResult = DB::select($proceduresQuery);
            $triggersResult = DB::select($triggersQuery);

            $combinedResult = array_merge($tablesResult, $proceduresResult, $triggersResult);


            return response()->json(['status' => true, 'data' => $combinedResult]);
        } catch (\Exception $e) {
            \Log::error('Error in getDbStructure: ' . $e->getMessage());

            return response()->json(['status' => false, 'error' => 'An error occurred while fetching the database structure.', 'message' => $e->getMessage()]);
        }
    }
    public function uploadEnvFile(Request $request)
    {

        $request->validate([
            'envFile' => 'required|file|mimetypes:text/plain,text/env',
        ]);

        $envFile = $request->file('envFile');

        // Read the content of the uploaded file
        $fileContents = file_get_contents($envFile->getRealPath());

        // Save the entire content as .env, overwriting the existing file
        file_put_contents(base_path('.env'), $fileContents);
        return response()->json(['message' => 'Environment file replaced successfully'], 200);
    
    }
    public function downlaodenv(Request $request){
        $envContents = file_get_contents(base_path('.env'));
        $headers = [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => 'attachment; filename=".env"',
        ];
        return response($envContents, 200, $headers);
    }
    // public function errorLists()
    // {
    //     try{
    //         $errors = Errorlogs::where('status', '=', '1')->where('deleted', '=', '0')->orderby('id','DESC')->paginate(Config::get('constant.pagination'));
    //         return response()->json(['status'=>true,'message'=>'Errors detail','error'=>'','data'=>$errors], 200);
    //      } catch (\Exception $e) {
    //         throw new HttpException(500, $e->getMessage());
    //     }
    // }

    // public function errorSearch(Request $request) {
    //    try{
    //         $input = $request->all();
    //         $search_data = $input['keyword'];
    //         $errors = Errorlogs::where('status', '=', '1')
    //                 ->where('deleted', '=', '0')
    //                 ->where(
    //         function($query) use ($search_data){
    //             $query->where('error_message', 'LIKE', '%'.$search_data.'%');
    //             $query->orWhere('file_name', 'LIKE', '%'.$search_data.'%');
    //             $query->orWhere('operating_system', 'LIKE', '%'.$search_data.'%');
    //             $query->orWhere('browser', 'LIKE', '%'.$search_data.'%');
    //             $query->orWhere('created_at', 'LIKE', '%'.$search_data.'%');
    //         })
    //               ->orderBy('id','DESC')
    //               ->paginate(Config::get('constant.pagination'));
           
    //         return response()->json(['status'=>true,'message'=>'Errors detail','error'=>'','data'=>$errors], 200);
    //      } catch (\Exception $e) {
    //         throw new HttpException(500, $e->getMessage());
    //     }
    // }

    // public function geterrorDetails(Request $request, $id) {
    //   try{
    //     $isError = Errorlogs::find($id);
    //     if ($isError) {
    //         $error = Errorlogs::where('id', $id)->first()->toArray();
    //         return response()->json(['status'=>true,'message'=>'Error details','error'=>'','data'=>$error], 200);
    //         } else {
    //         return response()->json(['status'=>false,'message'=>'Error not found','error'=>'','data'=>''], 400);
    //         }
    //      } catch (\Exception $e) {
    //         throw new HttpException(500, $e->getMessage());
    //     }

    // }    

    public function errorLists()
    {
        try {
            $errors = Errorlogs::select('error_message', 'line_number', 'file_name') ->selectRaw('COUNT(*) as count')
            ->groupBy('error_message', 'line_number', 'file_name')
            ->get();
    
            return response()->json([
                'status' => true,
                'message' => 'Errors detail',
                'error' => '',
                'data' => $errors,
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    
    public function deleteerrorlog(Request $request)
    {
        try {
            $id = $request->id;

          $errorlog = ErrorLogs::find($id);
        
         if (!$errorlog) {
                return response()->json(['message' => 'Errorlog not found'], 404);
            }
    
            // Get the data of the errorlog
            $error_message = $errorlog->error_message;
            $line_number = $errorlog->line_number;
            $file_name = $errorlog->file_name;
    
            // Delete rows where the data matches
            ErrorLogs::Where(function ($query) use ($error_message, $line_number, $file_name) {
                    $query->where('error_message', $error_message)
                          ->where('line_number', $line_number)
                          ->where('file_name', $file_name);
                })
                ->delete();
    
            return response()->json([
                 'status'=>true,
                'message' => 'Deleted successfully',
                'data' =>$id
            
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function deleteAllErorlog(Request $request){
        try{
            ErrorLogs::truncate();

            return response()->json([
                'status' => true,
                'message' => 'All Error Logs deleted successfully',
                'data' => ''
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());

        }
    }
}