<?php

namespace App\Http\Controllers\Api;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Src\AppFramework\ApiController;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Sector;

class SectorController extends ApiController
{
    public function index(Request $request): JsonResponse
    {
        try {
            // $sectors = (new Sector)
            //     ->where('status', 'active')
            //     ->orderBy('sector_name', 'asc')
            //     ->get();
            // return $this->respondWithSuccess($sectors);
            $sectors = (new Sector)->orderBy('sector_name');
            if ($request->keywords) {
                $sectors->where('sector_name', 'like', '%' . $request->keywords . '%');
            }
            if ($request->order_by) {
                if ($request->order_by == 'asc') {
                    $sectors->orderBy('id', 'ASC');
                }
                $sectors->orderBy('id', 'DESC');
            }
            $sectors->where('status', 'active');
            if ($sectors->count() > 0) {
                return $this->respondWithSuccess($sectors->get());
            } else {
                return $this->respondNoContent();
            }
            /*countries*/
            // $countries = (new Country)->orderBy('country_name');
            // if ($request->keywords) {
            //     $countries->where('country_name', 'like', '%' . $request->keywords . '%');
            // }
            // if ($request->order_by) {
            //     if ($request->order_by == 'asc') {
            //         $countries->orderBy('id', 'ASC');
            //     }
            //     $countries->orderBy('id', 'DESC');
            // }
            // $countries->where('status', 'active');
            // if ($countries->count() > 0) {
            //     return $this->respondWithSuccess($countries->get());
            // } else {
            //     return $this->respondNoContent();
            // }
            /*end*/
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllSectors()
    {
        try {
            $sectors = Sector::where('status', 'active')->orderBy('sector_name', 'asc')->get();

            return response()->json([
                'success' => true,
                'sectors' => $sectors
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllSectorsForAdmin()
    {
        try{
            $sectors = Sector::where('status','active')->orderByDesc('id')->get();
            return response()->json([
                'success'=>true,
                'sectors'=>$sectors
            ],200);
        } catch(Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function searchAllSectors(Request $request)
    {
        try {
            $query = DB::table('sector');
            if($request->keywords){
                $query->where('sector_name', 'like', '%'.$request->keywords.'%');
            }
            if($request->order_by){
                if($request->order_by == 'asc'){
                    $query->orderBy('id', 'ASC');
                }
                $query->orderBy('id', 'DESC');
            }
            $query->where('status', '!=', 'deactive');
            $sectors = $query->get();
            if($sectors){
                return response()->json(['status' => true ,'message' => "search sectors data fetch successfully", 'data' => $sectors], 200);
            }else {
                return response()->json(['status' => false, 'message' => "No search sectors data found", 'data' => ""], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

     public function editAndSaveSector(Request $request)
    {
        try {

            if($request->id){

                $sector = Sector::findOrFail($request->id);
                $sector->sector_name = $request->sector_name;
                $sector->save();

                return response()->json([
                'status' => true,
                'message' => 'Sector updated successfully',
            ], 200);

            }else {

                $sector = new Sector;
                $sector->sector_name = $request->sector_name;
                $sector->status = $request->status ?? 'active';
                $sector->save();

                return response()->json([
                'status' => true,
                'message' => 'Sector added successfully',
                ], 200);
            }



        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteSector(Request $request)
    {
        try {

            $sector = Sector::find($request->id);
            $sector->status = 'deleted';
            $sector->save();

            return response()->json([
                'status' => true,
                'message' => 'Sector has been deleted successfully!',
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
