<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Payment;
use App\Models\Membership;
use App\Models\User;
use App\Classes\ErrorsClass;
use Stripe;
use Stripe\Exception\CardException;
use Carbon\Carbon;
use App\Models\Notification;
use App\Models\Company;
use App\Models\AdminSettings;
use App\Models\Job;

//use JWTAuth;

class PaymentController extends Controller
{

    public function getUserPaymentDetails($user_id)
    {
        try {
            $payment = Payment::where('user_id', $user_id)->where('status', '!=', 'deleted')->first();
            if ($payment) {
                return response()->json(['status' => true, 'data' => $payment], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Payment not found', 'data' => ''], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function savePayment(Request $request)
    {
        try {

            $check = AdminSettings::select('payment_mode', 'stripe_test_secret_key', 'stripe_live_secret_key')->first();

            if ($check->payment_mode == 'live') {

                Stripe\Stripe::setApiKey($check->stripe_live_secret_key);

            } else {

                Stripe\Stripe::setApiKey($check->stripe_test_secret_key);
            }


            $name = $request->name;
            $email = $request->email;
            $userid = $request->user_id;
            $company_id = $request->company_id;

            $membership = Membership::where(['user_id' => $userid, 'company_id' => $company_id])->with('plan')->latest()->first();

            $custom_plan_id = $membership->plan_id;
            $amount = $membership->plan->plan_amount * 100;

            $customer = Stripe\Customer::create(
                array(
                    'name' => $name,
                    'email' => $email
                )
            );
            $token = Stripe\Token::create([
                'card' => [
                    'name' => $name,
                    'number' => $request->card_number,
                    'exp_month' => $request->exp_month,
                    'exp_year' => $request->exp_year,
                    'cvc' => $request->cvc
                ],
            ]);

            $customer->sources->create(['source' => $token->id]);

            $charge = Stripe\Charge::create([
                'amount' => $amount,
                'currency' => $membership->plan->plan_currency,
                'customer' => $customer->id,
                'source' => $customer->default_source
            ]);


            $today = Carbon::now();

            if ($custom_plan_id == 2) {
                $validUpto = $today->addMonths(12);
            } else {
                $validUpto = $today->addMonths(18);
            }

            if ($charge['paid'] == 1) {

                $Input = [];
                $membershipData = [
                    'user_id' => $userid,
                    'company_id' => $company_id,
                ];

                $membershipUpdateData = [
                    'expire_at' => $validUpto,
                    'purchase_at' => $today,
                    'status' => 'active'
                ];

                $savemembershippayementid = Membership::updateOrInsert($membershipData, $membershipUpdateData);

                if ($savemembershippayementid) {
                    $Input['user_id'] = $userid;
                    $Input['company_id'] = $company_id;
                    $Input['plan_id'] = $custom_plan_id;
                    $Input['membership_id'] = $membership->id;
                    $Input['charge_id'] = $charge['id'];
                    $Input['amount'] = $charge['amount'];
                    $Input['currency'] = $charge['currency'];
                    $Input['payment_gateway'] = 'Stripe';
                    $Input['plan_type'] = $custom_plan_id == 2 ? '12 Month' : '18 Month';

                    $Input['charge_receipt_url'] = $charge['receipt_url'];
                    $Input['charge_created_date'] = $charge['created'];
                    $Input['expire_at'] = $validUpto;
                    $Input['purchase_at'] = Carbon::now();

                    $Input['status'] = 'active';

                    $savepayement = Payment::insert($Input);

                    Company::where('id', $request->company_id)
                        ->update([
                            'available_resume_count' => '100000'
                        ]);

                    Job::where('company_id', $request->company_id)->where('job_status', 'deactive')
                        ->update([
                            'job_status' => 'active'
                        ]);


                    if ($savepayement) {
                        $notification_text = 'Plan Payment completed successfully!';
                        $link = null;
                        $type = 'membership';
                        $admin_id = '1';
                        $result = app('App\Http\Controllers\Api\NotificationController')->saveNotification($request->user_id, $admin_id, $notification_text, $link, $type);
                        return response()->json(['status' => true, 'message' => 'Payment saved successfully'], 200);
                    } else {
                        return response()->json(['status' => false, 'message' => 'Payment not saved successfully'], 200);
                    }
                } else {
                    return response()->json(['status' => false, 'message' => 'Payment not saved successfully'], 200);
                }
            } else {
                return response()->json(['status' => false, 'message' => 'There has been error please try again'], 200);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Failed to save payment', 'status_code' => 500, 'error' => $e->getMessage()], 500);
        }
    }



    //  public function savePayment(Request $request) {
    //      try {
    //          Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
    //          $name = $request->name;
    // $email = $request->email;
    // $userid = $request->user_id;
    //          $company_id = $request->company_id;
    // $custom_plan_id = $request->custom_plan_id;
    // $amount = $request->amount;
    // $plan_type = $request->plan_type;
    // $customer = Stripe\Customer::create(array(
    // 	'name' => $name,
    // 	'email' => $email
    // ));
    //          $token = Stripe\Token::create([
    //              'card' => [
    //                  'name' => $name,
    //                  'number' => $request->card_number,
    //                  'exp_month' => $request->exp_month,
    //                  'exp_year' => $request->exp_year,
    //                  'cvc' => $request->cvc
    //              ],
    //          ]);
    // $card_response = Stripe\Customer::createSource(
    // 	$customer->id,
    // 	['source' => $token->id]
    // );
    // if($amount=='0' && $plan_type == 'Month'){
    // 	$plan_id = 'price_1NUnlNB91miOIWh526jjBA63';
    // }
    //          if($amount=='0' && $plan_type == 'Year'){
    // 	$plan_id = 'price_1NUnrDB91miOIWh5WSeYKv9C';
    // }
    // if($amount=='25' && $plan_type == 'Month'){
    // 	$plan_id = 'price_1NUnolB91miOIWh5KrVgULPQ';
    // }
    //          if($amount=='25' && $plan_type == 'Year'){
    // 	$plan_id = 'price_1NUnruB91miOIWh5NFMut5ey';
    // }
    // if($amount=='100' && $plan_type == 'Month'){
    // 	$plan_id = 'price_1NUnq5B91miOIWh5gRhA4BJO';
    // }
    //          if($amount=='100' && $plan_type == 'Year'){
    // 	$plan_id = 'price_1NUnslB91miOIWh5syYtDr8s';
    // }
    //          $subscription = Stripe\Subscription::create(array(
    //              "customer" => $customer->id,
    //              "items" => array(
    //                  array("plan" => $plan_id),
    //              )
    //          ));
    //          if($subscription['status'] == 'active') {
    //              $Input =  [];
    //              $membership_data = Membership::where('user_id', $userid)->where('status', 'active')->first();
    //              if(!$membership_data){
    //                  $InputMembership = [];
    //                  $InputMembership['user_id'] = $userid;
    //                  $InputMembership['company_id'] = $company_id;
    //                  $InputMembership['plan_id'] = $custom_plan_id;
    //                  $InputMembership['expire_at'] = trim($subscription['current_period_end']);
    //                  $InputMembership['purchase_at'] = trim($subscription['current_period_start']);
    //                  $InputMembership['status'] = 'active';
    //                  $savemembershippayementid = Membership::insertGetId($InputMembership);
    //              } else {
    //                  $savemembershippayementid = $membership_data->id;
    //              }
    //              if($savemembershippayementid){
    //                  $Input['user_id'] = $userid;
    //                  $Input['company_id'] = $company_id;
    //                  $Input['plan_id'] = $custom_plan_id;
    //                  $Input['membership_id'] = $savemembershippayementid;
    //                  $Input['customer_id'] = trim($subscription['customer']);
    //                  $Input['transaction_id'] = null;
    //                  $Input['subscription_id'] = trim($subscription['id']);
    //                  $Input['stripe_plan_id'] = trim($subscription['plan']['id']);
    //                  $Input['stripe_plan_product_id'] = trim($subscription['plan']['product']);
    //                  $Input['card_id'] = trim($card_response['id']);
    //                  $Input['customer_email'] = $email;
    //                  $Input['subscription_status'] = trim($subscription['status']);
    //                  $Input['current_period_start'] = trim($subscription['current_period_start']);
    //                  $Input['current_period_end'] = trim($subscription['current_period_end']);
    //                  $Input['card_holder_name'] = trim($name);
    //                  $Input['card_number'] = trim($request->card_number);
    //                  $Input['card_exp_month'] = trim($request->exp_month);
    //                  $Input['card_exp_year'] = trim($request->exp_year);
    //                  $Input['card_cvv'] = trim($request->cvc);
    //                  $Input['card_type'] = trim($card_response['brand']);
    //                  $Input['amount'] = trim($subscription['plan']['amount']);
    //                  $Input['currency'] = trim($subscription['currency']);
    //                  $Input['payment_gateway'] = 'Stripe';
    //                  $Input['plan_type'] = $plan_type;
    //                  $Input['status'] = 'active';
    //                  $savepayement = Payment::insert($Input);
    //                  if($savepayement){
    //                      $InputUserCardDetails = [];
    //                      $InputUserCardDetails['card_number'] = trim($request->card_number);
    //                      $InputUserCardDetails['card_exp_month'] = trim($request->exp_month);
    //                      $InputUserCardDetails['card_exp_year'] = trim($request->exp_year);
    //                      $InputUserCardDetails['card_cvv'] = trim($request->cvc);
    //                      $InputUserCardDetails['card_type'] = trim($card_response['brand']);
    //                      $saveusercarddetails = User::where('id', $userid)->update($InputUserCardDetails);
    //                      $notification_text = 'Plan Payment completed successfully!';
    //                      $link = null;
    //                      $type = 'membership';
    //                      $admin_id = '1';
    //                      $result = app('App\Http\Controllers\Api\NotificationController')->saveNotification($request->user_id, $admin_id,  $notification_text, $link, $type);
    //                      return response()->json(['status' => true, 'message' => 'Payment saved successfully'], 200);
    //                  } else {
    //                      return response()->json(['status' => false, 'message' => 'Payment not saved successfully'], 200);
    //                  }
    //              } else {
    //                  return response()->json(['status' => false, 'message' => 'Payment not saved successfully'], 200);
    //              }
    //          } else {
    //              return response()->json(['status' => false, 'message' => 'There has been error please try again'], 200);
    //          }
    //      } catch (\Exception $e) {
    //          return response()->json(['status' => false, 'message' => 'Failed to save payment', 'status_code' => 500, 'error' => $e->getMessage()], 500);
    //      }
    //  }

    public function getUserAllPaymentDetails(Request $request, $user_id)
    {
        try {
            //$strtotime_start_date = strtotime($request->filterStartDate);
            //$strtotime_end_date = strtotime($request->filterEndDate);
            $strtotime_start_date = $request->filterStartDate;
            $strtotime_end_date = $request->filterEndDate;
            if ($request->filterStartDate && $request->filterEndDate) {
                $payment = Payment::whereBetween('purchase_at', [$strtotime_start_date, $strtotime_end_date])->where('user_id', $user_id)->where('status', '!=', 'deleted')->get();
                $payment_amount = 0;
                foreach ($payment as $payment_data) {
                    $payment_amount += $payment_data->amount;
                }
                if ($payment) {
                    return response()->json(['status' => true, 'data' => $payment, 'total_amount' => $payment_amount], 200);
                } else {
                    return response()->json(['status' => false, 'message' => 'Payment not found', 'data' => ''], 200);
                }
            } else {
                $payment = Payment::where('user_id', $user_id)->where('status', '!=', 'deleted')->get();
                $payment_amount = 0;
                foreach ($payment as $payment_data) {
                    $payment_amount += $payment_data->amount;
                }
                if ($payment) {
                    return response()->json(['status' => true, 'data' => $payment, 'total_amount' => $payment_amount], 200);
                } else {
                    return response()->json(['status' => false, 'message' => 'Payment not found', 'data' => ''], 200);
                }
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getStripePlanDetails(Request $request)
    {
        try {
            $stripe_plan_id = $request->stripe_plan_id;
            Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
            $plan = Stripe\Plan::retrieve($stripe_plan_id);
            if ($plan) {
                return response()->json(['status' => true, 'data' => $plan], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Payment not found', 'data' => ''], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getLastPaymentdetails(Request $request)
    {
        try {
            $check = AdminSettings::select('payment_mode', 'stripe_test_secret_key', 'stripe_live_secret_key')->first();
            if ($check->payment_mode == 'live') {
                Stripe\Stripe::setApiKey($check->stripe_live_secret_key);
            } else {
                Stripe\Stripe::setApiKey($check->stripe_test_secret_key);
            }
            $user_id = $request->user_id;
            $payment = Payment::where('user_id', $user_id)->where('status', '!=', 'deleted')->orderBy('id', 'desc')->first();
            // $product_data = Stripe\Product::retrieve($payment->stripe_plan_product_id,[]);
            if ($payment->plan_id == '1') {
                $payment['plan_name'] = 'USD 0 / Month';
            } elseif ($payment->plan_id == '2') {
                $payment['plan_name'] = 'USD 2500 /12 Month';
            } elseif ($payment->plan_id == '3') {
                $payment['plan_name'] = 'USD 3500 /18 Month';
            }

            if ($payment) {
                return response()->json(['status' => true, 'data' => $payment], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Payment not found', 'data' => ''], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
