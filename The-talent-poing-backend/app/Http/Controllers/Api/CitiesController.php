<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Models\Country;
use App\Models\Cities;
use App\Models\Job;

//use JWTAuth;
use DB;

class CitiesController extends Controller
{
    use ApiResponseHelpers;

    public function getAllCities()
    {
        try {
            $getallcities = Cities::where('status', 'active')->orderBy('city_name', 'asc')->get();
            if ($getallcities) {
                return response()->json([
                    'status' => true,
                    'data' => $getallcities,
                    'message' => 'cities listing successfully!'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'user' => '',
                    'message' => 'cities listing not found successfully!'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleCountryAllCities($country_id)
    {
        try {
            $getallcities = Cities::where('country_id', $country_id)->where('status', 'active')->orderBy('city_name', 'asc')->get();
            if ($getallcities) {
                return response()->json([
                    'status' => true,
                    'data' => $getallcities,
                    'message' => 'cities listing successfully!'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'data' => '',
                    'message' => 'cities listing not found successfully!'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllCitiesByCountryName($country_name)
    {
        try {
            $getallcities = (new Cities)->where('country_name', $country_name)->where('status', 'active')->orderBy('city_name', 'asc')->get();
            if ($getallcities) {
                return response()->json([
                    'status' => true,
                    'data' => $getallcities,
                    'message' => 'cities listing successfully!'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'data' => '',
                    'message' => 'cities listing not found successfully!'
                ]);
            }
            // if ($getallcities) {
            //     return $this->respondWithSuccess($getallcities->get());
            // } else {
            //     return $this->respondNoContent();
            // }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllCitiesForAdmin()
    {
        try {
            $getallcities = Cities::select('cities.*')->join('countries', 'countries.id', '=','cities.country_id')->where('countries.status', '=', 'active')->where('cities.status', '!=', 'deleted')->orderByDesc('cities.id')->get();
            if ($getallcities) {
                return response()->json([
                    'status' => true,
                    'data' => $getallcities,
                    'message' => 'cities listing successfully!'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'user' => '',
                    'message' => 'cities listing not found successfully!'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function searchAllCities(Request $request)
    {
        try {
            $query = DB::table('cities');
            if ($request->keywords) {
                $query->where('city_name', 'like', '%' . $request->keywords . '%');
            }
            if ($request->order_by) {
                if ($request->order_by == 'asc') {
                    $query->orderBy('id', 'ASC');
                }
                $query->orderBy('id', 'DESC');
            }
            $query->where('status', '!=', 'inactive');
            $continent = $query->get();
            if ($continent) {
                return response()->json(['status' => true, 'message' => "search cities data fetch successfully", 'data' => $continent], 200);
            } else {
                return response()->json(['status' => false, 'message' => "No cities country data found", 'data' => ""], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function editAndSaveCity(Request $request)
    {
        try {
            $country_data = Country::where('id', $request->country_id)->where('status', 'active')->first();
            if ($request->id) {
                $cities = Cities::find($request->id);
                $cities->city_name = $request->city_name;
                $cities->country_id = $request->country_id;
                $cities->country_name = $country_data->country_name;
                $cities->save();
                return response()->json([
                    'status' => true,
                    'message' => 'cities updated successfully',
                ], 200);
            } else {
                $cities = new Cities;
                $cities->city_name = $request->city_name;
                $cities->country_id = $request->country_id;
                $cities->country_name = $country_data->country_name;
                $cities->save();
                return response()->json([
                    'status' => true,
                    'message' => 'Location added successfully',
                ], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteCity(Request $request)
    {
        try {
            $cities = Cities::find($request->id);
            $cities->status = 'deleted';
            $cities->save();
            return response()->json([
                'status' => true,
                'message' => 'cities has been deleted successfully!',
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleCityByName(Request $request): JsonResponse
    {
        try {
            $cityName = $request->get('city_name');
            $city_data = (new Cities)->where('city_name', 'like', '%' . $cityName . '%')->where('status', 'active');
            if ($city_data) {
                return $this->respondWithSuccess($city_data->first());
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function updateCityStatus(Request $request)
    {
        try {
            $cities = Cities::find($request->cityId);
            if($request->status == 'false'){
                $status = 'inactive';
            } else {
                $status = 'active';
            }
            $cities->status = $status;
            $cities->save();
            return response()->json([
                'status' => true,
                'message' => 'city status has been updated successfully!',
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
