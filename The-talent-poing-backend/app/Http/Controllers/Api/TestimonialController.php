<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Testimonial;

class TestimonialController extends Controller
{
    public function index()
    {
        $testimonials = Testimonial::all();
        // dd($testimonials);
        return response()->json($testimonials);
    }

    //createOrUpdate a new testimonial
    public function createOrUpdate(Request $request)
    {
        // Validation
        $request->validate([
            'user_name' => 'nullable|string|max:255',
            'user_profile_photo' => 'nullable|image|max:5120', // Validate image file
            'position' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'review_comment' => 'nullable|string',
        ]);

        // Check if we are updating or creating a testimonial
        if ($request->id) {
            $id = $request->id;
            // Update existing testimonial
            $testimonial = Testimonial::find($id);

            if (!$testimonial) {
                return response()->json(['message' => 'Testimonial not found'], 404);
            }

            // Check if a new image has been uploaded
            if ($request->hasFile('user_profile_photo')) {
                // Generate a new image name
                $randomNumber = mt_rand(1000000000, 9999999999);
                $imagePath = $request->file('user_profile_photo');
            
                // Create the image name with the random number and original name
                $imageName = $randomNumber . '_' . time() . '_' . $imagePath->getClientOriginalName(); // Added timestamp for uniqueness
            
                // Store the image in the specified path
                $path = $imagePath->storeAs('public/images/testimonal', $imageName);
               
            
                // Update the model with the image path (store the relative path for the database)
                $testimonial->user_profile_photo = $imageName; // Update with the relative path
            }
            
            // Update other fields
            $testimonial->user_name = $request->input('user_name', $testimonial->user_name);
            $testimonial->position = $request->input('position', $testimonial->position);
            $testimonial->company_name = $request->input('company_name', $testimonial->company_name);
            $testimonial->review_comment = $request->input('review_comment', $testimonial->review_comment);
            $testimonial->save();

            return response()->json([
                'message' => 'Testimonial updated successfully.',
                'testimonial' => $testimonial
            ],200);
        } else {
            // Create a new testimonial
            $data = $request->all();

            // Check if a new image has been uploaded
            if ($request->hasFile('user_profile_photo')) {
                // Generate a new image name
                $randomNumber = mt_rand(1000000000, 9999999999);
                $imagePath = $request->file('user_profile_photo');
            
                // Create the image name with the random number and original name
                $imageName = $randomNumber . '_' . time() . '_' . $imagePath->getClientOriginalName(); // Added timestamp for uniqueness
            
                // Store the image in the specified path
                $path = $imagePath->storeAs('public/images/testimonal', $imageName);
               
            
                // Update the model with the image path (store the relative path for the database)
                $data['user_profile_photo'] = $imageName; // Update with the relative path
            }


            $testimonial = Testimonial::create($data);

        
            return response()->json([
                'message' => 'Testimonial created successfully.',
                'testimonial' => $testimonial
            ], 200);
        }
    }


    // Display a specific testimonial
    public function show($id)
    {
        $testimonial = Testimonial::find($id);

        if (!$testimonial) {
            return response()->json(['message' => 'Testimonial not found'], 404);
        }

        return response()->json($testimonial);
    }

   

    // Delete a testimonial
    public function destroy($id)
    {
        $testimonial = Testimonial::find($id);

        if (!$testimonial) {
            return response()->json(['message' => 'Testimonial not found'], 404);
        }

        $testimonial->delete();

        return response()->json(['message' => 'Testimonial deleted']);
    }
}
