<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Src\FileManagement\Infrastructure\Resources\FileResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Models\Applications;
use App\Models\Resumes;
use App\Classes\ErrorsClass;
//use JWTAuth;
use App\Models\SavedJobs;
use App\Models\User;
use App\Models\Job;
use Carbon\Carbon;

class ApplicationsController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getAllApplications($id)
    {
        try {
            // $applications = Applications::where('status', 'active')->orderBy('id', 'DESC')->paginate(10);

            $applications = Applications::where('applications.status', 'active')
                ->orderBy('applications.id', 'DESC')->where('applications.user_id', $id)
                ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                ->join('users', 'applications.user_id', '=', 'users.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->select('company.company_name as company_name', 'jobs.type_of_position', 'applications.apply_status', 'countries.country_name as country_name', 'company.id as companyId', 'jobs.job_title', 'jobs.job_slug', 'company.company_slug', 'applications.created_at')
                ->paginate(10);
            return response()->json([
                'message' => 'Applications retrieved successfully.',
                'status' => true,
                'data' => $applications
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllJobApplications(Request $request)
    {
        try {
            // $applications = Applications::where('status', 'active')->orderBy('id', 'DESC')->paginate(10);

            $user_id = $request->user_id;
            $company_id = $request->company_id;
            $role = $request->role;
            $job_id = 0;
            if($request->job_id !=0) {
                $job_id = $request->job_id;
            };
            if ($role == 'employer') {

                $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.job_status', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'company.company_slug', 'jobs.job_slug', 'jobs.job_title', 'job_posted_by_user.name as job_posted_by_name', 'applicant_id', 'resumes_viewed_user.name as viewed_user_name', 'resumes_viewed_user.id as viewed_user_id');
                $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                $query->join('users', 'applications.user_id', '=', 'users.id');
                $query->join('company', 'jobs.company_id', '=', 'company.id');
                $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                });

                $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');

                $query->where('jobs.user_id', $user_id);
                if ($request->hiring_selected_status) {
                    if ($request->hiring_selected_status == 'InstantApply') {
                        $query->where('applications.instant_apply', '1');
                    } else if($request->hiring_selected_status == 'All'){
                        $query->where('applications.instant_apply', '0');
                    } else {
                        $query->where('applications.hiring_status', $request->hiring_selected_status);
                        $query->where('applications.instant_apply', '0');
                    }
                } 
                if($job_id !=0 ){
                    $query->where('applications.job_id', $job_id);
                }
                $query->where('applications.status', 'active');
                $query->orderBy('applications.id', 'DESC');
                $applications = $query->get();
                foreach ($applications as $item) {
                    $user = User::with('profile')->find($item->user_id);
                    if($user->profile){
                        $profile = new FileResource($user->profile);
                        $item->profile = $profile;
                    } else {
                        $item->profile = '';
                    }
                }
                $hiring_status_all_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    // ->where('jobs.user_id', $user_id)
                    ->when($job_id != 0, function ($query) use ($job_id) {
                        return $query->where('applications.job_id', $job_id);
                    }, function ($query) use ($user_id) {
                        return $query->where('jobs.user_id', $user_id);
                    })
                    ->where('applications.instant_apply', '0')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();

                $hiring_status_yes_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    // ->where('jobs.user_id', $user_id)
                    ->when($job_id != 0, function ($query) use ($job_id) {
                        return $query->where('applications.job_id', $job_id);
                    }, function ($query) use ($user_id) {
                        return $query->where('jobs.user_id', $user_id);
                    })
                    ->where('applications.instant_apply', '0')
                    ->where('applications.hiring_status', 'Yes')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                $hiring_status_no_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    // ->where('jobs.user_id', $user_id)
                    ->when($job_id != 0, function ($query) use ($job_id) {
                        return $query->where('applications.job_id', $job_id);
                    }, function ($query) use ($user_id) {
                        return $query->where('jobs.user_id', $user_id);
                    })
                    ->where('applications.instant_apply', '0')
                    ->where('applications.hiring_status', 'No')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                $hiring_status_maybe_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    // ->where('jobs.user_id', $user_id)
                    ->when($job_id != 0, function ($query) use ($job_id) {
                        return $query->where('applications.job_id', $job_id);
                    }, function ($query) use ($user_id) {
                        return $query->where('jobs.user_id', $user_id);
                    })
                    ->where('applications.instant_apply', '0')
                    ->where('applications.hiring_status', 'Maybe')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                $hiring_status_instant_apply_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    // ->where('jobs.user_id', $user_id)
                    ->when($job_id != 0, function ($query) use ($job_id) {
                        return $query->where('applications.job_id', $job_id);
                    }, function ($query) use ($user_id) {
                        return $query->where('jobs.user_id', $user_id);
                    })
                    ->where('applications.instant_apply', '1')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                return response()->json([
                    'message' => 'Applications retrieved successfully.',
                    'status' => true,
                    'data' => $applications,
                    'hiring_status_all_count' => $hiring_status_all_count,
                    'hiring_status_yes_count' => $hiring_status_yes_count,
                    'hiring_status_no_count' => $hiring_status_no_count,
                    'hiring_status_maybe_count' => $hiring_status_maybe_count,
                    'hiring_status_instant_apply_count' => $hiring_status_instant_apply_count
                ], 200);

            }

            if ($role == 'staff') {


                $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.job_status', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'company.company_slug', 'jobs.job_slug', 'jobs.job_title', 'job_posted_by_user.name as job_posted_by_name', 'applicant_id', 'resumes_viewed_user.name as viewed_user_name', 'resumes_viewed_user.id as viewed_user_id');
                $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                $query->join('users', 'applications.user_id', '=', 'users.id');
                $query->join('company', 'jobs.company_id', '=', 'company.id');
                $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                });

                $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                $query->where('jobs.user_id', $user_id);
                if ($request->hiring_selected_status) {
                    if ($request->hiring_selected_status == 'InstantApply') {
                        $query->where('applications.instant_apply', '1');
                    } else {
                        $query->where('applications.hiring_status', $request->hiring_selected_status);
                        $query->where('applications.instant_apply', '0');
                    }
                } else {
                    //$query->where('applications.instant_apply', '0');
                    //$query->where('applications.hiring_status', 'Maybe');
                }
                $query->where('applications.status', 'active');
                $query->orderBy('applications.id', 'DESC');
                $applications = $query->get();
                $hiring_status_yes_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.instant_apply', '0')
                    ->where('applications.hiring_status', 'Yes')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                $hiring_status_no_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.instant_apply', '0')
                    ->where('applications.hiring_status', 'No')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                $hiring_status_maybe_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.instant_apply', '0')
                    ->where('applications.hiring_status', 'Maybe')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                $hiring_status_instant_apply_count = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.current_position as candidate_current_position', 'users.profile_image', 'countries.country_name as country_name', 'jobs.job_title')
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.instant_apply', '1')
                    ->where('applications.status', 'active')
                    ->orderBy('applications.id', 'DESC')
                    ->count();
                return response()->json([
                    'message' => 'Applications retrieved successfully.',
                    'status' => true,
                    'data' => $applications,
                    'hiring_status_yes_count' => $hiring_status_yes_count,
                    'hiring_status_no_count' => $hiring_status_no_count,
                    'hiring_status_maybe_count' => $hiring_status_maybe_count,
                    'hiring_status_instant_apply_count' => $hiring_status_instant_apply_count
                ], 200);

            }



        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getSingleJobApplications(Request $request)
    {
        try {
            // $applications = Applications::where('status', 'active')->orderBy('id', 'DESC')->paginate(10);
            $candidate_id = $request->candidate_id;
            $applicants_id = $request->applicants_id;
            $query = Applications::select('applications.id as applicants_id', 'applications.*', 'company.company_name as company_name', 'company.id as job_company_id', 'users.*', 'countries.country_name as country_name', 'jobs.job_title', 'company_slug');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'users.where_currently_based', '=', 'countries.id');
            $query->where('applications.id', $applicants_id);
            $query->where('users.id', $candidate_id);
            $query->where('applications.status', 'active');
            $applications = $query->first();
            return response()->json([
                'message' => 'Applications retrieved successfully.',
                'status' => true,
                'data' => $applications,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error occurred while retrieving applications.',
                'status' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function createApplication(Request $request)
    {
        try {

            $user_id = $request->input('user_id');
            $job_id = $request->input('job_id');

            // Check if the user has already applied for the same job_id
            $existingApplication = Applications::where('user_id', $user_id)
                ->where('job_id', $job_id)
                ->where('status', '!=', 'deleted')
                ->first();

            if ($existingApplication) {
                return response()->json([
                    'message' => 'You have already applied for this job.',
                    'status' => false
                ], 400);
            }
            // Create the new application

            $resumegetandcheck = Resumes::select('resume_pdf_path')->where('user_id', $user_id)->where('status', 'active')->where('default_resume', 1)->first();

            $applications = new Applications;
            $applications->user_id = $user_id;
            $applications->job_id = $job_id;
            $applications->company_id = $request->input('company_id');
            $applications->jobpost_by_userid = $request->input('jobpost_by_userid');
            $applications->resume_path = $request->input('resume_path');
            $applications->instant_apply = $request->input('instant_apply');


            if ($request->hasFile('cover_letter')) {
                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $imagePath = $request->file('cover_letter');
                // $imageName = $randomNumber . $imagePath->getClientOriginalName();
                // $path = $imagePath->storeAs('public/images/employee/cover_letter', $imageName);
                // $applications->cover_letter = $imageName;

                $file = $request->file('cover_letter');
                $request->validate([
                    'cover_letter' => 'required|mimes:pdf,xlxs,xlx,docx,doc|max:2048',
                ]);
                $randomNumber = mt_rand(1000000000, 9999999999);
                $fileName = $randomNumber . $file->getClientOriginalName();
                $filePath = 'images/employee/cover_letter/' . $fileName;
                $path = Storage::disk('public')->put($filePath, file_get_contents($request->cover_letter));
                $path = Storage::disk('public')->url($path);
                $applications->cover_letter = $fileName;
            }


            if ($request->hasFile('resume_path')) {
                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $filePath = $request->file('resume_path');
                // $fileName = $randomNumber . $filePath->getClientOriginalName();
                // $path = $filePath->storeAs('public/images/employee/resume', $fileName);

                $file = $request->file('resume_path');
                $request->validate([
                    'resume_path' => 'required|mimes:pdf,xlxs,xlx,docx,doc|max:2048',
                ]);
                $randomNumber = mt_rand(1000000000, 9999999999);
                $fileName = $randomNumber . $file->getClientOriginalName();
                $filePath = 'images/employee/resume/' . $fileName;
                $path = Storage::disk('public')->put($filePath, file_get_contents($request->resume_path));
                $path = Storage::disk('public')->url($path);

                $resume = new Resumes;
                $resume->user_id = $user_id;
                $resume->resume_pdf_path = $fileName;

                if ($resumegetandcheck) {
                    $resume->default_resume = 0;
                } else {
                    $resume->default_resume = 1;
                }

                $resume->status = 'active';
                $resume->save();

                if ($request->choice == 'above') {
                    $applications->resume_path = $fileName;
                } else {
                    $applications->resume_path = $resumegetandcheck->resume_pdf_path;
                }

            } else {

                if ($resumegetandcheck) {

                    $applications->resume_path = $resumegetandcheck->resume_pdf_path;

                } else {
                    $applications->resume_path = null;

                }
            }

            $applications->description = $request->input('description');
            $applications->apply_status = $request->input('apply_status', 'pending');
            $applications->status = $request->input('status', 'active');
            $applications->save();

            if ($applications->save()) {

                $user_data = User::find($user_id);
                $user_data->name = $request->name;
                $user_data->contact_no = $request->contact_no;
                $user_data->gender = $request->gender;
                if ($user_data->date_of_birth) {
                    $user_data->date_of_birth = Carbon::parse($request->date_of_birth)->toDateString();
                }

                $user_data->where_currently_based = $request->where_currently_based;
                $user_data->save();

                if ($user_data->save()) {

                    $job_data = Job::find($job_id);
                    $notification_text = '<b>' . $request->name . '</b> apply on this job <a href="/singlejobs/' . $job_data->id . '">' . $job_data->job_title . '</a>';
                    $link = null;
                    $type = 'job_apply';
                    $user_ids = array('1', $job_data->user_id);
                    //$result = app('App\Http\Controllers\Api\NotificationController')->saveNotification($user_id, $job_data->user_id, $notification_text, $link, $type);
                    // foreach($user_ids as $user_id_data){
                    //     $result = app('App\Http\Controllers\Api\NotificationController')->saveNotification($user_id, $user_id_data,  $notification_text, $link, $type);
                    // }
                    return response()->json([
                        'message' => 'Application created successfully.',
                        'status' => true,
                        'data' => $applications
                    ], 200);
                }
            }


        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getTotalApplicationsForjob(Request $request, $id)
    {
        $totalApplications = Applications::where('user_id', $id)->count();

        return response()->json([
            'data' => $totalApplications,
            'status' => true,
            'message' => 'Total Job fetched'
        ]);
    }

    public function getTotalSavedjob(Request $request, $id)
    {
        $totalApplications = SavedJobs::where('user_id', $id)->count();

        return response()->json([
            'data' => $totalApplications,
            'status' => true,
            'message' => 'Total Job fetched'
        ]);
    }
    // public function updateHiringStatus(Request $request, $id)
    // {
    //     try {
    //         $applications = Applications::where('id', $id)->where('status', 'active')->first();
    //         $applications->hiring_status = $request->input('hiring_status');
    //         $applications->save();
    //         return response()->json([
    //             'message' => 'Hiring Status updated successfully.',
    //             'status' => true,
    //             'data' => $applications
    //         ], 200);
    //     } catch (\Exception $e) {
    //         throw new HttpException(500, $e->getMessage());
    //     }
    // }

    public function updateHiringStatus(Request $request, $id)
    {
        try {
            $applications = Applications::where('id', $id)->where('status', 'active')->first();

            if (!$applications) {
                return response()->json([
                    'message' => 'Application not found.',
                    'status' => false
                ], 404);
            }


            $applications->hiring_status = $request->input('hiring_status');
            if ($applications->instant_apply == 1) {
                if ($applications->hiring_status === 'No') {
                    $applications->apply_status = 'declined';
                    $applications->instant_apply = '0';
                }
                if ($applications->hiring_status === 'Yes') {
                    $applications->apply_status = 'selected';
                    $applications->instant_apply = '0';
                }
                if ($applications->hiring_status === 'Maybe') {
                    $applications->apply_status = 'pending';
                    $applications->instant_apply = '0';
                }
            } else {
                if ($applications->hiring_status === 'No') {
                    $applications->apply_status = 'declined';
                }
                if ($applications->hiring_status === 'Yes') {
                    $applications->apply_status = 'selected';
                }
                if ($applications->hiring_status === 'Maybe') {
                    $applications->apply_status = 'pending';
                }
            }

            $applications->save();

            return response()->json([
                'message' => 'Hiring Status updated successfully.',
                'status' => true,
                'data' => $applications
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getTotalCompanyJobApplicationsCount(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;
            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $totalApplications = $query->count();
            //$totalApplications = Applications::where('user_id', $id)->count();
            $currentDate = Carbon::now();
            $lastWeekStartDate = $currentDate->subWeek();
            $lastWeekEndDate = Carbon::now();
            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastWeekStartDate, $lastWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $applicants_lastweek = $query->count();

            $last_Week_Start_Date = Carbon::now()->subWeek();
            $lastTwoWeekStartDate = $currentDate->subWeek(1);
            $lastTwoWeekEndDate = $last_Week_Start_Date;

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $applicants_lasttwoweek = $query->count();

            $applicants_Impressions_First_Week = $applicants_lasttwoweek;
            $applicants_Impressions_Last_Week = $applicants_lastweek;
            if ($applicants_Impressions_First_Week > 0 && $applicants_Impressions_Last_Week > 0) {
                $applicants_impression_count = (($applicants_Impressions_First_Week - $applicants_Impressions_Last_Week) / $applicants_Impressions_Last_Week) * 100;
                $applicants_impression_percentage = number_format($applicants_impression_count, 2) . '%';
            } else {
                $applicants_impression_percentage = '0%';
            }

            $applicantsLasttwoweek = $applicants_lasttwoweek;
            $applicantsLastweek = $applicants_lastweek;
            $applicants_ImpressionPercentage = $applicants_impression_percentage;

            return response()->json([
                'data' => $totalApplications,
                'applicants_ImpressionPercentage' => $applicants_ImpressionPercentage,
                'applicantsLasttwoweek' => $applicantsLasttwoweek,
                'applicantsLastweek' => $applicantsLastweek,
                'status' => true,
                'message' => 'Total Applicaints fetched successfully!'
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
