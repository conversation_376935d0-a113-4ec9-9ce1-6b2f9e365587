<?php

namespace App\Http\Controllers\Api;

use App\Models\Author;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Mockery\Expectation;

class AuthorController extends Controller
{
    public function authors(Request $request)
    {
        try {
            $authors = Author::active()->orderBy('id', 'desc')->get();

            if ($authors->isEmpty()) {
                return response()->json(['success' => true, 'message' => 'No authors found', 'data' => []], 200);
            }

            return response()->json(['success' => true, 'message' => 'Authors retrieved successfully', 'data' => $authors], 200);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    public function singleAuthor($slugOrId)
    {
        try {
            if (!$slugOrId) {
                return response()->json(['success' => false, 'message' => 'Invalid request', 'data' => []], 400);
            }

            //$author = Author::where('slug', $slugOrId)->orWhere('id', $slugOrId)->with('blogs','blogs.author')->active()->first();

            $author = Author::where('slug', $slugOrId)
                ->orWhere('id', $slugOrId)
                ->with(['blogs' => function ($query) {
                    $query->where('status', 'active');
                }, 'blogs.author'])
                ->active()
                ->first();

            if (!$author) {
                return response()->json(['success' => false, 'message' => 'Author not found', 'data' => []], 404);
            }

            $writeAbout = [];
            foreach ($author->blogs as $value) {
                $blogCategory = BlogCategory::find($value->blog_category_id);
                if ($blogCategory) {
                    $blogTitle = $blogCategory->category_name;
                    array_push($writeAbout, $blogTitle);
                }
            }

            return response()->json(['success' => true, 'message' => 'Author found', 'data' => $author, 'writeAbout' => $writeAbout], 200);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }


    public function createOrUpdate(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'designation' => 'required|string|max:255',
                'description' => 'nullable|string',
                'linkedin' => 'nullable|string|url',
            ]);
            $data = $request->all();
            if ($request->hasFile('profile_image')) {
                $randomNumber = mt_rand(1000000000, 9999999999);
                $imagePath = $request->file('profile_image');
                $imageName = $randomNumber . $imagePath->getClientOriginalName();
                $path = $imagePath->storeAs('public/images/blogs/author', $imageName);
                $data['profile_image'] = $imageName;
            }
            $author = Author::updateOrCreate(['id' => $request->id], $data);


            if (!$author) {
                return response()->json(['success' => false, 'message' => 'Failed to create Author', 'data' => []], 500);
            }

            return response()->json(['success' => true, 'message' => 'Author created successfully', 'data' => $author], 201);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    public function delete($id)
    {
        try {
            $author = Author::findOrFail($id);

            $deleted = $author->delete();

            if (!$deleted) {
                return response()->json(['success' => false, 'message' => 'Failed to delete Author', 'data' => []], 500);
            }

            return response()->json(['success' => true, 'message' => 'Author deleted successfully', 'data' => []], 200);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }

    }

    public function searchByName(Request $request)
    {
        try {
            $name = $request->searchInput;
            $authors = Author::where('name', 'like', '%' . $name . '%')->get();
            return response()->json(['success' => true, 'message' => 'Authors retrieved successfully', 'data' => $authors], 200);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }


}
