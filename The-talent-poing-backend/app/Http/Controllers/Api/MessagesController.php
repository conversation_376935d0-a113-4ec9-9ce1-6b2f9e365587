<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Src\FileManagement\Domain\File;
use Src\FileManagement\Infrastructure\Resources\FileResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Models\Messages;
use App\Models\Applications;
use App\Models\User;
use App\Models\Company;
use App\Classes\ErrorsClass;
use DB;
use Image;
use Validator;

//use JWTAuth;

class MessagesController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getAllEmployerReceiverMessages(Request $request, $user_id)
    {
        try {
            $message = DB::table('messages AS m1')
                ->select('m1.receiver_id', 'm1.sender_id')
                ->where(function ($query) use ($user_id) {
                    $query->where('m1.sender_id', $user_id)
                        ->where('m1.archived_messages', '!=', '1');
                })->orWhere(function ($query) use ($user_id) {
                    $query->where('m1.receiver_id', $user_id)
                        ->where('m1.archived_messages', '!=', '1');
                })
                ->where('m1.status', '!=', 'deleted')
                ->groupBy('m1.sender_id', 'm1.receiver_id')
                ->get();

            $sender_ids = $message->pluck('sender_id')->all();
            $receiver_ids = $message->pluck('receiver_id')->all();

            $mergedmessageArray = array_merge($sender_ids, $receiver_ids);

            $new_message_ids_array = array_unique($mergedmessageArray);
            if ($new_message_ids_array) {
                $all_messages_data = array();
                if (($key = array_search($user_id, $new_message_ids_array)) !== false) {
                    unset($new_message_ids_array[$key]);
                }
                foreach ($new_message_ids_array as $new_message_ids) {
                    $receiver_id = $new_message_ids;
                    $sender_id = $user_id;
                    $user_data = User::where('id', $receiver_id)->first();
                    if ($user_data) {
                        if ($user_data->role == 'admin' || $user_data->role == 'staff' || $user_data->role == 'employee') {
                            $messages = DB::table('messages AS m1')
                                ->leftjoin('messages AS m2', function ($join) {
                                    $join->on('m1.sender_id', '=', 'm2.sender_id');
                                    $join->on('m1.id', '<', 'm2.id');
                                })
                                ->whereNull('m2.id')
                                ->where(function ($query) use ($sender_id, $receiver_id) {
                                    $query->where('m1.sender_id', $sender_id)
                                        ->where('m1.receiver_id', $receiver_id)
                                        ->where('m1.archived_messages', '!=', '1');
                                })
                                ->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                    $query->where('m1.sender_id', $receiver_id)
                                        ->where('m1.receiver_id', $sender_id)
                                        ->where('m1.archived_messages', '!=', '1');
                                })
                                ->select('m1.*')
                                ->latest()
                                ->first();
                            if ($messages) {
                                if ($messages->receiver_id == $user_id) {
                                    $user_data = User::where('id', $messages->sender_id)->where('status', 'active')->first();
                                } else {
                                    $user_data = User::where('id', $messages->receiver_id)->where('status', 'active')->first();
                                }
                                if($user_data){
                                    $messages->candidate_name = $user_data->name;
                                    $messages->candidate_profile_image = $user_data->profile_image;
                                    $messages->candidate_position = $user_data->current_position;
                                    $messages->user_role = $user_data->role;
                                } else {
                                    $messages->candidate_name = '';
                                    $messages->candidate_profile_image = '';
                                    $messages->candidate_position = '';
                                    $messages->user_role = '';
                                }
                                $all_messages_data[] = $messages;
                            }
                        } else {
                            $messages = DB::table('messages AS m1')
                                ->leftjoin('messages AS m2', function ($join) {
                                    $join->on('m1.sender_id', '=', 'm2.sender_id');
                                    $join->on('m1.id', '<', 'm2.id');
                                })
                                ->whereNull('m2.id')
                                ->where(function ($query) use ($sender_id, $receiver_id) {
                                    $query->where('m1.sender_id', $sender_id)
                                        ->where('m1.receiver_id', $receiver_id)
                                        ->where('m1.archived_messages', '!=', '1');
                                })
                                ->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                    $query->where('m1.sender_id', $receiver_id)
                                        ->where('m1.receiver_id', $sender_id)
                                        ->where('m1.archived_messages', '!=', '1');
                                })
                                ->select('m1.*')
                                ->latest()
                                ->first();
                            if ($messages) {
                                if ($messages->receiver_id == $user_id) {
                                    $user_data = User::where('id', $messages->sender_id)->where('status', 'active')->first();
                                } else {
                                    $user_data = User::where('id', $messages->receiver_id)->where('status', 'active')->first();
                                }
                                if($user_data){
                                    $company_data = Company::where('id', $user_data->company_id)->where('status', 'active')->first();
                                    $messages->candidate_name = $user_data->name;
                                    $messages->candidate_profile_image = $user_data->profile_image;
                                    $messages->candidate_position = $company_data->company_name;
                                    $messages->user_role = $user_data->role;
                                } else {
                                    $messages->candidate_name = '';
                                    $messages->candidate_profile_image = '';
                                    $messages->candidate_position = '';
                                    $messages->user_role = '';
                                }
                                $all_messages_data[] = $messages;
                            }
                        }
                    }
                }
            } else {
                $all_messages_data = array();
            }
            foreach ($all_messages_data as $item) {
                if ($item->candidate_profile_image) {
                    $file = File::where('uuid', $item->candidate_profile_image)->first();

                    if ($file) {
                        $profile = new FileResource($file);
                        $item->profile = $profile;
                    } else {
                        $item->profile = null;
                    }
                }
                $count = Messages::where(['sender_id' => $item->sender_id, 'receiver_id' => $user_id, 'message_status' => 'unread'])->count();
                $item->unreadMessage = $count;
            }

            return response()->json(['status' => true, 'data' => $all_messages_data]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getAllEmployeesReceiverMessages(Request $request, $user_id)
    {
        try {
            $message = DB::table('messages AS m1')
                ->select('m1.receiver_id', 'm1.sender_id')
                ->where(function ($query) use ($user_id) {
                    $query->where('m1.sender_id', $user_id)
                        ->where('m1.archived_messages', '!=', '1');
                })->orWhere(function ($query) use ($user_id) {
                    $query->where('m1.receiver_id', $user_id)
                        ->where('m1.archived_messages', '!=', '1');
                })
                ->where('m1.status', '!=', 'deleted')
                ->groupBy('m1.sender_id', 'm1.receiver_id')
                ->get();
            $sender_ids = $message->pluck('sender_id')->all();
            $receiver_ids = $message->pluck('receiver_id')->all();
            $mergedmessageArray = array_merge($sender_ids, $receiver_ids);
            $new_message_ids_array = array_unique($mergedmessageArray);
            if ($new_message_ids_array) {
                $all_messages_data = array();
                if (($key = array_search($user_id, $new_message_ids_array)) !== false) {
                    unset($new_message_ids_array[$key]);
                }
                foreach ($new_message_ids_array as $new_message_ids) {
                    $receiver_id = $new_message_ids;
                    $sender_id = $user_id;
                    $user_data = User::where('id', $receiver_id)->first();
                    if ($user_data->role == 'admin' || $user_data->role == 'staff' || $user_data->role == 'employee') {
                        //$messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'users.role as user_role')
                        // $messages = Messages::select('messages.*')
                        // //->join('users', 'messages.sender_id', '=', 'users.id')
                        // ->where(function ($query) use ($sender_id, $receiver_id) {
                        //     $query->where('sender_id', $sender_id)
                        //         ->where('receiver_id', $receiver_id)
                        //         ->where('archived_messages', '!=', '1');
                        // })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                        //     $query->where('sender_id', $receiver_id)
                        //         ->where('receiver_id', $sender_id)
                        //         ->where('archived_messages', '!=', '1');
                        // })
                        // ->latest()
                        // ->first();
                        $messages = DB::table('messages AS m1')
                            ->leftjoin('messages AS m2', function ($join) {
                                $join->on('m1.sender_id', '=', 'm2.sender_id');
                                $join->on('m1.id', '<', 'm2.id');
                            })
                            ->whereNull('m2.id')
                            ->where(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('m1.sender_id', $sender_id)
                                    ->where('m1.receiver_id', $receiver_id)
                                    ->where('m1.archived_messages', '!=', '1');
                            })
                            ->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('m1.sender_id', $receiver_id)
                                    ->where('m1.receiver_id', $sender_id)
                                    ->where('m1.archived_messages', '!=', '1');
                            })
                            ->select('m1.*')
                            ->latest()
                            ->first();
                        if ($messages) {
                            if ($messages->receiver_id == $user_id) {
                                $user_data = User::where('id', $messages->sender_id)->where('status', 'active')->first();
                            } else {
                                $user_data = User::where('id', $messages->receiver_id)->where('status', 'active')->first();
                            }
                            $messages->employer_name = $user_data->name;
                            $messages->employer_profile_image = $user_data->profile_image;
                            $messages->user_role = $user_data->role;
                            $all_messages_data[] = $messages;
                        }
                    } else {
                        // $messages = Messages::select('messages.*')
                        // //$messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name', 'users.role as user_role')
                        // //->join('users', 'messages.sender_id', '=', 'users.id')
                        // //->join('company', 'users.company_id', '=', 'company.id')
                        // ->where(function ($query) use ($sender_id, $receiver_id) {
                        //     $query->where('messages.sender_id', $sender_id)
                        //         ->where('messages.receiver_id', $receiver_id)
                        //         ->where('messages.archived_messages', '!=', '1');
                        // })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                        //     $query->where('messages.sender_id', $receiver_id)
                        //         ->where('messages.receiver_id', $sender_id)
                        //         ->where('messages.archived_messages', '!=', '1');
                        // })
                        // ->latest()
                        // ->first();
                        $messages = DB::table('messages AS m1')
                            ->leftjoin('messages AS m2', function ($join) {
                                $join->on('m1.sender_id', '=', 'm2.sender_id');
                                $join->on('m1.id', '<', 'm2.id');
                            })
                            ->whereNull('m2.id')
                            ->where(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('m1.sender_id', $sender_id)
                                    ->where('m1.receiver_id', $receiver_id)
                                    ->where('m1.archived_messages', '!=', '1');
                            })
                            ->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('m1.sender_id', $receiver_id)
                                    ->where('m1.receiver_id', $sender_id)
                                    ->where('m1.archived_messages', '!=', '1');
                            })
                            ->select('m1.*')
                            ->latest()
                            ->first();
                        if ($messages) {
                            if ($messages->receiver_id == $user_id) {
                                $user_data = User::where('id', $messages->sender_id)->where('status', 'active')->first();
                            } else {
                                $user_data = User::where('id', $messages->receiver_id)->where('status', 'active')->first();
                            }
                            $company_data = Company::where('id', $user_data->company_id)->where('status', 'active')->first();
                            $messages->employer_name = $user_data->name;
                            $messages->employer_profile_image = $user_data->profile_image;
                            $messages->company_name = $company_data->company_name;
                            $messages->user_role = $user_data->role;
                            $all_messages_data[] = $messages;
                        }
                    }
                }
            } else {
                $all_messages_data = array();
            }
            foreach ($all_messages_data as $item) {
                if($item->employer_profile_image){
                    $file = File::where('uuid', $item->employer_profile_image)->first();
                    $count = Messages::where(['sender_id' => $item->sender_id, 'receiver_id' => $user_id, 'message_status' => 'unread'])->count();
                    $profile = new FileResource($file);
                    $item->profile = $profile;
                    $item->unreadMessage = $count;
                } else {
                    $count = Messages::where(['sender_id' => $item->sender_id, 'receiver_id' => $user_id, 'message_status' => 'unread'])->count();
                    $item->profile = '';
                    $item->unreadMessage = $count;
                }
            }
            return response()->json(['status' => true, 'data' => $all_messages_data]);
            // $messages = DB::table('messages AS m1')
            //     ->leftjoin('messages AS m2', function($join) {
            //         $join->on('m1.sender_id', '=', 'm2.sender_id');
            //         $join->on('m1.id', '<', 'm2.id');
            //     })
            //     ->leftjoin('users', 'm1.sender_id', '=', 'users.id')
            //     ->leftjoin('company', 'users.company_id', '=', 'company.id')
            //     ->whereNull('m2.id')
            //     ->select('m1.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name')
            //     ->where('m1.receiver_id', '=', $user_id)
            //     ->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '1')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->orderBy('m1.created_at', 'desc')->get();
            // $sender_users_id_count = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '<', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.receiver_id')
            //     ->where('m1.sender_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '1')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.receiver_id')
            //     ->count();
            // if($sender_users_id_count > 0){
            //     $sender_users_id = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '<', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.receiver_id')
            //     ->where('m1.sender_id', '=', $user_id)
            //     ->where('m1.sender_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '1')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.receiver_id')
            //     ->get();
            //     $sender_ids = $sender_users_id->pluck('receiver_id')->all();
            //     if($sender_ids){
            //         foreach($sender_ids as $senderIds){
            //             $messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name')->join('users', 'messages.receiver_id', '=', 'users.id')->join('company', 'users.company_id', '=', 'company.id')->where('messages.receiver_id', $senderIds)->where('messages.sender_id', $user_id)->latest()->first();
            //         }
            //     } else {
            //         $messages = array();
            //     }
            // } else {
            //     $sender_users_id = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '<', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.sender_id')
            //     ->where('m1.receiver_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '1')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.sender_id')
            //     ->get();
            //     $sender_ids = $sender_users_id->pluck('sender_id')->all();
            //     if($sender_ids){
            //         foreach($sender_ids as $senderIds){
            //             $messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name')->join('users', 'messages.sender_id', '=', 'users.id')->join('company', 'users.company_id', '=', 'company.id')->where('messages.sender_id', $senderIds)->where('messages.receiver_id', $user_id)->latest()->first();
            //         }
            //     } else {
            //         $messages = array();
            //     }
            // }
            //return response()->json(['status' => true, 'data' => $messages]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllEmployerSingleUserMessage(Request $request, $candidate_user_id)
    {
        try {
            $message = Messages::select('users.name as candidate_name', 'users.current_position as candidate_position', 'applications.created_at as applied_date', 'applications.id as applied_id', 'jobs.job_title', 'company.company_name', 'jobs.id as job_id', 'jobs.job_slug')
                ->join('users', 'messages.sender_id', '=', 'users.id')
                ->join('applications', 'messages.sender_id', '=', 'applications.jobpost_by_userId')
                //->join('applications', 'messages.receiver_id', '=', 'applications.user_id')
                ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->where(function ($query) use ($candidate_user_id) {
                    $query->where('messages.sender_id', $candidate_user_id);
                })
                ->orWhere(function ($query) use ($candidate_user_id) {
                    $query->where('messages.receiver_id', $candidate_user_id);
                })
                ->where('messages.archived_messages', '!=', '1')
                ->where('messages.status', '!=', 'deleted')
                ->first();
            return response()->json(['status' => true, 'data' => $message]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllEmployeesSingleUserMessage(Request $request, $candidate_user_id)
    {
        try {
            $message = Messages::select('users.name as candidate_name', 'users.current_position as candidate_position', 'applications.created_at as applied_date', 'applications.id as applied_id', 'jobs.job_title', 'company.company_name', 'jobs.id as job_id', 'jobs.job_slug')
                ->join('users', 'messages.sender_id', '=', 'users.id')
                ->join('applications', 'messages.sender_id', '=', 'applications.jobpost_by_userId')
                ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                //->where('messages.sender_id', $candidate_user_id)
                ->where(function ($query) use ($candidate_user_id) {
                    $query->where('messages.sender_id', $candidate_user_id);
                })->orWhere(function ($query) use ($candidate_user_id) {
                    $query->where('messages.receiver_id', $candidate_user_id);
                })
                ->where('messages.archived_messages', '!=', '1')
                ->where('messages.status', '!=', 'deleted')
                ->first();
            $user = User::with('profile')->find($message->sender_id);
            $profile = new FileResource($user->profile);
            $message->profile = $profile;

            $receiver_user_data = User::where('id', $candidate_user_id)->where('status', 'active')->first();
            $message['receiver_user_role'] = $receiver_user_data->role;
            if (!$message) {
                return response()->json(['status' => false, 'message' => 'Message not found'], 200);
            }
            return response()->json(['status' => true, 'data' => $message]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getAllReceiverUserMessages(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $receiver_id = $request->receiver_id;
            $message = Messages::select('messages.*', 'sender.name as sender_name', 'receiver.name as receiver_name', 'sender.profile_image as sender_profile_image', 'receiver.profile_image as receiver_profile_image', 'company.company_name', 'receiver.current_position as receiver_current_postion', 'sender.current_position as sender_current_postion', 'receiver.role as user_role', 'sender.role as user_role')
                ->leftjoin('users as sender', 'messages.sender_id', '=', 'sender.id')
                ->leftjoin('users as receiver', 'messages.receiver_id', '=', 'receiver.id')
                ->leftjoin('company', 'sender.company_id', '=', 'company.id')
                ->where(function ($query) use ($sender_id, $receiver_id) {
                    $query->where('messages.sender_id', $sender_id)
                        ->where('messages.receiver_id', $receiver_id)
                        ->where('messages.archived_messages', '!=', '1');
                })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                    $query->where('messages.sender_id', $receiver_id)
                        ->where('messages.receiver_id', $sender_id)
                        ->where('messages.archived_messages', '!=', '1');
                })->orderBy('messages.id', 'asc')->get();
            foreach ($message as $item) {
                $user = User::with('profile')->find($item->sender_id);
                if ($user->profile) {
                    $profile = new FileResource($user->profile);
                    $item->profile = $profile;
                } else {
                    $item->profile = null;
                }
            }
            if (!$message) {
                return response()->json(['status' => false, 'message' => 'Message not found'], 200);
            }
            return response()->json(['status' => true, 'data' => $message]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function saveMessage(Request $request)
    {
        try {
            $applications_id = $request->applicants_id;
            $candidate_id = $request->candidate_id;
            $job_id = $request->job_id;
            $current_user_id = $request->current_user_id;

            $message = new Messages;
            $message->sender_id = $request->current_user_id;
            $message->receiver_id = $request->candidate_id;
            $message->job_id = $request->job_id;
            $message->applicants_id = $request->applicants_id;
            $message->message_title = $request->message_title;
            $message->message_description = $request->message_description;
            $message->message_type = $request->message_type;

            if ($request->hasFile('attachment_path')) {
                $validator = Validator::make(
                    request()->all(),
                    [
                        'attachment_path' => 'required|mimes:pdf,xlxs,xlx,docx,doc,png,jpg,jpeg|max:2048',
                    ],
                    [
                        'attachment_path.required' => 'Please select any attachment file.',
                        'attachment_path.mimes' => 'Only Allowed file type: pdf, xlxs, xlx, docx, doc, png, jpg, jpeg.',
                        'attachment_path.max' => 'Attachment file not allowed greater than 2MB.',
                    ]
                );
                if ($validator->fails()) {
                    return response()->json([
                        'errors' => $validator->errors(),
                        'status' => false,
                    ], 200);
                }
                $file = $request->file('attachment_path');
                // $validation = $request->validate([
                //     'attachment_path' => 'required|mimes:pdf,xlxs,xlx,docx,doc,png,jpg,jpeg|max:2048',
                // ]);
                $fileName = time() . '.' . $file->getClientOriginalName();
                $filePath = 'images/messageAttachmentFile/' . $fileName;

                $path = Storage::disk('public')->put($filePath, file_get_contents($request->attachment_path));
                $path = Storage::disk('public')->url($path);
                $message->attachment_path = $fileName;

                // $file = $request->file('attachment_path');
                // $request->validate([
                //     'attachment_path' => 'required|mimes:pdf,xlxs,xlx,docx,doc,png,jpg,jpeg|max:2048',
                // ]);
                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $fileName = $randomNumber . $file->getClientOriginalName();
                // $file->move('public/images/messageAttachmentFile', $fileName);
                // $message->attachment_path = $fileName;
            }
            $message->status = 'active';
            $message->save();

            return response()->json([
                'status' => true,
                'message' => 'Message sent successfully',
                'data' => $message,
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function updateArchivedMessages(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $receiver_id = $request->receiver_id;
            $message = Messages::where(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $sender_id)
                    ->where('messages.receiver_id', $receiver_id);
            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $receiver_id)
                    ->where('messages.receiver_id', $sender_id);
            })->update(['archived_messages' => '1']);

            if ($message) {
                return response()->json(['status' => true, 'message' => 'Chat archived successfully!'], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Chat archived not successfully!'], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    function updateUnArchivedMessages(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $receiver_id = $request->receiver_id;
            $message = Messages::where(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $sender_id)
                    ->where('messages.receiver_id', $receiver_id);
            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $receiver_id)
                    ->where('messages.receiver_id', $sender_id);
            })->update(['archived_messages' => '0']);

            if ($message) {
                return response()->json(['status' => true, 'message' => 'Chat unarchived successfully!'], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Chat unarchived not successfully!'], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllEmployerReceiverArchivedMessages(Request $request, $user_id)
    {
        try {
            $message = DB::table('messages AS m1')
                ->select('m1.receiver_id', 'm1.sender_id')
                ->where(function ($query) use ($user_id) {
                    $query->where('m1.sender_id', $user_id)
                        ->where('m1.archived_messages', '!=', '0');
                })->orWhere(function ($query) use ($user_id) {
                    $query->where('m1.receiver_id', $user_id)
                        ->where('m1.archived_messages', '!=', '0');
                })
                ->where('m1.status', '!=', 'deleted')
                ->groupBy('m1.sender_id', 'm1.receiver_id')
                ->get();
            $sender_ids = $message->pluck('sender_id')->all();
            $receiver_ids = $message->pluck('receiver_id')->all();
            $mergedmessageArray = array_merge($sender_ids, $receiver_ids);
            $new_message_ids_array = array_unique($mergedmessageArray);
            if ($new_message_ids_array) {
                $messages = array();
                if (($key = array_search($user_id, $new_message_ids_array)) !== false) {
                    unset($new_message_ids_array[$key]);
                }
                foreach ($new_message_ids_array as $new_message_ids) {
                    $receiver_id = $new_message_ids;
                    $sender_id = $user_id;
                    $user_data = User::where('id', $receiver_id)->first();
                    //echo $user_data->role;
                    if ($user_data->role == 'admin' || $user_data->role == 'staff' || $user_data->role == 'employee') {
                        $messages[] = Messages::select('messages.*', 'users.name as candidate_name', 'users.profile_image as candidate_profile_image', 'users.current_position as candidate_position', 'users.role as user_role')
                            //->join('users', 'messages.receiver_id', '=', 'users.id')
                            ->join('users', function ($join) {
                                $join->on('messages.receiver_id', '=', 'users.id');
                            })
                            ->where(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $sender_id)
                                    ->where('messages.receiver_id', $receiver_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $receiver_id)
                                    ->where('messages.receiver_id', $sender_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })
                            ->latest()
                            ->first();
                    } else {
                        $messages[] = Messages::select('messages.*', 'users.name as candidate_name', 'users.profile_image as candidate_profile_image', 'users.current_position as candidate_position', 'users.role as user_role', 'company.company_name as company_name')
                            //->join('users', 'messages.receiver_id', '=', 'users.id')
                            ->join('users', function ($join) {
                                $join->on('messages.receiver_id', '=', 'users.id');

                            })
                            ->join('company', 'users.company_id', '=', 'company.id')
                            ->where(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $sender_id)
                                    ->where('messages.receiver_id', $receiver_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $receiver_id)
                                    ->where('messages.receiver_id', $sender_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })
                            ->latest()
                            ->first();
                    }
                }
            } else {
                $messages = array();
            }
            return response()->json(['status' => true, 'data' => $messages]);
            // $sender_users_id = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '>', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.sender_id')
            //     ->where('m1.receiver_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '0')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.sender_id')
            //     ->orderBy('m1.id', 'desc')->get();

            // $sender_ids = $sender_users_id->pluck('sender_id')->all();
            // if($sender_ids){
            //     foreach($sender_ids as $senderIds){
            //         $messages[] = Messages::select('messages.*', 'users.name as candidate_name', 'users.profile_image as candidate_profile_image', 'users.current_position as candidate_position')->join('users', 'messages.sender_id', '=', 'users.id')->where('messages.sender_id', $senderIds)->where('messages.receiver_id', $user_id)->latest()->first();
            //     }
            // } else {
            //     $messages = array();
            // }
            return response()->json(['status' => true, 'data' => $messages]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getAllEmployeesReceiverArchivedMessages(Request $request, $user_id)
    {
        try {
            //* $messages = DB::table('messages AS m1')
            //     ->leftjoin('messages AS m2', function($join) {
            //         $join->on('m1.sender_id', '=', 'm2.sender_id');
            //         $join->on('m1.id', '<', 'm2.id');
            //     })
            //     ->leftjoin('users', 'm1.sender_id', '=', 'users.id')
            //     ->leftjoin('company', 'users.company_id', '=', 'company.id')
            //      ->whereNull('m2.id')
            //     ->select('m1.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name')
            //     ->where('m1.receiver_id', '=', $user_id)
            //     ->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '0')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->orderBy('m1.created_at', 'desc')->get();
            $message = DB::table('messages AS m1')
                ->select('m1.receiver_id', 'm1.sender_id')
                ->where(function ($query) use ($user_id) {
                    $query->where('m1.sender_id', $user_id)
                        ->where('m1.archived_messages', '!=', '0');
                })->orWhere(function ($query) use ($user_id) {
                    $query->where('m1.receiver_id', $user_id)
                        ->where('m1.archived_messages', '!=', '0');
                })
                ->where('m1.status', '!=', 'deleted')
                ->groupBy('m1.sender_id', 'm1.receiver_id')
                ->get();
            $sender_ids = $message->pluck('sender_id')->all();
            $receiver_ids = $message->pluck('receiver_id')->all();
            $mergedmessageArray = array_merge($sender_ids, $receiver_ids);
            $new_message_ids_array = array_unique($mergedmessageArray);
            if ($new_message_ids_array) {
                $messages = array();
                if (($key = array_search($user_id, $new_message_ids_array)) !== false) {
                    unset($new_message_ids_array[$key]);
                }
                foreach ($new_message_ids_array as $new_message_ids) {
                    $receiver_id = $new_message_ids;
                    $sender_id = $user_id;
                    $user_data = User::where('id', $receiver_id)->first();
                    if ($user_data->role == 'admin' || $user_data->role == 'staff' || $user_data->role == 'employee') {
                        $messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'users.role as user_role')
                            ->join('users', 'messages.receiver_id', '=', 'users.id')
                            ->where(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $sender_id)
                                    ->where('messages.receiver_id', $receiver_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $receiver_id)
                                    ->where('messages.receiver_id', $sender_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })
                            ->latest()
                            ->first();
                    } else {
                        $messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name', 'users.role as user_role')
                            ->join('users', 'messages.receiver_id', '=', 'users.id')
                            ->join('company', 'users.company_id', '=', 'company.id')
                            ->where(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $sender_id)
                                    ->where('messages.receiver_id', $receiver_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                                $query->where('messages.sender_id', $receiver_id)
                                    ->where('messages.receiver_id', $sender_id)
                                    ->where('messages.archived_messages', '!=', '0');
                            })
                            ->latest()
                            ->first();
                    }
                }
            } else {
                $messages = array();
            }
            return response()->json(['status' => true, 'data' => $messages]);
            // $sender_users_id_count = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '<', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.receiver_id')
            //     ->where('m1.sender_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '0')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.receiver_id')
            //     ->count();
            // if($sender_users_id_count > 0){
            //     $sender_users_id = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '<', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.receiver_id')
            //     ->where('m1.sender_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '0')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.receiver_id')
            //     ->get();
            //     $sender_ids = $sender_users_id->pluck('receiver_id')->all();
            //     if($sender_ids){
            //         foreach($sender_ids as $senderIds){
            //             $messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name')->join('users', 'messages.receiver_id', '=', 'users.id')->join('company', 'users.company_id', '=', 'company.id')->where('messages.receiver_id', $senderIds)->where('messages.sender_id', $user_id)->latest()->first();
            //         }
            //     } else {
            //         $messages = array();
            //     }
            // } else {
            //     $sender_users_id = DB::table('messages AS m1')
            //     // ->leftjoin('messages AS m2', function($join) {
            //     //     $join->on('m1.sender_id', '=', 'm2.sender_id');
            //     //     $join->on('m1.id', '<', 'm2.id');
            //     // })
            //     // ->whereNull('m2.id')
            //     ->select('m1.sender_id')
            //     ->where('m1.receiver_id', '=', $user_id)
            //     //->where('m1.message_type', '=', 'applyJob')
            //     ->where('m1.archived_messages', '!=', '0')
            //     ->where('m1.status', '!=', 'deleted')
            //     ->groupBy('m1.sender_id')
            //     ->get();
            //     $sender_ids = $sender_users_id->pluck('sender_id')->all();
            //     if($sender_ids){
            //         foreach($sender_ids as $senderIds){
            //             $messages[] = Messages::select('messages.*', 'users.name as employer_name', 'users.profile_image as employer_profile_image', 'company.company_name')->join('users', 'messages.sender_id', '=', 'users.id')->join('company', 'users.company_id', '=', 'company.id')->where('messages.sender_id', $senderIds)->where('messages.receiver_id', $user_id)->latest()->first();
            //         }
            //     } else {
            //         $messages = array();
            //     }
            // }
            // return response()->json(['status' => true, 'data' => $messages]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllEmployerSingleUserArchivedMessages(Request $request, $candidate_user_id)
    {
        try {
            $message = Messages::select('users.name as candidate_name', 'users.current_position as candidate_position', 'applications.created_at as applied_date', 'applications.id as applied_id', 'jobs.job_title', 'company.company_name', 'jobs.id as job_id', 'jobs.job_slug', 'users.role as user_role')
                ->join('users', 'messages.sender_id', '=', 'users.id')
                ->join('applications', 'messages.sender_id', '=', 'applications.user_id')
                ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->where('messages.sender_id', $candidate_user_id)
                ->where('messages.archived_messages', '!=', '0')
                ->where('messages.status', '!=', 'deleted')
                ->first();
            if (!$message) {
                return response()->json(['status' => false, 'message' => 'Message not found'], 200);
            }
            return response()->json(['status' => true, 'data' => $message]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllEmployeesSingleUserArchivedMessages(Request $request, $candidate_user_id)
    {
        try {
            $message = Messages::select('users.name as candidate_name', 'users.current_position as candidate_position', 'applications.created_at as applied_date', 'applications.id as applied_id', 'jobs.job_title', 'company.company_name', 'jobs.id as job_id', 'jobs.job_slug', 'users.role as user_role')
                ->join('users', 'messages.sender_id', '=', 'users.id')
                ->join('applications', 'messages.sender_id', '=', 'applications.user_id')
                ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->where('messages.receiver_id', $candidate_user_id)
                ->where('messages.archived_messages', '!=', '0')
                ->where('messages.status', '!=', 'deleted')
                ->first();
            if (!$message) {
                return response()->json(['status' => false, 'message' => 'Message not found'], 200);
            }
            return response()->json(['status' => true, 'data' => $message]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getAllReceiverUserArchivedMessages(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $receiver_id = $request->receiver_id;
            $message = Messages::select('messages.*', 'sender.name as sender_name', 'receiver.name as receiver_name', 'sender.profile_image as sender_profile_image', 'receiver.profile_image as receiver_profile_image', 'company.company_name', 'receiver.current_position as receiver_current_postion', 'sender.current_position as sender_current_postion', 'receiver.role as user_role', 'sender.role as user_role')
                ->leftjoin('users as sender', 'messages.sender_id', '=', 'sender.id')
                ->leftjoin('users as receiver', 'messages.receiver_id', '=', 'receiver.id')
                ->leftjoin('company', 'sender.company_id', '=', 'company.id')
                ->where(function ($query) use ($sender_id, $receiver_id) {
                    $query->where('messages.sender_id', $sender_id)
                        ->where('messages.receiver_id', $receiver_id)
                        ->where('messages.archived_messages', '!=', '0');
                })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                    $query->where('messages.sender_id', $receiver_id)
                        ->where('messages.receiver_id', $sender_id)
                        ->where('messages.archived_messages', '!=', '0');
                })->orderBy('messages.id', 'asc')->get();
            if (!$message) {
                return response()->json(['status' => false, 'message' => 'Message not found'], 200);
            }
            return response()->json(['status' => true, 'data' => $message]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    function UpdateMessageReadUnReadStatus(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $receiver_id = $request->receiver_id;
            $message = Messages::where(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $sender_id)
                    ->where('messages.receiver_id', $receiver_id);
            })->update(['message_status' => 'read']);

            if ($message) {
                return response()->json(['status' => true, 'message' => 'Message status update Successfully!'], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Message status not update Successfully!'], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function getTotalMessageUnReadCount(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $message_count = Messages::where('receiver_id', $sender_id)->where('message_status', 'unread')->count();

            if ($message_count > 0) {
                return response()->json(['status' => true, 'message' => 'Message count!', 'total_unread_message_count' => $message_count], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Message count not found!', 'total_unread_message_count' => 0], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function getFirstMessageCheckCount(Request $request)
    {
        try {
            $sender_id = $request->sender_id;
            $receiver_id = $request->receiver_id;
            $message_count = Messages::where(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $sender_id)
                    ->where('messages.receiver_id', $receiver_id);
            })->orWhere(function ($query) use ($sender_id, $receiver_id) {
                $query->where('messages.sender_id', $receiver_id)
                    ->where('messages.receiver_id', $sender_id);
            })->count();
            if ($message_count > 0) {
                return response()->json(['status' => true, 'message' => 'Message count!', 'total_message_count' => $message_count], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Message count not found!', 'total_unread_message_count' => 0], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
