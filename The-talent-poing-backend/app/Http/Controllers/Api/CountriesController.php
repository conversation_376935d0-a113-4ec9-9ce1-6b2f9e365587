<?php

namespace App\Http\Controllers\Api;

use App\Models\Industry;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\AppFramework\ApiController;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Models\Country;
use App\Models\Cities;
use App\Models\Job;

/**
 * @group Countries
 */
class CountriesController extends ApiController
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $countries = (new Country)->orderBy('country_name');
            if ($request->keywords) {
                $countries->where('country_name', 'like', '%' . $request->keywords . '%');
            }
            if ($request->order_by) {
                if ($request->order_by == 'asc') {
                    $countries->orderBy('id', 'ASC');
                }
                $countries->orderBy('id', 'DESC');
            }
            $countries->where('status', 'active');
            if ($countries->count() > 0) {
                return $this->respondWithSuccess($countries->get());
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllStatusCountries():JsonResponse
    {
        try {
            $getAllCountries = Country::orderBy('country_name', 'asc')->get();
            if ($getAllCountries) {
                return $this->respondWithSuccess($getAllCountries);
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllCountries():JsonResponse
    {
        try {
            $getAllCountries = Country::where('status', 'active')->orderBy('country_name', 'asc')->get();
            if ($getAllCountries) {
                return $this->respondWithSuccess($getAllCountries);
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllCountriesForAdmin():JsonResponse
    {
        try {
            $getAllCountries = Country::where('status', '!=', 'deleted')->orderByDesc('id')->get();
            if ($getAllCountries) {
                return $this->respondWithSuccess($getAllCountries);
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function editAndSaveCountry(Request $request)
    {
        try {

            if ($request->id) {

                $countries = Country::find($request->id);
                $countries->country_name = $request->country_name;
                $countries->slug = $request->slug;
                $countries->capital = $request->capital;
                $countries->currency = $request->currency;

                if ($request->hasFile('image')) {
                    $randomNumber = mt_rand(1000000000, 9999999999);
                    $imagePath = $request->file('image');
                    $imageName = $randomNumber . $imagePath->getClientOriginalName();
                    $path = $imagePath->storeAs('public/images/country', $imageName);
                    $countries->flag = $imageName;
                }

                $countries->save();


                return response()->json([
                    'status' => true,
                    'message' => 'Country updated successfully',
                ], 200);
            } else {

                $countries = new Country;
                $countries->country_name = $request->country_name;
                $countries->slug = $request->slug;
                $countries->capital = $request->capital;
                $countries->currency = $request->currency;


                if ($request->hasFile('image')) {
                    $randomNumber = mt_rand(1000000000, 9999999999);
                    $imagePath = $request->file('image');
                    $imageName = $randomNumber . $imagePath->getClientOriginalName();
                    $path = $imagePath->storeAs('public/images/country', $imageName);
                    $countries->flag = $imageName;
                }


                $countries->save();


                return response()->json([
                    'status' => true,
                    'message' => 'Location added successfully',
                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteCountry(Request $request)
    {
        try {

            $sector = Country::find($request->id);
            $sector->status = 'deleted';
            $sector->save();

            return response()->json([
                'status' => true,
                'message' => 'Country has been deleted successfully!',
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getCountries()
    {
        try {
            $targetIds = [45, 34, 36, 4, 21, 16, 64, 18, 30];
            $getallcountries = Country::whereIn('id', $targetIds)->where('status', 'active')->get();
            $jobCount = Job::whereIn('job_country', $targetIds)
                ->groupBy('job_country')
                ->selectRaw('job_country, count(*) as count')
                ->pluck('count', 'job_country');

            if ($getallcountries) {
                return response()->json([
                    'status' => true,
                    'data' => $getallcountries,
                    'jobs' => $jobCount,
                    'message' => 'countries listing successfully!'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'user' => '',
                    'message' => 'countries listing not found successfully!'
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getAllIndustriesForAdmin():JsonResponse
    {
        try {
            $industry = Industry::where('status', 'active')->orderBy('id', 'desc')->get();
            if ($industry) {
                return $this->respondWithSuccess($industry);
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    //a:56:{i:0;s:40:"sitepress-multilingual-cms/sitepress.php";i:1;s:30:"wp-seo-multilingual/plugin.php";i:2;s:41:"Ultimate_VC_Addons/Ultimate_VC_Addons.php";i:3;s:30:"advanced-custom-fields/acf.php";i:4;s:35:"carousel-slider/carousel-slider.php";i:5;s:60:"cf7-conditional-fields/contact-form-7-conditional-fields.php";i:6;s:27:"check-email/check-email.php";i:7;s:33:"classic-editor/classic-editor.php";i:8;s:67:"contact-form-7-lead-info-with-country/wpshore_cf7_lead_tracking.php";i:9;s:59:"contact-form-7-multi-step-pro/contact-form-7-multi-step.php";i:10;s:38:"contact-form-7-multilingual/plugin.php";i:11;s:77:"contact-form-7-pipedrive-integration/contact-form-7-pipedrive-integration.php";i:12;s:36:"contact-form-7/wp-contact-form-7.php";i:13;s:51:"controlled-admin-access/controlled-admin-access.php";i:14;s:73:"country-phone-field-contact-form-7/country-phone-field-contact-form-7.php";i:15;s:31:"custom-css-js/custom-css-js.php";i:16;s:43:"custom-post-type-ui/custom-post-type-ui.php";i:17;s:58:"date-time-picker-for-contact-form-7/cf7-datetimepicker.php";i:18;s:76:"drag-and-drop-multiple-file-upload-contact-form-7/drag-n-drop-upload-cf7.php";i:19;s:49:"easy-table-of-contents/easy-table-of-contents.php";i:20;s:29:"expand-maker/expand-maker.php";i:21;s:25:"form-vibes/form-vibes.php";i:22;s:33:"formidable-pro/formidable-pro.php";i:23;s:37:"formidable-views/formidable-views.php";i:24;s:25:"formidable/formidable.php";i:25;s:25:"g-meta-keywords/index.php";i:26;s:24:"header-footer/plugin.php";i:27;s:22:"honeypot/wp-armour.php";i:28;s:39:"image-slider-slideshow/image-slider.php";i:29;s:35:"insert-headers-and-footers/ihaf.php";i:30;s:47:"ithemes-security-pro-2/ithemes-security-pro.php";i:31;s:27:"js_composer/js_composer.php";i:32;s:37:"link-whisper-premium/link-whisper.php";i:33;s:47:"marquee-image-crawler/marquee-image-crawler.php";i:34;s:55:"nextend-smart-slider3-pro/nextend-smart-slider3-pro.php";i:35;s:27:"popup-maker/popup-maker.php";i:36;s:22:"post-carousel/main.php";i:37;s:34:"pricing-table-by-supsystic/pts.php";i:38;s:58:"responsive-accordion-and-collapse/responsive-accordion.php";i:39;s:60:"schema-and-structured-data-for-wp/structured-data-for-wp.php";i:40;s:39:"search-filter-pro/search-filter-pro.php";i:41;s:66:"select-multiselect-field-contact-form-7/select-multiselect-cf7.php";i:42;s:43:"strong-testimonials/strong-testimonials.php";i:43;s:31:"ultimate-faqs/ultimate-faqs.php";i:44;s:19:"us-core/us-core.php";i:45;s:37:"user-role-editor/user-role-editor.php";i:46;s:23:"wordfence/wordfence.php";i:47;s:24:"wordpress-seo/wp-seo.php";i:48;s:37:"wp-carousel-free/wp-carousel-free.php";i:49;s:65:"wp-contact-form-7-spam-blocker/spam-protect-for-contact-form7.php";i:50;s:39:"wp-file-manager/file_folder_manager.php";i:51;s:23:"wp-rocket/wp-rocket.php";i:52;s:19:"wp-smtp/wp-smtp.php";i:53;s:37:"wp-table-builder/wp-table-builder.php";i:54;s:34:"wpml-string-translation/plugin.php";i:55;s:24:"zoho-forms/zohoForms.php";}

    /*
define('DB_NAME', 'connectresources_wp');


define('DB_USER', 'root');

define('DB_PASSWORD', '3676(/%&/&%');

*/

    public function getCountriesSeletedIds()
    {
        try {
            $targetIds = [45, 36, 30, 34, 21, 4];
            $getallcountries = Country::whereIn('id', $targetIds)->where('status', 'active')->orderBy('id', 'DESC')->get();
            $countries_data_array = array();
            foreach ($getallcountries as $countries_data) {
                $country_id = $countries_data->id;
                if ($countries_data->id == '45') {
                    $target_ids = [462, 478, 498, 485, 474, 479, 153572, 464];
                    $cities_data = Cities::whereIn('id', $target_ids)->where('country_id', $country_id)->where('status', 'active')->take(5)->get();
                } else if ($countries_data->id == '36') {
                    $target_ids = [454, 190, 394, 320, 153571, 156, 153570, 153569];
                    $cities_data = Cities::whereIn('id', $target_ids)->where('country_id', $country_id)->where('status', 'active')->take(5)->get();
                } else if ($countries_data->id == '30') {
                    $target_ids = [61, 153568, 48, 153567];
                    $cities_data = Cities::whereIn('id', $target_ids)->where('country_id', $country_id)->where('status', 'active')->take(5)->get();
                } else if ($countries_data->id == '34') {
                    $target_ids = [73];
                    $cities_data = Cities::whereIn('id', $target_ids)->where('country_id', $country_id)->where('status', 'active')->take(5)->get();
                } else if ($countries_data->id == '21') {
                    $target_ids = [153566];
                    $cities_data = Cities::whereIn('id', $target_ids)->where('country_id', $country_id)->where('status', 'active')->take(5)->get();
                } else if ($countries_data->id == '4') {
                    $target_ids = [2];
                    $cities_data = Cities::whereIn('id', $target_ids)->where('country_id', $country_id)->where('status', 'active')->take(5)->get();
                }
                $countries_data_arr = [];
                $countries_data_arr['country_name'] = $countries_data->country_name;
                $countries_data_arr['cities'] = $cities_data;
                array_push($countries_data_array, $countries_data_arr);
            }
            $other_location_cities = Cities::whereIn('country_id', $targetIds)
                ->inRandomOrder()
                ->take(30)
                ->get();
            if ($countries_data_array) {
                return response()->json([
                    'status' => true,
                    'data' => $countries_data_array,
                    'other_location_data' => $other_location_cities,
                    'message' => 'countries listing successfully!'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'data' => '',
                    'other_location_data' => '',
                    'message' => 'countries listing not found successfully!'
                ]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateCountryStatus(Request $request)
    {
        try {
            $countries = Country::find($request->countryId);
            if ($request->status == 'false') {
                $status = 'inactive';
            } else {
                $status = 'active';
            }
            $countries->status = $status;
            $countries->save();
            return response()->json([
                'status' => true,
                'message' => 'country status has been updated successfully!',
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getSingleCountryByName(Request $request)
    {
        try {
            $countryName = $request->get('countryName');
            $countryData = (new Country)->where('country_name', 'like', '%' . $countryName . '%')->where('status', 'active');
            if ($countryData) {
                return $this->respondWithSuccess($countryData->first());
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllNationality():JsonResponse
    {
        try {
            $getAllNationality = Country::where('status', '!=', 'deleted')->get();
            if ($getAllNationality) {
                return $this->respondWithSuccess($getAllNationality);
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllCountriesCities():JsonResponse
    {
        try {
            $getAllCountries = Country::where('status', 'active')->get();
            if ($getAllCountries) {
                foreach ($getAllCountries as $country){
                    $cities = Cities::where('country_id', $country->id)->where('status', 'active')->get();
                    $country->cities = $cities;
                }
                return $this->respondWithSuccess($getAllCountries);
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }
}
