<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Exception;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\AdminSettings;
use App\Models\User;
use App\Models\Job;
//use JWTAuth;
use Carbon\Carbon;

class AdminSettingsController extends Controller
{
    use ApiResponseHelpers;
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }
    public function updateAdminSetting(Request $request, $id)
    {
        try {
            $adminSetting = AdminSettings::where('id', $id)->where('status', 'active')->first();

            if (!$adminSetting) {
                return response()->json([
                    'status' => false,
                    'message' => 'Admin setting not found',
                ], 404);
            }

            $adminSetting->user_id = $request->input('user_id');
            $adminSetting->logo = $request->input('edit_logo');
            $adminSetting->favicon = $request->input('edit_favicon');
            $adminSetting->payment_gateway = $request->input('edit_payment_gateway');
            $adminSetting->stripe_test_secret_key = $request->input('edit_stripe_test_secret_key');
            $adminSetting->stripe_test_publish_key = $request->input('edit_stripe_test_publish_key');
            $adminSetting->stripe_live_secret_key = $request->input('edit_stripe_live_secret_key');
            $adminSetting->stripe_live_publish_key = $request->input('edit_stripe_live_publish_key');
            $adminSetting->homepage_meta_tag = $request->input('edit_homepage_meta_tag');
            $adminSetting->homepage_meta_title = $request->input('edit_homepage_meta_title');
            $adminSetting->homepage_meta_description = $request->input('edit_homepage_meta_description');
            $adminSetting->jobs_meta_tag = $request->input('edit_jobs_meta_tag');
            $adminSetting->jobs_meta_title = $request->input('edit_jobs_meta_title');
            $adminSetting->jobs_meta_description = $request->input('edit_jobs_meta_description');
            $adminSetting->carrer_meta_tag = $request->input('edit_carrer_meta_tag');
            $adminSetting->carrer_meta_title = $request->input('edit_carrer_meta_title');
            $adminSetting->carrer_meta_description = $request->input('edit_carrer_meta_description');
            $adminSetting->about_meta_tag = $request->input('edit_about_meta_tag');
            $adminSetting->about_meta_title = $request->input('edit_about_meta_title');
            $adminSetting->about_meta_description = $request->input('edit_about_meta_description');
            $adminSetting->employer_meta_tag = $request->input('edit_employer_meta_tag');
            $adminSetting->employer_meta_title = $request->input('edit_employer_meta_title');
            $adminSetting->employer_meta_description = $request->input('edit_employer_meta_description');
            $adminSetting->employee_meta_tag = $request->input('edit_employee_meta_tag');
            $adminSetting->employee_meta_title = $request->input('edit_employee_meta_title');
            $adminSetting->employee_meta_description = $request->input('edit_employee_meta_description');
            $adminSetting->pricing_meta_tag = $request->input('edit_pricing_meta_tag');
            $adminSetting->pricing_meta_title = $request->input('edit_pricing_meta_title');
            $adminSetting->pricing_meta_description = $request->input('edit_pricing_meta_description');
            $adminSetting->blog_listing_meta_tag = $request->input('edit_blog_listing_meta_tag');
            $adminSetting->blog_listing_meta_title = $request->input('edit_blog_listing_meta_title');
            $adminSetting->blog_listing_meta_description = $request->input('edit_blog_listing_meta_description');
            $adminSetting->status = $request->input('status');
            $adminSetting->save();

            return response()->json([
                'status' => true,
                'message' => 'Admin setting updated successfully',
                'data' => $adminSetting,
            ]);

        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

     public function updateLogoAndFavicon(Request $request)
    {
        try {

            if($request->hasFile('image')) {
                $randomNumber = mt_rand(1000000000, 9999999999);
                $imagePath = $request->file('image');
                $imageName = $randomNumber . $imagePath->getClientOriginalName();
                $path = $imagePath->storeAs('public/images', $imageName);
             }

            $setting = AdminSettings::where('id', 1)->update([$request->key => $imageName]);

            if ($setting) {

                if($request->key == 'logo'){

                     return response()->json(['status' => true, 'message' => 'Website Logo has been updated succesfully', 'error' => '', 'data' => '']);
                }else {

                     return response()->json(['status' => true, 'message' => 'Website favicon has been updated succesfully', 'error' => '', 'data' => '']);
                }



            } else {

                return response()->json(['status' => true, 'message' => 'There has been error for to adding the blog data', 'error' => '', 'data' => ''],404);

            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getPublicSettings(): JsonResponse
    {
        try {
            $publicSettings = (new AdminSettings)->first();
            return $this->respondWithSuccess($publicSettings);
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function UpdateAdminSettings(Request $request)
    {
        try {

           $setting = AdminSettings::where('id', 1)->update([

                'stripe_test_secret_key' => $request['settings']['stripe_test_secret_key'] ?? null,
                'stripe_test_publish_key' => $request['settings']['stripe_test_publish_key'] ?? null,
                'stripe_live_secret_key' => $request['settings']['stripe_live_secret_key'] ?? null,
                'stripe_live_publish_key' => $request['settings']['stripe_live_publish_key'] ?? null,
                'homepage_meta_title' => $request['settings']['homepage_meta_title'] ?? null,
                'homepage_meta_description' => $request['settings']['homepage_meta_description'] ?? null,
                'jobs_meta_title' => $request['settings']['jobs_meta_title'] ?? null,
                'jobs_meta_description' => $request['settings']['jobs_meta_description'] ?? null,
                'carrer_meta_title' => $request['settings']['carrer_meta_title'] ?? null,
                'carrer_meta_description' => $request['settings']['carrer_meta_description'] ?? null,
                'about_meta_title' => $request['settings']['about_meta_title'] ?? null,
                'about_meta_description' => $request['settings']['about_meta_description'] ?? null,
                'employer_meta_title' => $request['settings']['employer_meta_title'] ?? null,
                'employer_meta_description' => $request['settings']['employer_meta_description'] ?? null,
                'employee_meta_title' => $request['settings']['employee_meta_title'] ?? null,
                'employee_meta_description' => $request['settings']['employee_meta_description'] ?? null,
                'pricing_meta_title' => $request['settings']['pricing_meta_title'] ?? null,
                'pricing_meta_description' => $request['settings']['pricing_meta_description'] ?? null,
                'blog_listing_meta_title' => $request['settings']['blog_listing_meta_title'] ?? null,
                'blog_listing_meta_description' => $request['settings']['blog_listing_meta_description'] ?? null,
                'linkedin_link' => $request['settings']['linkedin_link'] ?? null,
                'instagram_link' => $request['settings']['instagram_link'] ?? null,
                'facebook_link' => $request['settings']['facebook_link'] ?? null,
                'payment_mode' => $request['settings']['payment_mode'] ?? null,
                'twitter_link' => $request['settings']['twitter_link'] ?? null,
                'website_url' => $request['settings']['website_url'] ?? null,
            ]);


            if($setting){

                return response()->json(['status' => true, 'message' => 'Setting data is update succesfully', 'error' => '', 'data' => '']);

            } else {

                return response()->json(['status' => false, 'message' => 'There has been error for to udpating the company data', 'error' => '', 'data' => ''],404);

            }


        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getAdminChartData(Request $request)
    {
        try {
            $time_type = $request->time_type;
            $time_val = $request->time_val;
            if($time_type == 'months'){
                //$dateS = Carbon::now()->startOfMonth()->subMonth($time_val);
                //$dateE = Carbon::now()->startOfMonth();
                $dateS = Carbon::now()->subMonth($time_val);
                $dateE = Carbon::now();
                $data = User::select(
                    'role as role_name',
                    DB::raw('MONTH(created_at) as month, COUNT(*) as count')
                    )
                    ->whereBetween('created_at',[$dateS,$dateE])
                    ->whereIn('role', ['employee', 'employer'])
                    ->where('status', 'active')
                    ->groupBy(DB::raw('MONTH(created_at), role'))
                    ->orderBy('month')
                    ->get();

                $jobs_data = Job::select(DB::raw('MONTH(created_at) as month, COUNT(*) as count'))
                    ->whereBetween('created_at',[$dateS,$dateE])
                    ->where('job_status', 'active')
                    ->groupBy(DB::raw('MONTH(created_at)'))
                    ->orderBy('month')
                    ->get();

                $monthsLabels = [
                    'January', 'February', 'March', 'April', 'May', 'June', 'July',
                    'August', 'September', 'October', 'November', 'December'
                ];
                $months_labels_arr = array();
                $candidate_counts_arr = array();
                $employer_counts_arr = array();
                foreach ($data as $count) {
                    array_push($months_labels_arr, $monthsLabels[$count->month - 1]);
                    if($count->role_name == 'employee'){
                        array_push($candidate_counts_arr, $count->count);
                    } else {
                        array_push($employer_counts_arr, $count->count);
                    }
                }
                $jobs_months_labels_arr = array();
                $active_jobs_counts_arr = array();
                foreach ($jobs_data as $jobs_count) {
                    array_push($jobs_months_labels_arr, $monthsLabels[$jobs_count->month - 1]);
                    array_push($active_jobs_counts_arr, $jobs_count->count);
                }
                // $unique_month = array_unique($months_labels_arr);
                // $new_unique_month = array();
                // foreach($unique_month as $filter_month_values){
                //     array_push($new_unique_month, $filter_month_values);
                // }
                $mergedlabelsArray = array_merge($months_labels_arr, $jobs_months_labels_arr);
                $unique_month = array_unique($mergedlabelsArray);
                $new_unique_month = array();
                foreach($unique_month as $filter_month_values){
                    array_push($new_unique_month, $filter_month_values);
                }
                return response()->json(['status' => true, 'message' => 'Chart data', 'error' => '', 'candidates_count_data' => $candidate_counts_arr, 'employer_count_data' => $employer_counts_arr, 'active_jobs_count_data' => $active_jobs_counts_arr, 'labels_data' => $new_unique_month]);
            } else {
                $data = DB::table(function ($query) use ($time_val) {
                    $query->select(DB::raw('DISTINCT DATE(created_at) as date'))
                        ->from('users')
                        ->where('created_at', '>=', DB::raw('DATE_SUB(NOW(), INTERVAL ' .$time_val. ' DAY)'));
                }, 'date_range')
                ->crossJoin(DB::raw('(
                    SELECT "employee" AS role_name
                    UNION
                    SELECT "employer" AS role_name
                ) AS roles'))
                ->leftJoin('users as users', function ($join) {
                    $join->on('date_range.date', '=', DB::raw('DATE(users.created_at)'))
                        ->on('roles.role_name', '=', 'users.role');
                })
                ->select('date_range.date as date', 'roles.role_name', DB::raw('COALESCE(COUNT(users.id), 0) as count'))
                ->where(function ($query) {
                    $query->where('users.status', '=', 'active')
                        ->orWhereNull('users.status');
                })
                ->groupBy('date_range.date', 'roles.role_name')
                ->get();
<<<<<<< HEAD
                $jobs_data = Job::select(DB::raw('DATE(created_at) as date, COUNT(*) as count'))
                    ->whereDate('created_at', '>=', now()->subDays($time_val))
                    ->where('job_status', 'active')
                    ->groupBy(DB::raw('date'))
                    ->orderBy('date')
                    ->get();
=======

>>>>>>> main
                $months_labels_arr = array();
                $candidate_counts_arr = array();
                $employer_counts_arr = array();
                foreach ($data as $count) {
                    array_push($months_labels_arr, $count->date);
                    if($count->role_name == 'employee'){
                        array_push($candidate_counts_arr, $count->count);
                    } else {
                        array_push($employer_counts_arr, $count->count);
                    }
                }

                $unsort_merge_data = usort($months_labels_arr, function ($a, $b){
                    if ($a < $b)
                        return -1;
                    else if ($a > $b)
                        return 1;
                    else
                        return 0;
                });
                $unique_month = array_unique($months_labels_arr);
                $new_unique_month = array();
                $active_jobs_counts_arr = array();

                foreach($unique_month as $filter_month_values){
                    $jobs_data = Jobs::whereDate('created_at', '=', $filter_month_values)
                    ->where('job_status', 'active')
                    ->count();
                    array_push($active_jobs_counts_arr, $jobs_data);
                    if (in_array($filter_month_values, $unique_month)) {
                        array_push($new_unique_month, $filter_month_values);
                    }
                }
                return response()->json(['status' => true, 'message' => 'Chart data', 'error' => '', 'candidates_count_data' => $candidate_counts_arr, 'employer_count_data' => $employer_counts_arr, 'active_jobs_count_data' => $active_jobs_counts_arr, 'labels_data' => $new_unique_month]);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    function compareByTimeStamp($time1, $time2)
    {
        if (strtotime($time1) < strtotime($time2))
            return 1;
        else if (strtotime($time1) > strtotime($time2))
            return -1;
        else
            return 0;
    }
}
