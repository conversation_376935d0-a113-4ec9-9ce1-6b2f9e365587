<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Location;
use App\Classes\ErrorsClass;
//use JWTAuth;

class LocationController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }
}