<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\Interview;
use App\Classes\ErrorsClass;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Carbon\Carbon;
use App\Mail\InterviewStatusUpdate;
use Illuminate\Support\Facades\Mail;
use DB;
use App\Models\User;
use App\Models\Job;

class InterviewController extends Controller
{
    public function getAllInterviews(Request $request)
    {
        try {
            $interviews = Interview::select('interviews.*', 'jobs.job_title', 'jobs.type_of_position', 'company.company_name')
                ->join('jobs', 'interviews.job_id', '=', 'jobs.id')
                ->join('users', 'jobs.user_id', '=', 'users.id')
                ->join('company', 'interviews.company_id', '=', 'company.id')
                ->where('interviews.interview_status', '!=', 'deleted')
                ->where('users.id', '=', $request->user_id)
                ->orderBy('id', 'DESC')
                ->get();
            //$interviews = Interview::orderByDesc('created_at')->get();
            return response()->json([
                'status' => true,
                'message' => 'All interviews fetched successfully',
                'data' => $interviews,
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getSingleInterviews($id)
    {
        try {
            $single_interview_data = Interview::findOrFail($id);
            return response()->json([
                'status' => true,
                'message' => 'interview data fetched successfully',
                'data' => $single_interview_data,
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getCompanyUserfutureInterviews($userId)
    {
        try {

            // return $userId;


            $interviews = Interview::select('interviews.*', 'jobs.job_title', 'applications.user_id as applicants_user_id', 'users.name as applicants_name')
                ->join('jobs', 'interviews.job_id', '=', 'jobs.id')
                ->leftjoin('applications', 'interviews.applicant_id', '=', 'applications.id')
                ->leftjoin('users', 'applications.user_id', '=', 'users.id')
                ->where('interviews.interview_schedule_date', '>', Carbon::now()->format('Y-m-d'))
                ->where(function ($query) use ($userId) {
                    $query->where('jobs.user_id', $userId) // Match jobs created by the same user_id as the company_id
                        ->orWhere('jobs.user_id', null) // Also allow jobs with no specified user_id
                        ->orWhere('interviews.applicant_id', $userId)
                        ->orWhere('applications.user_id', $userId);
                })
                ->where('interviews.interview_status', 'scheduled')
                ->orderBy('interviews.interview_schedule_date', 'asc')
                ->orderBy('interviews.interview_schedule_from_time', 'asc')
                ->orderBy('interviews.interview_schedule_to_time', 'asc')
                ->get();



            // $interviews = Interview::select('interviews.*', 'jobs.job_title', 'applications.user_id as applicants_user_id', 'users.name as applicants_name')
            //     ->join('jobs', 'interviews.job_id', '=', 'jobs.id')
            //     ->leftjoin('applications', 'interviews.applicant_id', '=', 'applications.id')
            //     ->leftjoin('users', 'applications.user_id', '=', 'users.id')
            //     ->where('interviews.company_id', $companyId)
            //     ->where('interviews.interview_schedule_date', '>', Carbon::now()->format('Y-m-d'))
            //     ->orderBy('interviews.interview_schedule_date', 'asc')
            //     ->orderBy('interviews.interview_schedule_from_time', 'asc')
            //     ->orderBy('interviews.interview_schedule_to_time', 'asc')
            //     ->get();
            return response()->json([
                'status' => true,
                'interviews' => $interviews
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function insertInterview(Request $request)
    {
        try {
            $interview = new Interview();
            $interview->job_id = $request->input('job_id');
            $interview->company_id = $request->input('company_id');
            $interview->applicant_id = $request->input('applicant_id');
            $interview->meeting_link = $request->input('meeting_link');
            $interview->interview_schedule_date = $request->input('interview_schedule_date');
            $interview->interview_schedule_from_time = $request->input('interview_from_time');
            $interview->interview_schedule_to_time = $request->input('interview_to_time');
            $interview->interview_status = $request->input('interview_status', 'scheduled');
            $interview->save();
            $userdata = User::where('id', $request->input('applicant_id'))->first();
            $jobtitle = Job::where('id', $request->input('job_id'))->first();
            $data = [
                'applicant_name' => $userdata->name,
                'interview_schedule_date' => $interview->interview_schedule_date,
                'interview_schedule_from_time' => $interview->interview_schedule_from_time,
                'interview_schedule_to_time' => $interview->interview_schedule_to_time,
                'meeting_link' => $interview->meeting_link,
                'email' => $userdata->email,
                'jobtitle' => $jobtitle->job_title,
            ];
            if ($interview) {
                Mail::send('emails.interview_mail', ["data" => $data], function ($message) use ($data) {
                    $message->to($data['email'])->subject('Scheduled Interview');
                });
            }
            return response()->json([
                'status' => true,
                'message' => 'Interview scheduled successfully',
                'data' => $interview,
            ], 201);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateInterview(Request $request, $interview_id)
    {
        try {
            $interview = Interview::findOrFail($interview_id);
            // $interview = new Interview();
            //$interview->job_id = $request->input('edit_job_id');
            //$interview->company_id = $request->input('edit_company_id');
            //$interview->applicant_id = $request->input('edit_applicant_id');
            //$interview->meeting_link = $request->input('edit_meeting_link');
            // $interview->interview_schedule_date = $request->input('edit_interview_schedule_date');
            // $interview->interview_schedule_from_time = $request->input('edit_interview_schedule_from_time');
            // $interview->interview_schedule_to_time = $request->input('edit_interview_schedule_to_time');
            // $interview->save();
            $Input = [];
            $Input['interview_schedule_date'] = $request->input('edit_interview_schedule_date');
            $Input['interview_schedule_from_time'] = $request->input('edit_interview_schedule_from_time');
            $Input['interview_schedule_to_time'] = $request->input('edit_interview_schedule_to_time');
            $update_data = Interview::where('id', $interview_id)->update($Input);
            if ($update_data) {
                return response()->json([
                    'status' => true,
                    'message' => 'Availibility updated successfully',
                    'data' => $interview,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Availibility not updated successfully',
                    'data' => '',
                ]);
            }


            return response()->json([
                'status' => true,
                'message' => 'Interview updated successfully',
                'data' => $interview,
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateZoomMeetingLink(Request $request, $interview_id)
    {
        try {
            $interview = Interview::findOrFail($interview_id);
            $update_data = Interview::where('id', $interview_id)->update(['meeting_link' => $request->edit_zoom_meeting_link]);
            if ($update_data) {
                return response()->json([
                    'status' => true,
                    'message' => 'Zoom Meeting Link updated successfully',
                    'data' => $interview,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Zoom Meeting Link not updated successfully',
                    'data' => '',
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteInterview($interview_id)
    {
        try {
            $interview = Interview::findOrFail($interview_id);
            $update_data = Interview::where('id', $interview_id)->update(['interview_status' => 'deleted']);
            if ($update_data) {
                return response()->json([
                    'status' => true,
                    'message' => 'Interview deleted successfully',
                    'data' => $interview,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Interview not deleted successfully',
                    'data' => '',
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserInterviewss(Request $request, $id)
    {
        try {
            $interviews = DB::table('interviews')
                ->join('jobs', 'interviews.job_id', '=', 'jobs.id')
                ->join('company', 'interviews.company_id', '=', 'company.id')
                ->where('interviews.applicant_id', $id)
                ->whereIn('interviews.interview_status', ['scheduled', 'accepted', 'rejected'])
                ->orderBy('interviews.id', 'DESC')
                ->select('interviews.*', 'jobs.job_title', 'company.company_name')
                ->get();
            return response()->json(['data' => $interviews, 'status' => true, 'message' => 'Interviews Details Fetched'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }

    }

    public function updateInterviewStatus(Request $request, $id)
    {
        try {
            $interview = Interview::findOrFail($id);

            if ($request->status == 'accepted') {
                $interview->interview_status = 'accepted';
            } elseif ($request->status == 'rejected') {
                $interview->interview_status = 'rejected';
            } else {
                return response()->json(['status' => false, 'message' => 'Invalid status'], 400);
            }

            $interview->save();

            $interviewStatus = $interview->interview_status;

            //employer
            $data = User::select('users.email', 'users.name', 'users.current_position', 'jobs.job_title as job_name')
                ->join('company', 'users.id', 'company.user_id')
                ->join('interviews', 'company.id', 'interviews.company_id')
                ->join('jobs', 'jobs.id', '=', 'interviews.job_id')
                ->first();

            $dataName = $data;

            $applicantEmail = User::select('users.email', 'users.name', 'interviews.interview_schedule_from_time', 'interviews.interview_schedule_date', 'interviews.interview_schedule_to_time')
                ->join('interviews', 'users.id', 'interviews.applicant_id')
                ->first();

            $apllicantName = $applicantEmail;

            Mail::to($data->email)
                ->send(new InterviewStatusUpdate($interviewStatus, $dataName, $apllicantName));

            return response()->json(['status' => true, 'message' => 'Interview status updated successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Unable to update interview status', 'error' => $e->getMessage()], 500);
        }
    }
    public function getApplyJobInterview(Request $request)
    {
        try {
            $job_id = $request->job_id;
            $applicants_id = $request->applicants_id;
            $interviews = DB::table('interviews')
                ->where('job_id', $job_id)
                ->where('applicant_id', $applicants_id)
                ->where('interview_status', 'scheduled')
                ->orderBy('id', 'DESC')
                ->get();
            return response()->json(['data' => $interviews, 'status' => true, 'message' => 'Apply Interviews Details Fetched'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
