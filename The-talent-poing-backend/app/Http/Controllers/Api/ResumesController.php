<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Models\ResumeDownload;
use App\Models\Resumes;
use ZipArchive;
use App\Jobs\ResumeZipJob;
use App\Classes\ErrorsClass;
//use JWTAuth;
use DB;

class ResumesController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getSingleOwnResume($id)
    {
        try {
            $resume = Resumes::where('user_id', $id)->where('status', 'active')->orderByDesc('id')->get();
            $defaultResumeCount = $resume->where('default_resume', 1)->count();
            if (!$resume) {
                return response()->json(['message' => 'Resume not found'], 404);
            }
            return response()->json(['data' => $resume, 'count' => $defaultResumeCount, 'status' => true, 'message' => "Data fetched"], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserDefaultResume($id)
    {
        try {
            $resume = Resumes::where(['user_id' => $id, 'default_resume' => 1, 'status' => 'active'])->orderByDesc('id')->first();
            if (!$resume) {
                return response()->json(['message' => 'Default resume not found'], 404);
            }
            return response()->json(['data' => $resume, 'status' => true, 'message' => "Data fetched"], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getUserResume(Request $request)
    {
        dd($request->all());

        $userId = $request->user_id;

        $selectedUsers = $request->selected_user;

        // Check if there's already a pending download job for this user
        $existingDownload = ResumeDownload::where('user_id', $userId)
            ->where('status', 'pending')
            ->first();

        if ($existingDownload) {
            return response()->json(['message' => 'Your request is still being processed. Please wait.'], 200);
        }

        // Create a new pending entry in the database for the new job
        ResumeDownload::create([
            'user_id' => $userId,
            'file_path' => null,
            'status' => 'pending',
        ]);

        ResumeZipJob::dispatch($userId, $selectedUsers)->onConnection('queue_jobs');

        return response()->json(['message' => 'Resume ZIP job has been started.']);
    }


    public function getDownloadLink(Request $request)
    {
        $userId = $request->user_id;

        // Check if there is a pending download job for this user
        $pendingDownload = ResumeDownload::where('user_id', $userId)
            ->where('status', 'pending')
            ->first();

        if ($pendingDownload) {
            return response()->json(['message' => 'Your download request is still being processed. Please wait.'], 200);
        }

        // Get the latest job for the user, whether it was completed or failed
        $latestDownload = ResumeDownload::where('user_id', $userId)
            ->latest()
            ->first();

        if ($latestDownload) {
            if ($latestDownload->status === 'failed') {
                return response()->json(['message' => 'The latest download attempt has failed. Please try again.'], 200);
            }

            if ($latestDownload->status === 'completed' && Storage::exists("public/{$latestDownload->file_path}")) {
                return response()->download(storage_path("app/public/{$latestDownload->file_path}"))->deleteFileAfterSend(true);
            }
        }

        return response()->json(['message' => 'File not found.'], 404);
    }

    public function saveResume(Request $request)
    {
        try {
            $userId = $request->user_id;
            $resumeCount = Resumes::where('user_id', $userId)->where('status', 'active')->count();

            if ($resumeCount >= 4) {
                return response()->json(['message' => 'You have already uploaded four resumes.', 'Count' => $resumeCount], 400);
            }

            $resume = new Resumes;
            $resume->user_id = $request->user_id;

            // if ($request->hasFile('resume_pdf_path')) {
            //     $randomNumber = mt_rand(1000000000, 9999999999);
            //     $filePath = $request->file('resume_pdf_path');
            //     $fileName = $randomNumber . $filePath->getClientOriginalName();

            //     $path = $filePath->storeAs('public/images/employee/resume', $fileName);
            //     $resume->resume_pdf_path = $fileName;
            // }
            if ($request->hasFile('resume_pdf_path')) {
                $randomNumber = mt_rand(1000000000, 9999999999); // Generate random number
                $filePath = $request->file('resume_pdf_path');

                // Get original file name and replace spaces with underscores
                $originalFileName = $filePath->getClientOriginalName();
                $sanitizedFileName = str_replace(' ', '_', $originalFileName); // Replace spaces with "_"

                // Combine the random number with the sanitized file name
                $fileName = $randomNumber . $sanitizedFileName;

                // Store the file
                $path = $filePath->storeAs('public/images/employee/resume', $fileName);

                // Save the file name to the resume model
                $resume->resume_pdf_path = $fileName;
            }


            $resume->status = 'active';

            $existingDefaultResume = Resumes::where('user_id', $userId)->where('default_resume', 1)->count() > 0;

            $resume->default_resume = $existingDefaultResume ? 0 : 1;

            $resume->save();

            return response()->json(['message' => 'Resume saved successfully', 'data' => $resume], 201);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function deleteResume($id)
    {
        try {
            $resume = Resumes::findOrFail($id);
            $resume->status = 'deleted';
            $resume->save();

            return response()->json(['message' => 'Resume deleted successfully', 'status' => true], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateDefaultResume(Request $request, $id)
    {
        try {
            $resume = Resumes::find($id);

            if (!$resume) {
                return response()->json(['message' => 'Resume not found'], 404);
            }

            // Set default_resume to 0 for all resumes with the same user_id
            Resumes::where('user_id', $resume->user_id)->update(['default_resume' => 0]);

            // Set default_resume to 1 for the specified resume
            $resume->update(['default_resume' => 1]);

            return response()->json(['message' => 'Default resume updated successfully'], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
