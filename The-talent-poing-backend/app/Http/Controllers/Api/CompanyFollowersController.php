<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Src\FileManagement\Domain\File;
use Src\FileManagement\Infrastructure\Resources\FileResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\CompanyFollower;
use App\Classes\ErrorsClass;
use App\Models\Company;
use Carbon\Carbon;

//use JWTAuth;

class CompanyFollowersController extends Controller
{
    public function getCompanyFollowersByCompanyid($company_id)
    {
        try {
            if (is_numeric($company_id)) {
                $company = Company::select('id')->where('id', $company_id)->first();
            } else {
                $company = Company::select('id')->where('company_slug', $company_id)->first();
            }

            $company_followers = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email', 'work_experience.company as candidate_currently_company', 'work_experience.currently_work_here')
                ->join('users', 'company_followers.user_id', '=', 'users.id')
                ->leftjoin('work_experience', 'users.id', '=', 'work_experience.user_id')
                ->join('company', 'company_followers.company_id', '=', 'company.id')
                ->where('company_followers.company_id', $company->id)
                ->where('company_followers.status', 'active')
                ->get();

            $count = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'users.email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->where('company_followers.company_id', $company->id)->where('company_followers.status', 'active')->count();

            $currentDate = Carbon::now();
            $lastWeekStartDate = $currentDate->subWeek();
            $lastWeekEndDate = Carbon::now();
            $company_followers_lastweek = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')
                ->join('users', 'company_followers.user_id', '=', 'users.id')
                ->join('company', 'company_followers.company_id', '=', 'company.id')
                ->whereBetween('company_followers.created_at', [$lastWeekStartDate, $lastWeekEndDate])
                ->where('company_followers.company_id', $company_id)
                ->where('company_followers.status', 'active')
                ->count();
            $last_Week_Start_Date = Carbon::now()->subWeek();
            $lastTwoWeekStartDate = $currentDate->subWeek(1);
            $lastTwoWeekEndDate = $last_Week_Start_Date;
            $company_followers_lasttwoweek = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')
                ->join('users', 'company_followers.user_id', '=', 'users.id')
                ->join('company', 'company_followers.company_id', '=', 'company.id')
                ->whereBetween('company_followers.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                ->where('company_followers.company_id', $company_id)
                ->where('company_followers.status', 'active')
                ->count();

            $company_followers_Impressions_First_Week = $company_followers_lasttwoweek;
            $company_followers_Impressions_Last_Week = $company_followers_lastweek;
            if ($company_followers_Impressions_First_Week > 0 && $company_followers_Impressions_Last_Week > 0) {
                $company_followers_impression_count = (($company_followers_Impressions_First_Week - $company_followers_Impressions_Last_Week) / $company_followers_Impressions_Last_Week) * 100;
                $company_followers_impression_percentage = number_format($company_followers_impression_count, 2) . '%';
            } else {
                $company_followers_impression_percentage = '0%';
            }

            $company_followers_lasttwoweek = $company_followers_lasttwoweek;
            $company_followers_lastweek = $company_followers_lastweek;
            $company_followers_ImpressionPercentage = $company_followers_impression_percentage;

            foreach ($company_followers as $item) {
                if($item->profile_image){
                    $file = File::where('uuid', $item->profile_image)->first();
                    $file = new FileResource($file);
                    $item->profile = $file;
                } else {
                    $item->profile = '';
                }
            }

            return response()->json(['status' => true, 'companies_followers' => $company_followers, 'count' => $count, 'company_followers_lasttwoweek' => $company_followers_lasttwoweek, 'company_followers_lastweek' => $company_followers_lastweek, 'company_followers_ImpressionPercentage' => $company_followers_ImpressionPercentage]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleCompanyFollowersByCompanyid($company_id)
    {
        try {
            $company_followers = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->get();
            $count = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->count();
            return response()->json(['status' => true, 'companies_followers' => $company_followers, 'count' => $count]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleCompaniesUserFollow(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;

            if (is_numeric($company_id)) {
                $company = Company::select('id')->where('id', $company_id)->first();
            } else {
                $company = Company::select('id')->where('company_slug', $company_id)->first();
            }

            $companies_follow_count = CompanyFollower::where('user_id', $user_id)->where('company_id', $company->id)->where('status', 'active')->count();

            $companies_follow = CompanyFollower::where('user_id', $user_id)->where('company_id', $company->id)->where('status', 'active')->first();

            return response()->json(['status' => true, 'companies_follow_count' => $companies_follow_count, 'companies' => $companies_follow]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function createCompanyFollower(Request $request)
    {
        try {
            $company_follower = new CompanyFollower;
            $company_follower->user_id = $request->user_id;
            $company_follower->company_id = $request->company_id;
            $company_follower->status = 'active';
            $company_follower->save();
            return response()->json(['status' => true, 'message' => 'Company followed successfully.']);
        } catch (\Exception $e) {
            \Log::error('Error in createCompanyFollower:', [
                'message' => $e->getMessage(),
                'user_id' => $request->user_id,
                'company_id' => $request->company_id,
            ]);
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function deleteCompanyFollower(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;


            // Use where clause to find the specific company follower
            $company_follower = CompanyFollower::where('user_id', $user_id)
                ->where('company_id', $company_id)
                ->first();

            if (!$company_follower) {
                return response()->json(['status' => false, 'message' => 'Company follower not found']);
            }

            // Delete the company follower
            $company_follower->delete();

            return response()->json(['status' => true, 'message' => 'Company unfollowed successfully.']);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


}
