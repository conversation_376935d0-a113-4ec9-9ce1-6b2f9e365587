<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Languages;
use App\Classes\ErrorsClass;
//use JWTAuth;

class LanguagesController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    function getAllLanguages($user_id)
    {
        try {
            $languages = Languages::where('user_id', $user_id)->where('status', 'active')->orderBy('id', 'desc')->get();
            return response()->json([
                'status' => true,
                'message' => 'All languages retrieved successfully!',
                'data' => $languages
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function getSingleLanguages($id)
    {
        try {
            $languages = Languages::findOrFail($id);
            return response()->json([
                'status' => 'success',
                'message' => 'Languages retrieved successfully!',
                'data' => $languages
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

     public function getSingleUserLanguage($id)
    {
        try {
            $language = Languages::where('user_id', $id)->get();
            if ($language) {
                return response()->json([
                    'status' => true,
                    'data' => $language,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'data' => 'No data found'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function addLanguages(Request $request)
    {
        try {
            $languages = new Languages;
            $languages->user_id = $request->user_id;
            $languages->language = $request->language;
            $languages->proficiency = $request->proficiency;
            $languages->status = 'active';
            $languages->save();
            return response()->json([
                'status' => 'success',
                'message' => 'Languages added successfully!',
                'data' => $languages
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function updateLanguages(Request $request)
    {
        try {
            $languages = Languages::find($request->id);
            $languages->language = $request->language;
            $languages->proficiency = $request->proficiency;
            $languages->save();
            return response()->json([
                'status' => 'success',
                'message' => 'Languages updated successfully!',
                'data' => $languages
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function deletelanguage($id) {
        try {
            $language = Languages::find($id);
            $language->delete();

            return response()->json(['message' => 'language deleted successfully', 'status' => true]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to delete language', 'status' => false]);
        }
    }
    
}