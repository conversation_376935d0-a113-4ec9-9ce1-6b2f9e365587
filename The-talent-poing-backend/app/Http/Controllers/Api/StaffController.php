<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Job;
use App\Models\SavedJobs;
use App\Classes\ErrorsClass;
use App\Models\Applications;
use App\Models\JobsView;
use Carbon\Carbon;
use App\Models\Company;
use Image;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Models\JobFilter;
//use JWTAuth;

class StaffController extends Controller
{


    public function getStaffUserAllJobs(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $job_sort = $request->job_sort;
            if($job_sort){
                $jobs = Job::select('jobs.*', 'countries.country_name', 'company.company_name','company.company_slug','users.name as job_created_name','users.id as job_created_by_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->join('users', 'jobs.user_id', '=', 'users.id')
                ->where('jobs.user_id', $user_id)
                ->where('jobs.job_status', '=', $job_sort)
                ->orderByDesc('id')
                ->get();
                foreach($jobs as $jobs_data){
                    $jobs_id = $jobs_data->id;
                    $jobs_shortlisted_count = Applications::where(['job_id'=>$jobs_id, 'hiring_status' => 'Yes'])->count();
                    $jobs_rejected_count = Applications::where(['job_id'=>$jobs_id, 'hiring_status' => 'No'])->count();
                    $jobs_applicants_count = Applications::where(['job_id'=>$jobs_id])->count();
                    $jobs_views_count = JobsView::where('job_id', $jobs_id)->count();
                    $currentDate = Carbon::now();
                    $lastWeekStartDate = $currentDate->subWeek();
                    $lastWeekEndDate = Carbon::now();
                    $jobViewCountslastweek = JobsView::whereBetween('created_at', [$lastWeekStartDate, $lastWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();
                    $last_Week_Start_Date = Carbon::now()->subWeek();
                    $lastTwoWeekStartDate = $currentDate->subWeek(1);
                    $lastTwoWeekEndDate = $last_Week_Start_Date;
                    //$weekBeforeStart = now()->startOfWeek()->subWeeks(1);
                    //$weekBeforeEnd = now()->endOfWeek()->subWeeks(1);
                    $jobViewCountslasttwoweek = JobsView::whereBetween('created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();

                    $Impressions_First_Week = $jobViewCountslasttwoweek;
                    $Impressions_Last_Week = $jobViewCountslastweek;
                    if($Impressions_First_Week > 0 && $Impressions_Last_Week > 0){
                        $impression_count = (($Impressions_First_Week - $Impressions_Last_Week) / $Impressions_Last_Week) * 100;
                        $impression_percentage = $impression_count.'%';
                    } else {
                        $impression_percentage = '0%';
                    }
                    $jobs_data['shortlisted_jobs_count'] = $jobs_shortlisted_count;
                    $jobs_data['rejected_jobs_count'] = $jobs_rejected_count;
                    $jobs_data['jobs_view_count'] = $jobs_views_count;
                    $jobs_data['jobs_applicants'] = $jobs_applicants_count;
                    $jobs_data['jobViewCountslastweek'] = $jobViewCountslastweek;
                    $jobs_data['jobViewCountslasttwoweek'] = $jobViewCountslasttwoweek;
                    $jobs_data['jobsImpressionpercentage'] = $impression_percentage;
                }
                return response()->json([
                    'status' => true,
                    'message' => 'All jobs retrieved successfully',
                    'data' => $jobs,
                ], 200);
            } else {
                $jobs = Job::select('jobs.*', 'countries.country_name', 'company.company_name','company.company_slug','users.name as job_created_name','users.id as job_created_by_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->join('users', 'jobs.user_id', '=', 'users.id')
                ->where('jobs.user_id', $user_id)
                ->where('jobs.job_status', '!=', 'deleted')
                ->orderByDesc('id')
                ->get();
                foreach($jobs as $jobs_data){
                    $jobs_id = $jobs_data->id;
                    $jobs_shortlisted_count = Applications::where(['job_id'=>$jobs_id, 'hiring_status' => 'Yes'])->count();
                    $jobs_rejected_count = Applications::where(['job_id'=>$jobs_id, 'hiring_status' => 'No'])->count();
                    $jobs_applicants_count = Applications::where(['job_id'=>$jobs_id])->count();
                    $jobs_views_count = JobsView::where('job_id', $jobs_id)->count();
                    $currentDate = Carbon::now();
                    $lastWeekStartDate = $currentDate->subWeek();
                    $lastWeekEndDate = Carbon::now();
                    $jobViewCountslastweek = JobsView::whereBetween('created_at', [$lastWeekStartDate, $lastWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();
                    $last_Week_Start_Date = Carbon::now()->subWeek();
                    $lastTwoWeekStartDate = $currentDate->subWeek(1);
                    $lastTwoWeekEndDate = $last_Week_Start_Date;
                    //$weekBeforeStart = now()->startOfWeek()->subWeeks(1);
                    //$weekBeforeEnd = now()->endOfWeek()->subWeeks(1);
                    $jobViewCountslasttwoweek = JobsView::whereBetween('created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();

                    $Impressions_First_Week = $jobViewCountslasttwoweek;
                    $Impressions_Last_Week = $jobViewCountslastweek;

                    if($Impressions_First_Week > 0 && $Impressions_Last_Week > 0){
                        $impression_count = (($Impressions_First_Week - $Impressions_Last_Week) / $Impressions_Last_Week) * 100;
                        $impression_percentage = $impression_count;
                    } else {
                        $impression_percentage = '0';
                    }

                    $totaljobimpressionn = $jobViewCountslasttwoweek+$jobViewCountslastweek;

                    $jobs_data['shortlisted_jobs_count'] = $jobs_shortlisted_count;
                    $jobs_data['rejected_jobs_count'] = $jobs_rejected_count;
                    $jobs_data['jobs_view_count'] = $jobs_views_count;
                    $jobs_data['jobs_applicants'] = $jobs_applicants_count;
                    $jobs_data['jobViewCountslastweek'] = $jobViewCountslastweek;
                    $jobs_data['jobViewCountslasttwoweek'] = $jobViewCountslasttwoweek;
                    $jobs_data['jobsImpressionpercentage'] = $impression_percentage;
                    $jobs_data['totaljobimpression'] = $totaljobimpressionn;
                }
                return response()->json([
                    'status' => true,
                    'message' => 'All jobs retrieved successfully',
                    'data' => $jobs,
                ], 200);
            }

        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }



    function updateJobByStaff(Request $request, $job_id)
    {
        try {

            if(is_numeric($job_id)){
                $id = $job_id;
            }else {
                $getjobid = Job::select('id')->where('job_slug',$job_id)->first();
                $id = $getjobid->id;
            }

            $jobs = Job::find($id);
            // $jobs->user_id = $request->edit_user_id;
            $jobs->company_id = $request->edit_company_id;
            $jobs->sector_id = null;
            $jobs->job_title = $request->edit_job_title;
            $jobs->job_description = $request->edit_job_description;
            //$jobs->type_of_position = $request->edit_type_of_position;
            $jobs->type_of_position = null;
            $jobs->job_type = $request->edit_job_type;
            $jobs->job_country = $request->edit_job_country;
            $jobs->industry = $request->edit_industry;
            $jobs->experience = $request->edit_experience;
            $jobs->skills_required = $request->edit_skills;
            $jobs->monthly_fixed_salary_currency = $request->edit_monthly_fixed_salary_currency;
            $jobs->monthly_fixed_salary_min = $request->edit_monthly_fixed_min_salary;
            $jobs->monthly_fixed_salary_max = $request->edit_monthly_fixed_max_salary;
            $jobs->available_vacancies = $request->edit_available_vacancies;
            $jobs->deadline = $request->edit_deadline;
            $jobs->is_featured = $request->edit_is_featured;
            if($request->edit_hide_employer_details == 'on'){
                $jobs->hide_employer_details = '1';
            } else {
                $jobs->hide_employer_details = '0';
            }
            $jobs->save();

            return response()->json(['message' => 'Jobs updated successfully', 'status' => true], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
