<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpKernel\Exception\HttpException;


use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;


class BlogCategoryController extends Controller
{
    public function getAllBlogCategories(Request $request)
    {
        try {
            $blogs = BlogCategory::where('status', 'active')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'status' => true,
                'message' => 'Blogs fetched successfully',
                'data' => $blogs
            ], 200);
        }  catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
