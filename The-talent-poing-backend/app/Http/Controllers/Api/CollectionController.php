<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\Collection;
use Src\EmployerManagement\Infrastructure\Resources\EmployeeResource;
use App\Models\User;
use App\Models\CollectionUsers;

class CollectionController extends Controller
{


    public function getCollectionByUserId(Request $request)
    {
        // Validate the input data
        $request->validate([
            'user_id' => 'required|integer', // Ensure user_id is provided and is an integer
        ]);

        // Get the user ID from the request
        $userId = (int)$request->user_id;

        // Find collections created by the given user ID, along with associated user IDs
        $collections = Collection::with('users')->where('created_by', $userId)->get();

        // Check if any collections were found
        if ($collections->isEmpty()) {
            return response()->json(['message' => 'No collections found for this user.'], 404);
        }

        // Prepare the response to include user IDs in each collection
        $response = $collections->map(function ($collection) {
            return [
                'id' => $collection->id,
                'name' => $collection->name,
                'slug' => $collection->slug,
                'created_by' => $collection->created_by,
                'created_at' => $collection->created_at,
                'updated_at' => $collection->updated_at,
                'user_ids' => $collection->users->pluck('id'), // Get user IDs from the pivot table
            ];
        });

        return response()->json(['collections' => $response], 200);
    }


    public function store(Request $request)
    {
        $collectionId = '';
        // Validate the input data
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Store the collection name
        $name = $request->name;


        if ($request->collection_id == '') {
            // Generate a slug from the collection name
            $slug = Str::slug($name, '-');

            // Check if the slug already exists in the collections table
            $existingSlugCount = Collection::where('slug', $slug)->count();

            // If the collection name (slug) already exists, return an error response
            if ($existingSlugCount > 0) {
                return response()->json(['message' => 'You cannot use the same collection name. Please choose a different name.'], 422);
            }

            // Create the collection with the name, slug, and employer's ID
            $collection = Collection::create([
                'name' => $name,
                'slug' => $slug,
                'created_by' => $request->id,  // Store the logged-in employer's ID
            ]);

            $collectionId = $collection->id;
        } else {
            $collectionId = $request->collection_id;
        }

        // Initialize an array to store the user IDs added to the collection
        $addedUserIds = [];



        // Check if user_ids is present and has length greater than 0
        if ($request->has('user_ids') && count($request->user_ids) > 0) {
            // Prepare the data for the pivot table with the created_by field
            foreach ($request->user_ids as $userId) {
                $collection_user = CollectionUsers::create([
                    'collection_id' => $collectionId,
                    'user_id' => $userId
                ]);

                // Add the user ID to the array
                $addedUserIds[] = $userId;
            }
        }
        // return response()->json(['message' => 'Collection created successfully'], 200);
        return response()->json([
            'message' => 'Collection created successfully',
            'collection_id' => $collectionId,
            'user_ids' => $addedUserIds
        ], 200);
    }

    public function usersCollection(Request $request)
    {
        try {
            $employees = (new User)
                ->with('company', 'company.logo', 'country', 'resumes', 'languages', 'work_experience', 'education', 'portfolio')
                ->where('role', 'employee');

            $users_id = CollectionUsers::where('collection_id', $request->collection_id)->pluck('user_id');

            if ($users_id->isNotEmpty()) {
                $employees->whereIn('id', $users_id);
            } else {
                return response()->json(['message' => 'No data found'], 404);
            }

            $employees->where('status', 'active');

            $employees->orderBy('id', 'desc');

            return EmployeeResource::collection($employees->paginate(10));
        } catch (\Exception $e) {
            // Handle any unexpected errors and return a 500 response
            return response()->json([
                'message' => 'An error occurred while fetching data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
