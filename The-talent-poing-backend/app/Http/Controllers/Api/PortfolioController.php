<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Portfolio;
use App\Classes\ErrorsClass;
//use JWTAuth;

class PortfolioController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    //  public function getSinglePortfolio($id) {
    //     try {
    //         $portfolio = Portfolio::where('user_id', $id)->where('status', '!=', 'deleted')->get();
    //         if (!$portfolio) {
    //             return response()->json(['message' => 'Portfolio not found'], 404);
    //         }
    //         return response()->json(['data' => $portfolio,'status'=>true,'message'=>'portfolio fetched'], 200);
    //     } catch (\Exception $e) {
    //         return response()->json(['message' => 'Failed to fetch portfolio', 'error' => $e->getMessage()], 400);
    //     }
    // }

    public function getSinglePortfolio($id)
    {
        try {
            $portfolio = Portfolio::where('user_id', $id)->where('status','active')->get();
            if ($portfolio) {
                return response()->json([
                    'status' => true,
                    'data' => $portfolio,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'data' => 'No data found'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSinglePortfolioID($id) {
        try {
            $portfolio = Portfolio::where('id', $id)->where('status', '!=', 'deleted')->first();
            if (!$portfolio) {
                return response()->json(['status'=>false, 'message' => 'Portfolio not found'], 404);
            }
            return response()->json(['status'=>true, 'data' => $portfolio], 200);
        }catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    // public function addPortfolio(Request $request) {
    //     try {
    //         $portfolio = new Portfolio;
    //         $portfolio->user_id = $request->user_id;
    //         $portfolio->title = $request->title;
    //         $portfolio->portfolio_link = $request->portfolio_link;
    //         $portfolio->start_date = $request->start_date;
    //         $portfolio->end_date = $request->end_date;
    //         $portfolio->present = $request->present;
    //         $portfolio->description = $request->description;
    //         // $portfolio->status = $request->status;
    //         $portfolio->save();

    //         return response()->json(['message' => 'Portfolio added successfully', 'data' => $portfolio], 200);
    //     } catch (\Exception $e) {
    //         return response()->json(['message' => 'Failed to add portfolio', 'error' => $e->getMessage()], 400);
    //     }
    // }

    public function addPortfolio(Request $request)
    {
       // try {
            $portfolio = new Portfolio();
            $portfolio->user_id = $request->user_id;
            $portfolio->title = $request->title;
            $portfolio->portfolio_link = $request->portfolio_link;
            $portfolio->start_date = $request->start_date;
            $portfolio->end_date = $request->end_date;
            $portfolio->present = $request->present;
            $portfolio->description = $request->description;
            $portfolio->save();
            return response()->json([
                'status' => 'success',
                'message' => 'Portfilio added successfully'
            ]);
      //  } catch (\Exception $e) {
      //      throw new HttpException(500, $e->getMessage());
      //  }
    }

     public function updatePortfolio(Request $request)
    {
        try{
            $portfolio = Portfolio::find($request->id);
            $portfolio->user_id = $request->user_id;
            $portfolio->title = $request->title;
            $portfolio->portfolio_link = $request->portfolio_link;
            $portfolio->start_date = $request->start_date;
            $portfolio->end_date = $request->end_date;
            $portfolio->present = $request->present;
            $portfolio->description = $request->description;
            $portfolio->status = $request->status;
            $portfolio->save();
            if($portfolio){
                return response()->json(['status'=>true,'message'=>'Portfolio updated Successfully!','error'=>'','data'=>''], 200);
            } else {
                return response()->json(['status'=>false,'message'=>'Portfolio not updated Successfully!','error'=>'','data'=>''], 400);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    function deletePortfolio($id) {
        try {
            $portfolio = Portfolio::find($id);
            $portfolio->delete();

            return response()->json(['message' => 'Portfolio deleted successfully', 'status' => true]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

}