<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\WorkExperience;
use App\Classes\ErrorsClass;
use Symfony\Component\HttpKernel\Exception\HttpException;
//use JWTAuth;

class WorkExperienceController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }


    public function getSingleWorkExperience($id)
    {
        try{
            //$workexperience = WorkExperience::find($id);
            $workexperience = WorkExperience::where('user_id', $id)->where('status','active')->get();
            if($workexperience){
                return response()->json(['status'=>true,'message'=>'Work Experience details','error'=>'','data'=>$workexperience], 200);
            } else {
                return response()->json(['status'=>false,'message'=>'Work Experience details not found','error'=>'','data'=>''], 400);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleWorkExperienceID($id)
    {
        try {
            $workexperience = WorkExperience::where('id', $id)->first();
            if ($workexperience) {
                return response()->json([
                    'status' => true,
                    'workexperience' => $workexperience,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'workexperience' => 'No data found'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

     public function updateWorkExperience(Request $request)
    {
        try{
            $workexperience = WorkExperience::find($request->id);
            $workexperience->title = $request->title;
            $workexperience->company = $request->company;
            $workexperience->start_date = $request->start_date;
            $workexperience->end_date = $request->end_date;
            $workexperience->currently_work_here = $request->currently_work_here;
            $workexperience->description = $request->description;
            $updateworkexperience = $workexperience->save();
            if($updateworkexperience){
                return response()->json(['status'=>true,'message'=>'Work Experience updated Successfully!','error'=>'','data'=>''], 200);
            } else {
                return response()->json(['status'=>false,'message'=>'Work Experience not updated Successfully!','error'=>'','data'=>''], 400);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function createWorkExperience(Request $request)
    {
       try{
            $workexperience = new WorkExperience();
            $workexperience->user_id = $request->user_id;
            $workexperience->title = $request->title;
            $workexperience->company = $request->company;
            $workexperience->start_date = $request->start_date;
            $workexperience->end_date = $request->end_date;
            $workexperience->currently_work_here = $request->currently_work_here;
            $workexperience->description = $request->description;
            // 'status' => 'pending';
            $workexperience->save();
            return response()->json([
                'status' => 'success',
                'message' => 'Work Experience Added successfully'
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try{
            $workexperience = WorkExperience::find($id);
            $workexperience->title = $request->edit_title;
            $workexperience->company = $request->edit_company;
            $workexperience->start_date = $request->edit_start_date;
            $workexperience->end_date = $request->edit_end_date;
            $workexperience->currently_work_here = $request->edit_currently_work_here;
            $workexperience->description = $request->edit_description;
            $updateworkexperience = $workexperience->save();
            if($updateworkexperience){
                return response()->json(['status'=>true,'message'=>'Work Experience updated Successfully!','error'=>'','data'=>''], 200);
            } else {
                return response()->json(['status'=>false,'message'=>'Work Experience not updated Successfully!','error'=>'','data'=>''], 400);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function destroyWorkExperience($id)
    {
         try {
            $workexperience = WorkExperience::find($id);
            $workexperience->delete();

            return response()->json(['message' => 'Work Experience deleted successfully', 'status' => true]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
