<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\JobsView;
use App\Classes\ErrorsClass;
use App\Models\Job;
//use JWTAuth;

class JobsViewController extends Controller
{
    public function getJobViewUserViewCount(Request $request)
    {   
        try {
            $user_id = $request->user_id;
            $job_id = $request->job_id;

            $jobs = Job::select('id')->where('id', $job_id)->first();

            $views_count = JobsView::where('user_id', $user_id)->where('job_id', $jobs->id)->where('status', 'active')->count();
            return response()->json(['status' => true, 'user_views_count' => $views_count]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getJobAllUserViewCount(Request $request)
    {
        try {
            $job_id = $request->job_id;
            $count = JobsView::where('job_id', $job_id)
                ->where('status', 'active')
                ->count();
            return response()->json(['status' => true, 'job_views_count' => $count]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function insertJobView(Request $request)
    {
        try {
            $jobsView = new JobsView;
            $jobsView->job_id = $request->job_id;
            $jobsView->user_id = $request->user_id;
            $jobsView->status = 'active';
            $jobsView->save();
            return response()->json(['status' => true, 'message' => 'Job view inserted successfully']);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getJobViewUsers(Request $request)
    {
        $viewjobs =  JobsView::select('users.email', 'users.name', 'users.slug', 'users.role')
            ->join('users', 'jobs_view.user_id', '=', 'users.id')
            ->where('job_id', $request->job_id)
            ->distinct()
            ->get();

        return response()->json(['status' => true, 'data' => $viewjobs]);
    }
}
