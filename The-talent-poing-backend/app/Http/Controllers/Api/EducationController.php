<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Education;
use App\Classes\ErrorsClass;
//use JWTAuth;

class EducationController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getSingleEducation($id)
    {
        try {
            $education = Education::where('user_id', $id)->where('status','active')->get();
            if ($education) {
                return response()->json([
                    'status' => true,
                    'education' => $education,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'education' => 'No data found'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function createEducation(Request $request)
    {
        try {
            $education = new Education();
            $education->user_id = $request->user_id;
            $education->education_title = $request->education_title;
            $education->degree = $request->degree;
            $education->start_date = $request->start_date;
            $education->end_date = $request->end_date;
            $education->currently_study_here = $request->currently_study_here;
            $education->your_score = $request->your_score;
            $education->max_score = $request->max_score;
            $education->save();
            return response()->json([
                'status' => 'success',
                'message' => 'Education Added successfully'
            ]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

     public function updateEducation(Request $request)
    {
        try {
            $education = Education::find($request->id);
            $education->education_title = $request->education_title;
            $education->degree = $request->degree;
            $education->start_date = $request->start_date;
            $education->end_date = $request->end_date;
            $education->currently_study_here = $request->currently_study_here;
            $education->your_score = $request->your_score;
            $education->max_score = $request->max_score;
            $updateeducation = $education->save();
            if ($updateeducation) {
                return response()->json(['status' => true, 'message' => 'Education updated Successfully!', 'error' => '', 'data' => ''], 200);
            } else {
                return response()->json(['status' => false, 'message' => 'Education not updated Successfully!', 'error' => '', 'data' => ''], 400);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

     public function getSingleEducationbyID($id)
    {
        try {
            $education = Education::where('id', $id)->first();
            if ($education) {
                return response()->json([
                    'status' => true,
                    'education' => $education,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'education' => 'No data found'
                ]);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteEducation($id)
    {
        try {
            $education = Education::find($id);
            $education->delete();

            return response()->json(['message' => 'Education deleted successfully', 'status' => true]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to delete Education', 'status' => false]);
        }
    }
}
