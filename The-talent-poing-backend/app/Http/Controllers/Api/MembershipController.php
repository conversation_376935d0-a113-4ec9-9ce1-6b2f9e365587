<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Membership;
use App\Classes\ErrorsClass;
use DB;
//use JWTAuth;

class MembershipController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getAllMemeberShipDetails($user_id){

         try {

              $membership = DB::table('membership')->select('expire_at', 'plan_id','purchase_at')->where('user_id', $user_id)->latest('id')->first();


              if ($membership) {
                return response()->json([
                    'status' => true,
                    'membership' => $membership,
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'membership' => '',
                ]);
            }

        }catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}