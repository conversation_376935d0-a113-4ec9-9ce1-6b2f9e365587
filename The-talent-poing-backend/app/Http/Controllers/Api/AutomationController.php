<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DataPoint;
use App\Models\User;
use App\Models\WorkFlow;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Jobs\SendEmailAutomationJob;
use App\Models\EmailSent;
use App\Models\EmailTemplate;

class AutomationController extends Controller
{
    // public function showOverview(Request $request)
    // {
    //     try {
    //         // Fetch the required statistics
    //         $totalEmails = DB::table('email_sents')->count();
    //         $deliveredEmails = DB::table('email_sents')->where('is_sent', 1)->count();
    //         $openedEmails = DB::table('email_sents')->where('is_viewed', 1)->count();
    //         $spamEmails = DB::table('email_sents')->where('is_spam', 1)->count();

    //         $deliveredPercentage = $totalEmails > 0 ? round(($deliveredEmails / $totalEmails) * 100, 2) : 0;
    //         $openedPercentage = $deliveredEmails > 0 ? round(($openedEmails / $deliveredEmails) * 100, 2) : 0;
    //         $spamPercentage = $totalEmails > 0 ? round(($spamEmails / $totalEmails) * 100, 2) : 0;

    //         // Return the response
    //         return response()->json([
    //             'total_emails' => $totalEmails,
    //             'delivered' => [
    //                 'count' => $deliveredEmails,
    //                 'percentage' => $deliveredPercentage,
    //             ],
    //             'opened' => [
    //                 'count' => $openedEmails,
    //                 'percentage' => $openedPercentage,
    //             ],
    //             'spam' => [
    //                 'count' => $spamEmails,
    //                 'percentage' => $spamPercentage,
    //             ],
    //             'message' => 'Statistics fetched successfully.',
    //         ], 200);
    //     } catch (\Exception $e) {
    //         return response()->json([
    //             'error' => 'An error occurred while fetching statistics.',
    //             'message' => $e->getMessage(),
    //         ], 500);
    //     }
    // }

    public function showOverview(Request $request)
    {
        try {
            // Get the filters from the request, with defaults for year and date range
            $year = $request->input('year', Carbon::now()->year);
            $startDate = $request->input('start_date', Carbon::now()->startOfYear()->format('Y-m-d'));
            $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

            // Ensure start date is before or equal to end date
            if (Carbon::parse($startDate)->gt(Carbon::parse($endDate))) {
                return response()->json(['error' => 'Start date cannot be later than end date.'], 400);
            }

            // Fetch overall statistics for the specified year and date range
            $totalEmails = DB::table('email_sents')
                ->whereYear('created_at', $year)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $deliveredEmails = DB::table('email_sents')
                ->whereYear('created_at', $year)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('is_sent', 1)
                ->count();

            $openedEmails = DB::table('email_sents')
                ->whereYear('created_at', $year)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('is_viewed', 1)
                ->count();

            $spamEmails = DB::table('email_sents')
                ->whereYear('created_at', $year)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('is_spam', 1)
                ->count();

            // Percentages
            $deliveredPercentage = $totalEmails > 0 ? round(($deliveredEmails / $totalEmails) * 100, 2) : 0;
            $openedPercentage = $deliveredEmails > 0 ? round(($openedEmails / $deliveredEmails) * 100, 2) : 0;
            $spamPercentage = $totalEmails > 0 ? round(($spamEmails / $totalEmails) * 100, 2) : 0;

            // Fetch detailed data grouped by date
            $detailedGraphData = DB::table('email_sents')
                ->select(
                    DB::raw("DATE(created_at) as date"),
                    DB::raw("SUM(CASE WHEN is_sent = 1 THEN 1 ELSE 0 END) as delivered"),
                    DB::raw("SUM(CASE WHEN is_viewed = 1 THEN 1 ELSE 0 END) as opened"),
                    DB::raw("SUM(CASE WHEN is_spam = 1 THEN 1 ELSE 0 END) as spam")
                )
                ->whereYear('created_at', $year)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy(DB::raw("DATE(created_at)"))
                ->orderBy(DB::raw("DATE(created_at)"))
                ->get();

            // Normalize the data to a structured format for the graph
            $normalizedGraphData = $detailedGraphData->map(function ($data) {
                return [
                    'date' => $data->date,
                    'delivered' => $data->delivered,
                    'opened' => $data->opened,
                    'spam' => $data->spam,
                ];
            });

            // Additional data for total emails sent, candidates and employers, and active workflows
            $totalCandidates = User::where('role', 'employee')->count();
            $totalEmployers = User::where('role', 'employer')->count();

            $totalActiveWorkflows = WorkFlow::where('status', 1)->count();

            // Return response
            return response()->json([
                'year' => $year,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_emails' => $totalEmails,
                'delivered' => [
                    'count' => $deliveredEmails,
                    'percentage' => $deliveredPercentage,
                ],
                'opened' => [
                    'count' => $openedEmails,
                    'percentage' => $openedPercentage,
                ],
                'spam' => [
                    'count' => $spamEmails,
                    'percentage' => $spamPercentage,
                ],
                'graph_data' => $normalizedGraphData,
                'total_candidates' => $totalCandidates,
                'total_employers' => $totalEmployers,
                'total_active_workflows' => $totalActiveWorkflows,
                'message' => 'Statistics with date filter fetched successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while fetching statistics and graph data.',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    // public function showContactList(Request $request)
    // {
    //     // Validate the type parameter
    //     $request->validate([
    //         'type' => 'required|in:employee,employer',
    //     ]);

    //     // Get the type from the request
    //     $type = $request->input('type');

    //     // Fetch users based on the type
    //     $users = User::where('role', $type)
    //         ->select('name', 'email', 'where_currently_based', 'created_at')
    //         ->with('country') // Assuming you want to get the country name or details
    //         ->get();

    //     // Map the users to include the location name if needed
    //     $contactList = $users->map(function ($user) {
    //         return [
    //             'name' => $user->name,
    //             'email' => $user->email,
    //             'location' => optional($user->country)->country_name ?? 'N/A', // Assuming 'country_name' is the field in the Country model
    //             'created_at' => $user->created_at,
    //         ];
    //     });

    //     // Check if the contact list is empty
    //     if ($contactList->isEmpty()) {
    //         return response()->json([
    //             'status' => 'success',
    //             'message' => 'No contacts found for the specified type.',
    //             'data' => []
    //         ], 200);
    //     }

    //     // Return the response
    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Contact list retrieved successfully.',
    //         'data' => $contactList
    //     ], 200);
    // }


    //Contacts Section

    public function showContactList(Request $request)
    {
        // Validate the type, name, and location parameters
        $request->validate([
            'type' => 'required|in:employee,employer',
            'name' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1', // Optional items per page
            'page' => 'nullable|integer|min:1', // Optional page number
            'sort_by' => 'nullable|in:name,email,where_currently_based,created_at', // Supported sorting fields
            'sort_order' => 'nullable|in:asc,desc',
        ]);

        // Get the type, name, and location from the request
        $type = $request->input('type');
        $name = $request->input('name');
        $location = $request->input('location');
        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $page = $request->input('page', 1); // Default to the first page
        $sortBy = $request->input('sort_by', 'created_at'); // Default sorting by created_at
        $sortOrder = $request->input('sort_order', 'asc');

        // Start building the query
        $query = User::where('role', $type)
            ->select('id', 'name', 'email', 'where_currently_based', 'created_at')
            ->with('country'); // Assuming you want to get the country name or details

        // Apply the name filter if provided
        if ($name) {
            $query->where('name', 'LIKE', '%' . $name . '%');
        }

        // Apply the location filter if provided
        if ($location) {
            $query->where('where_currently_based', 'LIKE', '%' . $location . '%');
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        // Execute the query
        $users = $query->paginate($perPage, ['*'], 'page', $page);

        // Map the users to include the location name if needed
        $contactList = $users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'location' => optional($user->country)->country_name ?? 'N/A', // Assuming 'country_name' is the field in the Country model
                'created_at' => $user->created_at,
            ];
        });

        // Check if the contact list is empty
        if ($contactList->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'message' => 'No contacts found for the specified criteria.',
                'data' => [],
                'pagination' => [
                    'total' => $users->total(),
                    'current_page' => $users->currentPage(),
                    'last_page' => $users->lastPage(),
                    'per_page' => $users->perPage(),
                ],
            ], 200);
        }

        // Return the response
        return response()->json([
            'status' => 'success',
            'message' => 'Contact list retrieved successfully.',
            'data' => $contactList,
            'pagination' => [
                'total' => $users->total(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
            ],
        ], 200);
    }

    public function exportContacts(Request $request)
    {
        // Validate input parameters
        $validated = $request->validate([
            'contact_type' => 'nullable|string|in:all,candidates,employers', // 'all', 'candidates', 'employers'
            'location' => 'nullable|string', // Single value or null
        ]);

        $contactType = $validated['contact_type'] ?? 'all'; // Default to 'all'
        $location = $validated['location'] ?? null; // Default to no location filter

        // Build the query
        $query = User::query();

        // Apply filters for contact type
        if ($contactType === 'candidates') {
            $query->where('role', 'employee');
        } elseif ($contactType === 'employers') {
            $query->where('role', 'employer');
        } elseif ($contactType === 'all') {
            $query->whereIn('role', ['employee', 'employer']); // Include both
        }

        // Apply filters for specific location if provided
        if (!empty($location) && $location !== 'all') {
            $query->where('where_currently_based', $location);
        }

        // Fetch filtered data
        $users = $query->get(['id', 'name', 'email', 'role', 'where_currently_based']);

        // If no data found
        if ($users->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'message' => 'No contacts found.',
            ], 200);
        }

        // Generate CSV content
        $fileName = 'contacts_export_' . now()->format('Ymd_His') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$fileName\"",
        ];

        $csvContent = $this->generateCsv($users);

        return response($csvContent, 200, $headers);
    }

    private function generateCsv($data)
    {
        $output = fopen('php://temp', 'r+');
        fputcsv($output, ['ID', 'Name', 'Email', 'Role', 'Location']);

        foreach ($data as $user) {
            fputcsv($output, [
                $user->id,
                $user->name,
                $user->email,
                ucfirst($user->role), // Capitalize role
                $user->where_currently_based, // Display location
            ]);
        }

        rewind($output);
        return stream_get_contents($output);
    }


    //Datapoint Section
    public function showDataPoints(Request $request)
    {
        // Validate the request parameters
        $validated = $request->validate([
            'user_type' => 'nullable|string', // Can be 1, 2, or comma-separated
            'data_type' => 'nullable|string|max:255',
            'name' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1', // Items per page for pagination
            'page' => 'nullable|integer|min:1', // Current page number for pagination
        ]);

        // Extract parameters
        $userType = $validated['user_type'] ?? null;
        $dataType = $validated['data_type'] ?? null;
        $name = $validated['name'] ?? null;
        $perPage = $validated['per_page'] ?? 10; // Default items per page
        $page = $validated['page'] ?? 1; // Default to first page

        // Build the query
        $query = DataPoint::query();

        // Apply user_type filter if provided
        if ($userType) {
            $query->where(function ($q) use ($userType) {
                $q->where('user_type', $userType) // Match exact user_type
                    ->orWhere('user_type', 'LIKE', "%{$userType}%"); // Match within a comma-separated list
            });
        }

        // Apply data_type filter if provided
        if ($dataType) {
            $query->where('data_type', $dataType);
        }

        // Apply name filter if provided
        if ($name) {
            $query->where('data_point_name', 'LIKE', "%{$name}%");
        }

        // Fetch data with pagination
        $data = $query->paginate($perPage, ['*'], 'page', $page);

        // Check if data is not empty
        if ($data->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'message' => 'No data points found.',
                'data' => [],
                'pagination' => [
                    'total' => $data->total(),
                    'current_page' => $data->currentPage(),
                    'last_page' => $data->lastPage(),
                    'per_page' => $data->perPage(),
                ],
            ], 200);
        }

        // Return the filtered data points in a JSON response
        return response()->json([
            'status' => 'success',
            'message' => 'Data points retrieved successfully.',
            'data' => $data->items(),
            'pagination' => [
                'total' => $data->total(),
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
            ],
        ], 200);
    }



    //Workflow Section
    public function listWorkFlow(): JsonResponse
    {
        try {
            // Fetch all workflows from the database
            $data = WorkFlow::all();

            // Return a successful response with the data
            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => 'Workflows retrieved successfully.'
            ], 200);
        } catch (\Exception $e) {
            // Handle any exceptions that may occur
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving workflows.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // public function saveWorkFlow(Request $request)
    // {
    //     // dd(now());

    //     // Validate the incoming request data
    //     $validator = Validator::make($request->all(), [
    //         'name' => 'nullable|string|max:255',
    //         'instance' => 'nullable|string|max:255',
    //         'parent_id' => 'nullable|integer|exists:work_flows,id',
    //         'condition_data_point' => 'nullable|integer',
    //         'condition_status' => 'nullable|integer',
    //         'contacts' => 'nullable|string|max:255',
    //         'segment' => 'nullable|integer',
    //         'segment_condition' => 'nullable|string|max:255',
    //         'segment_name' => 'nullable|string|max:255',
    //         'segment_value' => 'nullable|string|max:255',
    //         'frequency_value' => 'nullable|string|max:255',
    //         'frequency_period' => 'nullable|string|max:255',
    //         'frequency_time' => 'nullable|string|max:255',
    //         'AM_PM' => 'nullable|string|max:2',
    //         'start_date' => 'nullable|date',
    //         'start_time' => 'nullable|string|max:5',
    //         'email_sent' => 'nullable|string|max:255',
    //         'status' => 'nullable|integer|in:0,1,2',
    //         'execution_type' => 'required|string|in:immediate,delay,specific', // Ensure execution type is provided
    //         'delay_value' => 'nullable|integer', // For delay
    //         'delay_unit' => 'nullable|string|in:minutes,hours', // For delay
    //         'email_template_id' => 'nullable|string', // For delay
    //     ]);

    //     // Check if validation fails
    //     if ($validator->fails()) {
    //         return response()->json([
    //             'success' => false,
    //             'errors' => $validator->errors(),
    //         ], 422);
    //     }


    //     $workflow = WorkFlow::create($request->all());
    //     // dd(2);

    //     // Check conditions after saving
    //     $contacts = explode(',', $workflow->contacts); // Assuming contacts are stored as '1,2'
    //     // Define the role mapping
    //     $roleMapping = [
    //         1 => 'employee',
    //         2 => 'employer',
    //     ];

    //     // Map the numeric values to their corresponding roles
    //     $roles = array_map(function ($contact) use ($roleMapping) {
    //         return $roleMapping[(int)$contact] ?? null; // Convert to int and get the role, default to null if not found
    //     }, $contacts);

    //     // Filter out any null values (in case of invalid mappings)
    //     $roles = array_filter($roles);
    //     $users = User::whereIn('role', $roles); //->get(); // Fetch users based on roles

    //     // Apply condition_data_point logic
    //     if ($request->condition_data_point) {
    //         $dataPoint = DataPoint::find($request->condition_data_point);

    //         if ($dataPoint) {
    //             if ($dataPoint->relation_name) {
    //                 // Relation-based condition
    //                 if ($request->condition_status == 0) {
    //                     // False: Relation does not exist
    //                     $users->whereDoesntHave($dataPoint->relation_name);
    //                 } else {
    //                     // True: Relation exists
    //                     $users->whereHas($dataPoint->relation_name);
    //                 }
    //             } elseif ($dataPoint->column_name === 'skills') {
    //                 // Custom logic for `skills`
    //                 if ($request->condition_status == 0) {
    //                     // False: Skills are empty
    //                     $users->whereRaw("skills IS NULL OR skills = ''");
    //                 } else {
    //                     // True: Skills are not empty
    //                     $users->whereRaw("skills IS NOT NULL AND skills != ''");
    //                 }
    //             } else {
    //                 // Column-based condition
    //                 $users->where(function ($query) use ($dataPoint, $request) {
    //                     if ($request->condition_status == 0) {
    //                         // False: Column is null or empty
    //                         $query->whereNull($dataPoint->column_name)
    //                             ->orWhere($dataPoint->column_name, '');
    //                     } else {
    //                         // True: Column is not null or not empty
    //                         $query->whereNotNull($dataPoint->column_name)
    //                             ->where($dataPoint->column_name, '!=', '');
    //                     }
    //                 });
    //             }
    //         }
    //     }

    //     $filteredUsers = $users->get(); // Apply all filters
    //     // dd($filteredUsers);

    //     // Determine when to dispatch the job based on execution type
    //     $dispatchTime = null;

    //     switch ($request->execution_type) {
    //         case 'immediate':
    //             // Dispatch immediately
    //             $dispatchTime = now();
    //             break;

    //         case 'delay':
    //             // Calculate delay time
    //             $delayValue = $request->delay_value;
    //             $delayUnit = $request->delay_unit;

    //             // Create a Carbon instance for the delay
    //             $dispatchTime = now()->add($delayValue, $delayUnit);
    //             break;

    //         case 'specific':
    //             // Calculate specific start time
    //             $startDateTime = Carbon::parse($request->start_date . ' ' . $request->start_time . ' ' . $request->start_AM_PM);
    //             $dispatchTime = $startDateTime;
    //             break;
    //     }

    //     // // Dispatch the job for the first time
    //     // SendEmailAutomationJob::dispatch($users, $workflow)->delay($dispatchTime);

    //     // // Handle repetition if frequency is set
    //     // if ($request->frequency_value && $request->frequency_period) {
    //     //     // Calculate the next run time based on frequency
    //     //     $frequencyValue = $request->frequency_value;
    //     //     $frequencyPeriod = $request->frequency_period;

    //     //     // Calculate the frequency time
    //     //     $frequencyTime = Carbon::createFromFormat('h:i A', $request->frequency_time . ' ' . $request->AM_PM);

    //     //     // Set the time for the next run
    //     //     $nextRunTime = $dispatchTime->copy()->setTime($frequencyTime->hour, $frequencyTime->minute)->add($frequencyValue, $frequencyPeriod);

    //     //     // If the next run time is in the past, add the frequency period again
    //     //     if ($nextRunTime->isPast()) {
    //     //         $nextRunTime = $nextRunTime->add($frequencyValue, $frequencyPeriod);
    //     //     }

    //     //     // Dispatch the job to repeat
    //     //     SendEmailAutomationJob::dispatch($users, $workflow)->delay($nextRunTime);
    //     // }

    //     \Log::info("First run time: " . $dispatchTime->toDateTimeString());

    //     SendEmailAutomationJob::dispatch(
    //         $filteredUsers,
    //         $workflow,
    //         $request->frequency_value,
    //         $request->frequency_period,
    //         $request->frequency_time,
    //         $request->AM_PM,
    //         $request->email_template_id
    //     )->delay($dispatchTime);






    //     // if ($workflow->condition_data_point === '1' && $workflow->condition_status == 0) {
    //     //     // Check if no resume uploaded (you need to implement this logic)

    //     //     // Schedule the job based on frequency
    //     //     $frequencyValue = $workflow->frequency_value;
    //     //     $frequencyPeriod = $workflow->frequency_period;
    //     //     $frequencyTime = $workflow->frequency_time;
    //     //     $startDate = Carbon::parse($workflow->start_date . ' ' . $workflow->start_time);

    //     //     // Calculate the next run time
    //     //     $nextRunTime = $startDate;

    //     //     // If the current time is past the next run time, calculate the next occurrence
    //     //     if (Carbon::now()->greaterThan($nextRunTime)) {
    //     //         $nextRunTime = $nextRunTime->add($frequencyValue, $frequencyPeriod);
    //     //     }

    //     //     // Dispatch the job to send emails
    //     //     SendEmailAutomationJob::dispatch($users, $workflow)->delay($nextRunTime);
    //     // }

    //     return response()->json(['message' => 'Workflow saved and email scheduled.']);
    // }

    // public function saveWorkFlow(Request $request)
    // {
    //     // Validate the incoming request data
    //     $validator = Validator::make($request->all(), [
    //         'name' => 'nullable|string|max:255',
    //         'instance' => 'nullable|string|max:255',
    //         'parent_id' => 'nullable|integer|exists:work_flows,id',
    //         'contacts' => 'nullable|string|max:255',
    //         'segments' => 'nullable|array',
    //         'segments.*.datapoint_id' => 'required|integer|exists:data_points,id',
    //         'segments.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
    //         'segments.*.value' => 'nullable|string',
    //         'frequency_value' => 'nullable|string|max:255',
    //         'frequency_period' => 'nullable|string|max:255',
    //         'frequency_time' => 'nullable|string|max:255',
    //         'AM_PM' => 'nullable|string|max:2',
    //         'start_date' => 'nullable|date',
    //         'start_time' => 'nullable|string|max:5',
    //         'email_sent' => 'nullable|string|max:255',
    //         'status' => 'nullable|integer|in:0,1,2',
    //         'execution_type' => 'required|string|in:immediate,delay,specific',
    //         'delay_value' => 'nullable|integer',
    //         'delay_unit' => 'nullable|string|in:minutes,hours',
    //         'email_template_id' => 'nullable|string',
    //         'conditions' => 'required|array',
    //         'conditions.*.datapoint_id' => 'required|integer|exists:data_points,id',
    //         'conditions.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
    //         'conditions.*.value' => 'nullable|string',
    //         'conditions.*.connector' => 'nullable|string|in:AND,OR',
    //     ]);

    //     // Check if validation fails
    //     if ($validator->fails()) {
    //         return response()->json([
    //             'success' => false,
    //             'errors' => $validator->errors(),
    //         ], 422);
    //     }

    //     // Create the workflow
    //     $workflow = WorkFlow::create($request->only([
    //         'name',
    //         'instance',
    //         'parent_id',
    //         'contacts',
    //         'segment',
    //         'segment_condition',
    //         'segment_name',
    //         'segment_value',
    //         'frequency_value',
    //         'frequency_period',
    //         'frequency_time',
    //         'AM_PM',
    //         'start_date',
    //         'start_time',
    //         'status',
    //         'execution_type',
    //         'delay_value',
    //         'delay_unit',
    //         'email_template_id',
    //     ]));

    //     // Save workflow segments
    //     if ($request->segments) {
    //         // dd($request->segments);
    //         foreach ($request->segments as $segment) {
    //             $workflow->segments()->create([
    //                 'datapoint_id' => $segment['datapoint_id'],
    //                 'operator' => $segment['operator'],
    //                 'value' => $segment['value'] ?? null,
    //                 'connector' => $segment['connector'] ?? null,
    //             ]);
    //         }
    //     }

    //     // Save workflow conditions
    //     foreach ($request->conditions as $condition) {
    //         $workflow->conditions()->create([
    //             'datapoint_id' => $condition['datapoint_id'], // This stores the data_point_id
    //             'operator' => $condition['operator'],
    //             'value' => $condition['value'] ?? null,
    //             'connector' => $condition['connector'] ?? null,
    //         ]);
    //     }

    //     // Check conditions after saving
    //     $contacts = explode(',', $workflow->contacts); // Assuming contacts are stored as '1,2'
    //     // Define the role mapping
    //     $roleMapping = [
    //         1 => 'employee',
    //         2 => 'employer',
    //     ];

    //     // Map the numeric values to their corresponding roles
    //     $roles = array_map(function ($contact) use ($roleMapping) {
    //         return $roleMapping[(int)$contact] ?? null; // Convert to int and get the role, default to null if not found
    //     }, $contacts);

    //     // Filter out any null values (in case of invalid mappings)
    //     $roles = array_filter($roles);
    //     $users = User::whereIn('role', $roles); //->get(); // Fetch users based on roles

    //     // Fetch the data point details for segments
    //     $segmentDataPoints = DataPoint::whereIn('id', collect($request->segments)->pluck('datapoint_id')->toArray())->get();
    //     // dd($segmentDataPoints);

    //     if ($request->segments) {
    //         $users->where(function ($query) use ($request, $segmentDataPoints) {
    //             foreach ($request->segments as $segment) {
    //                 $dataPoint = $segmentDataPoints->firstWhere('id', $segment['datapoint_id']);
    //                 if (!$dataPoint) {
    //                     continue; // Skip if the data point is not found
    //                 }

    //                 $field = $dataPoint->column_name;
    //                 $relation = $dataPoint->relation_name;
    //                 $operator = $segment['operator'];
    //                 $value = $segment['value'] ?? null;

    //                 if ($relation) {
    //                     // Relation-Based Filtering
    //                     if ($operator === 'IS NULL') {
    //                         $query->whereDoesntHave($relation);
    //                     } elseif ($operator === 'IS NOT NULL') {
    //                         $query->whereHas($relation);
    //                     } else {
    //                         $query->whereHas($relation, function ($q) use ($field, $operator, $value) {
    //                             if (in_array($operator, ['IS NULL', 'IS NOT NULL'])) {
    //                                 $operator === 'IS NULL'
    //                                     ? $q->whereNull($field)
    //                                     : $q->whereNotNull($field);
    //                             } else {
    //                                 $q->where($field, $operator, $value);
    //                             }
    //                         });
    //                     }
    //                 } elseif ($field) {
    //                     // Column-Based Filtering
    //                     if (in_array($operator, ['IS NULL', 'IS NOT NULL'])) {
    //                         $operator === 'IS NULL'
    //                             ? $query->whereNull($field)
    //                             : $query->whereNotNull($field);
    //                     } else {
    //                         $query->where($field, $operator, $value);
    //                     }
    //                 }
    //             }
    //         });
    //     }


    //     // // Save workflow conditions
    //     // foreach ($request->conditions as $condition) {
    //     //     $workflow->conditions()->create([
    //     //         'datapoint_id' => $condition['datapoint_id'], // This stores the data_point_id
    //     //         'operator' => $condition['operator'],
    //     //         'value' => $condition['value'] ?? null,
    //     //         'connector' => $condition['connector'] ?? null,
    //     //     ]);
    //     // }

    //     // Fetch the data point details for conditions
    //     $dataPoints = DataPoint::whereIn('id', collect($request->conditions)->pluck('datapoint_id')->toArray())->get();
    //     // dd($dataPoints);
    //     // Filter users dynamically
    //     // Build the user query
    //     $usersQuery = $users;
    //     $usersQuery->where(function ($query) use ($request, $dataPoints) {
    //         foreach ($request->conditions as $index => $condition) {
    //             $dataPoint = $dataPoints->firstWhere('id', $condition['datapoint_id']);
    //             // dd($condition['datapoint_id']);
    //             if (!$dataPoint) {
    //                 \Log::info('Skipped condition: ' . json_encode($condition));
    //                 continue; // Skip if the data point doesn't exist
    //             }

    //             $field = $dataPoint->column_name;
    //             $relation = $dataPoint->relation_name;
    //             $operator = $condition['operator'];
    //             $value = $condition['value'] ?? null;
    //             $connector = $condition['connector'] ?? 'AND';

    //             \Log::info("Processing condition: Datapoint = {$dataPoint->id}, Field = $field, Relation = $relation, Operator = $operator, Value = $value, Connector = $connector");

    //             if ($relation) {
    //                 // Apply relation-based conditions
    //                 if ($operator === 'IS NULL') {
    //                     $connector === 'AND'
    //                         ? $query->whereDoesntHave($relation)
    //                         : $query->orWhereDoesntHave($relation);
    //                 } elseif ($operator === 'IS NOT NULL') {
    //                     $connector === 'AND'
    //                         ? $query->whereHas($relation)
    //                         : $query->orWhereHas($relation);
    //                 }
    //             } elseif ($field) {
    //                 // Apply column-based conditions
    //                 if (in_array($operator, ['IS NULL', 'IS NOT NULL'])) {
    //                     if ($connector === 'AND') {
    //                         $operator === 'IS NULL'
    //                             ? $query->whereNull($field)
    //                             : $query->whereNotNull($field);
    //                     } else {
    //                         $operator === 'IS NULL'
    //                             ? $query->orWhereNull($field)
    //                             : $query->orWhereNotNull($field);
    //                     }
    //                 } else {
    //                     if ($connector === 'AND') {
    //                         // dd($value);
    //                         $query->where($field, $operator, $value);
    //                     } else {
    //                         $query->orWhere($field, $operator, $value);
    //                     }
    //                 }
    //             }

    //             // Log current state of the query
    //             \Log::info('Current Query: ' . $query->toSql());
    //         }
    //     });

    //     // Log the final query
    //     \Log::info('Final User Query: ' . $usersQuery->toSql());
    //     \Log::info('Bindings: ' . json_encode($usersQuery->getBindings()));

    //     $filteredUsers = $usersQuery->get(); // Execute and fetch results
    //     // dd($usersQuery->toSql());
    //     // dd($usersQuery->getBindings());
    //     // dd($filteredUsers);

    //     // Determine when to dispatch the job based on execution type
    //     $dispatchTime = null;

    //     switch ($request->execution_type) {
    //         case 'immediate':
    //             // Dispatch immediately
    //             $dispatchTime = now();
    //             break;

    //         case 'delay':
    //             // Calculate delay time
    //             $delayValue = $request->delay_value;
    //             $delayUnit = $request->delay_unit;

    //             // Create a Carbon instance for the delay
    //             $dispatchTime = now()->add($delayValue, $delayUnit);
    //             break;

    //         case 'specific':
    //             // Calculate specific start time
    //             $startDateTime = Carbon::parse($request->start_date . ' ' . $request->start_time . ' ' . $request->AM_PM);
    //             $dispatchTime = $startDateTime;
    //             break;
    //     }

    //     \Log::info("First run time: " . $dispatchTime->toDateTimeString());

    //     // Dispatch the job
    //     SendEmailAutomationJob::dispatch(
    //         $filteredUsers,
    //         $workflow,
    //         $request->frequency_value,
    //         $request->frequency_period,
    //         $request->frequency_time,
    //         $request->AM_PM,
    //         $request->email_template_id
    //     )->delay($dispatchTime);

    //     return response()->json(['message' => 'Workflow saved and email scheduled.']);
    // }

    // public function saveWorkFlow(Request $request)
    // {
    //     // Validate the incoming request data
    //     $validator = Validator::make($request->all(), [
    //         'workflows' => 'required|array',
    //         'workflows.*.name' => 'nullable|string|max:255',
    //         'workflows.*.contacts' => 'nullable|string|max:255',
    //         'workflows.*.segments' => 'nullable|array',
    //         'workflows.*.segments.*.datapoint_id' => 'required|integer|exists:data_points,id',
    //         'workflows.*.segments.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
    //         'workflows.*.segments.*.value' => 'nullable|string',
    //         'workflows.*.conditions' => 'required|array',
    //         'workflows.*.conditions.*.datapoint_id' => 'required|integer|exists:data_points,id',
    //         'workflows.*.conditions.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
    //         'workflows.*.conditions.*.value' => 'nullable|string',
    //         'workflows.*.frequency_value' => 'nullable|string|max:255',
    //         'workflows.*.frequency_period' => 'nullable|string|max:255',
    //         'workflows.*.frequency_time' => 'nullable|string|max:255',
    //         'workflows.*.AM_PM' => 'nullable|string|max:2',
    //         'workflows.*.execution_type' => 'required|string|in:immediate,delay,specific',
    //         'workflows.*.delay_value' => 'nullable|integer',
    //         'workflows.*.delay_unit' => 'nullable|string|in:minutes,hours',
    //         'workflows.*.email_template_id' => 'nullable|string',
    //         'workflows.*.parent_id' => 'nullable|integer|exists:work_flows,id',
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json([
    //             'success' => false,
    //             'errors' => $validator->errors(),
    //         ], 422);
    //     }

    //     $previousWorkflowId = null;

    //     foreach ($request->workflows as $workflowData) {
    //         // Create the workflow
    //         $workflow = WorkFlow::create([
    //             'name' => $workflowData['name'] ?? null,
    //             'contacts' => $workflowData['contacts'] ?? null,
    //             'frequency_value' => $workflowData['frequency_value'] ?? null,
    //             'frequency_period' => $workflowData['frequency_period'] ?? null,
    //             'frequency_time' => $workflowData['frequency_time'] ?? null,
    //             'AM_PM' => $workflowData['AM_PM'] ?? null,
    //             'execution_type' => $workflowData['execution_type'],
    //             'delay_value' => $workflowData['delay_value'] ?? null,
    //             'delay_unit' => $workflowData['delay_unit'] ?? null,
    //             'email_template_id' => $workflowData['email_template_id'] ?? null,
    //             'parent_id' => $previousWorkflowId, // Link to the previous workflow
    //         ]);

    //         // Save workflow segments
    //         if (!empty($workflowData['segments'])) {
    //             foreach ($workflowData['segments'] as $segment) {
    //                 $workflow->segments()->create([
    //                     'datapoint_id' => $segment['datapoint_id'],
    //                     'operator' => $segment['operator'],
    //                     'value' => $segment['value'] ?? null,
    //                 ]);
    //             }
    //         }

    //         // Save workflow conditions
    //         foreach ($workflowData['conditions'] as $condition) {
    //             $workflow->conditions()->create([
    //                 'datapoint_id' => $condition['datapoint_id'],
    //                 'operator' => $condition['operator'],
    //                 'value' => $condition['value'] ?? null,
    //             ]);
    //         }

    //         // Role-based filtering
    //         $contacts = explode(',', $workflowData['contacts']);
    //         $roleMapping = [
    //             1 => 'employee',
    //             2 => 'employer',
    //         ];
    //         $roles = array_map(function ($contact) use ($roleMapping) {
    //             return $roleMapping[(int)$contact] ?? null;
    //         }, $contacts);
    //         $roles = array_filter($roles);
    //         $users = User::whereIn('role', $roles);

    //         // Fetch segment data points
    //         $segmentDataPoints = DataPoint::whereIn('id', collect($workflowData['segments'])->pluck('datapoint_id'))->get();

    //         // Apply segment filtering
    //         if (!empty($workflowData['segments'])) {
    //             $users->where(function ($query) use ($workflowData, $segmentDataPoints) {
    //                 foreach ($workflowData['segments'] as $segment) {
    //                     $dataPoint = $segmentDataPoints->firstWhere('id', $segment['datapoint_id']);
    //                     if (!$dataPoint) continue;

    //                     $field = $dataPoint->column_name;
    //                     $relation = $dataPoint->relation_name;
    //                     $operator = $segment['operator'];
    //                     $value = $segment['value'] ?? null;

    //                     if ($relation) {
    //                         $query->whereHas($relation, function ($q) use ($field, $operator, $value) {
    //                             $operator === 'IS NULL'
    //                                 ? $q->whereNull($field)
    //                                 : ($operator === 'IS NOT NULL'
    //                                     ? $q->whereNotNull($field)
    //                                     : $q->where($field, $operator, $value));
    //                         });
    //                     } elseif ($field) {
    //                         $operator === 'IS NULL'
    //                             ? $query->whereNull($field)
    //                             : ($operator === 'IS NOT NULL'
    //                                 ? $query->whereNotNull($field)
    //                                 : $query->where($field, $operator, $value));
    //                     }
    //                 }
    //             });
    //         }

    //         // Fetch condition data points
    //         $conditionDataPoints = DataPoint::whereIn('id', collect($workflowData['conditions'])->pluck('datapoint_id'))->get();

    //         // Apply condition filtering
    //         $users->where(function ($query) use ($workflowData, $conditionDataPoints) {
    //             foreach ($workflowData['conditions'] as $condition) {
    //                 $dataPoint = $conditionDataPoints->firstWhere('id', $condition['datapoint_id']);
    //                 if (!$dataPoint) continue;

    //                 $field = $dataPoint->column_name;
    //                 $relation = $dataPoint->relation_name;
    //                 $operator = $condition['operator'];
    //                 $value = $condition['value'] ?? null;

    //                 if ($relation) {
    //                     $query->whereHas($relation, function ($q) use ($field, $operator, $value) {
    //                         $operator === 'IS NULL'
    //                             ? $q->whereNull($field)
    //                             : ($operator === 'IS NOT NULL'
    //                                 ? $q->whereNotNull($field)
    //                                 : $q->where($field, $operator, $value));
    //                     });
    //                 } elseif ($field) {
    //                     $operator === 'IS NULL'
    //                         ? $query->whereNull($field)
    //                         : ($operator === 'IS NOT NULL'
    //                             ? $query->whereNotNull($field)
    //                             : $query->where($field, $operator, $value));
    //                 }
    //             }
    //         });

    //         $filteredUsers = $users->get();

    //         // Schedule the email job
    //         $dispatchTime = match ($workflowData['execution_type']) {
    //             'immediate' => now(),
    //             'delay' => now()->add($workflowData['delay_value'], $workflowData['delay_unit']),
    //             'specific' => Carbon::parse("{$workflowData['start_date']} {$workflowData['start_time']} {$workflowData['AM_PM']}"),
    //         };

    //         SendEmailAutomationJob::dispatch(
    //             $filteredUsers,
    //             $workflow,
    //             $workflowData['frequency_value'],
    //             $workflowData['frequency_period'],
    //             $workflowData['frequency_time'],
    //             $workflowData['AM_PM'],
    //             $workflowData['email_template_id']
    //         )->delay($dispatchTime);

    //         // Set the current workflow as the parent for the next workflow
    //         $previousWorkflowId = $workflow->id;
    //     }

    //     return response()->json(['message' => 'Workflows saved and email scheduled successfully.']);
    // }

    public function saveWorkFlow(Request $request)
    {
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'workflows' => 'required|array',
            'workflows.*.name' => 'nullable|string|max:255',
            'workflows.*.contacts' => 'nullable|string|max:255',
            'workflows.*.segments' => 'nullable|array',
            'workflows.*.segments.*.datapoint_id' => 'required|integer|exists:data_points,id',
            'workflows.*.segments.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL,IN,BETWEEN',
            'workflows.*.segments.*.value' => 'nullable',
            'workflows.*.conditions' => 'required|array',
            'workflows.*.conditions.*.datapoint_id' => 'required|integer|exists:data_points,id',
            'workflows.*.conditions.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL,IN,BETWEEN',
            'workflows.*.conditions.*.value' => 'nullable|string',
            'workflows.*.frequency_value' => 'nullable|string|max:255',
            'workflows.*.frequency_period' => 'nullable|string|max:255',
            'workflows.*.frequency_time' => 'nullable|string|max:255',
            'workflows.*.AM_PM' => 'nullable|string|max:2',
            'workflows.*.execution_type' => 'required|string|in:immediate,delay,specific',
            'workflows.*.delay_value' => 'nullable|integer',
            'workflows.*.delay_unit' => 'nullable|string|in:minutes,hours',
            'workflows.*.email_template_id' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $previousWorkflowId = null;

        foreach ($request->workflows as $workflowData) {
            // Create the workflow
            $workflow = WorkFlow::create([
                'name' => $workflowData['name'] ?? null,
                'contacts' => $workflowData['contacts'] ?? null,
                'frequency_value' => $workflowData['frequency_value'] ?? null,
                'frequency_period' => $workflowData['frequency_period'] ?? null,
                'frequency_time' => $workflowData['frequency_time'] ?? null,
                'AM_PM' => $workflowData['AM_PM'] ?? null,
                'execution_type' => $workflowData['execution_type'],
                'delay_value' => $workflowData['delay_value'] ?? null,
                'delay_unit' => $workflowData['delay_unit'] ?? null,
                'email_template_id' => $workflowData['email_template_id'] ?? null,
                'parent_id' => $previousWorkflowId, // Link to the previous workflow
                'status' => 1, // Link to the previous workflow
            ]);

            // Save workflow segments
            if (!empty($workflowData['segments'])) {
                foreach ($workflowData['segments'] as $segment) {
                    $workflow->segments()->create([
                        'datapoint_id' => $segment['datapoint_id'],
                        'operator' => $segment['operator'],
                        'value' => $segment['value'] ?? null,
                    ]);
                }
            }

            // Save workflow conditions
            foreach ($workflowData['conditions'] as $condition) {
                $workflow->conditions()->create([
                    'datapoint_id' => $condition['datapoint_id'],
                    'operator' => $condition['operator'],
                    'value' => $condition['value'] ?? null,
                ]);
            }

            // Dispatch the job for the first workflow
            if ($previousWorkflowId === null) {
                $dispatchTime = match ($workflowData['execution_type']) {
                    'immediate' => now(),
                    'delay' => now()->add($workflowData['delay_value'], $workflowData['delay_unit']),
                    'specific' => Carbon::parse("{$workflowData['start_date']} {$workflowData['start_time']} "),
                };

                // Log of current time
                \Log::info("Current time is " . now());

                // Log messages for delay and specific execution types
                if ($workflowData['execution_type'] === 'delay') {
                    \Log::info("Job for workflow ID {$workflow->id} will be dispatched with a delay of {$workflowData['delay_value']} {$workflowData['delay_unit']} at {$dispatchTime}.");
                } elseif ($workflowData['execution_type'] === 'specific') {
                    \Log::info("Job for workflow ID {$workflow->id} will be dispatched at a specific time: {$dispatchTime}.");
                }

                SendEmailAutomationJob::dispatch($workflow->id)->delay($dispatchTime);
            }

            $previousWorkflowId = $workflow->id; // Update for nested workflow
        }

        return response()->json(['message' => 'Workflows saved and email scheduled successfully.']);
    }

    public function fetchSingleWorkFlow($id)
    {
        $workflow = WorkFlow::with(['segments', 'conditions'])->find($id);

        if (!$workflow) {
            return response()->json(['message' => 'Workflow not found.'], 404);
        }

        // Recursively fetch all children workflows
        $formattedWorkflow = $this->formatWorkflow($workflow);

        return response()->json(['workflow' => $formattedWorkflow]);
    }

    private function formatWorkflow($workflow)
    {
        return [
            'id' => $workflow->id,
            'name' => $workflow->name,
            'contacts' => $workflow->contacts,
            'frequency_value' => $workflow->frequency_value,
            'frequency_period' => $workflow->frequency_period,
            'frequency_time' => $workflow->frequency_time,
            'AM_PM' => $workflow->AM_PM,
            'execution_type' => $workflow->execution_type,
            'delay_value' => $workflow->delay_value,
            'delay_unit' => $workflow->delay_unit,
            'email_template_id' => $workflow->email_template_id,
            'segments' => $workflow->segments->map(function ($segment) {
                return [
                    'datapoint_id' => $segment->datapoint_id,
                    'operator' => $segment->operator,
                    'value' => $segment->value,
                ];
            }),
            'conditions' => $workflow->conditions->map(function ($condition) {
                return [
                    'datapoint_id' => $condition->datapoint_id,
                    'operator' => $condition->operator,
                    'value' => $condition->value,
                ];
            }),
            'children' => $workflow->children->map(function ($child) {
                return $this->formatWorkflow($child); // Recursive call for nested children
            }),
        ];
    } //this function connected to fetchSingleWorkFlow

    public function updateWorkflow(Request $request)
    {
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'workflows' => 'required|array',
            'workflows.*.id' => 'required|integer|exists:work_flows,id', // Required for updating
            'workflows.*.name' => 'nullable|string|max:255',
            'workflows.*.contacts' => 'nullable|string|max:255',
            'workflows.*.segments' => 'nullable|array',
            'workflows.*.segments.*.datapoint_id' => 'required|integer|exists:data_points,id',
            'workflows.*.segments.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
            'workflows.*.segments.*.value' => 'nullable|string',
            'workflows.*.conditions' => 'required|array',
            'workflows.*.conditions.*.datapoint_id' => 'required|integer|exists:data_points,id',
            'workflows.*.conditions.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
            'workflows.*.conditions.*.value' => 'nullable|string',
            'workflows.*.frequency_value' => 'nullable|string|max:255',
            'workflows.*.frequency_period' => 'nullable|string|max:255',
            'workflows.*.frequency_time' => 'nullable|string|max:255',
            'workflows.*.AM_PM' => 'nullable|string|max:2',
            'workflows.*.execution_type' => 'required|string|in:immediate,delay,specific',
            'workflows.*.delay_value' => 'nullable|integer',
            'workflows.*.delay_unit' => 'nullable|string|in:minutes,hours',
            'workflows.*.email_template_id' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Update workflows in a transaction
        DB::transaction(function () use ($request) {
            foreach ($request->workflows as $workflowData) {
                // Find the existing workflow
                $workflow = WorkFlow::find($workflowData['id']);

                // Update the workflow details
                $workflow->update([
                    'name' => $workflowData['name'] ?? $workflow->name,
                    'contacts' => $workflowData['contacts'] ?? $workflow->contacts,
                    'frequency_value' => $workflowData['frequency_value'] ?? $workflow->frequency_value,
                    'frequency_period' => $workflowData['frequency_period'] ?? $workflow->frequency_period,
                    'frequency_time' => $workflowData['frequency_time'] ?? $workflow->frequency_time,
                    'AM_PM' => $workflowData['AM_PM'] ?? $workflow->AM_PM,
                    'execution_type' => $workflowData['execution_type'] ?? $workflow->execution_type,
                    'delay_value' => $workflowData['delay_value'] ?? $workflow->delay_value,
                    'delay_unit' => $workflowData['delay_unit'] ?? $workflow->delay_unit,
                    'email_template_id' => $workflowData['email_template_id'] ?? $workflow->email_template_id,
                ]);

                // Update segments and conditions
                $this->updateSegmentsAndConditions($workflow, $workflowData['segments'] ?? [], $workflowData['conditions'] ?? []);
            }
        });

        return response()->json(['message' => 'Workflows updated successfully.']);
    }

    private function updateSegmentsAndConditions($workflow, $segments, $conditions)
    {
        // Clear existing segments and conditions
        $workflow->segments()->delete();
        $workflow->conditions()->delete();

        // Re-add segments
        foreach ($segments as $segment) {
            $workflow->segments()->create([
                'datapoint_id' => $segment['datapoint_id'],
                'operator' => $segment['operator'],
                'value' => $segment['value'] ?? null,
            ]);
        }

        // Re-add conditions
        foreach ($conditions as $condition) {
            $workflow->conditions()->create([
                'datapoint_id' => $condition['datapoint_id'],
                'operator' => $condition['operator'],
                'value' => $condition['value'] ?? null,
            ]);
        }
    } //this function connected to updateWorkflow

    public function duplicateWorkflow(Request $request, $id)
    {
        // Fetch the existing workflow
        $originalWorkflow = WorkFlow::with(['segments', 'conditions', 'children'])->find($id);

        if (!$originalWorkflow) {
            return response()->json(['message' => 'Original workflow not found.'], 404);
        }

        DB::beginTransaction();
        try {
            // Create the duplicate workflow with status as inactive (status = 0)
            $duplicatedWorkflow = WorkFlow::create([
                'name' => $originalWorkflow->name . ' (Copy)',
                'contacts' => $originalWorkflow->contacts,
                'frequency_value' => $originalWorkflow->frequency_value,
                'frequency_period' => $originalWorkflow->frequency_period,
                'frequency_time' => $originalWorkflow->frequency_time,
                'AM_PM' => $originalWorkflow->AM_PM,
                'execution_type' => $originalWorkflow->execution_type,
                'delay_value' => $originalWorkflow->delay_value,
                'delay_unit' => $originalWorkflow->delay_unit,
                'email_template_id' => $originalWorkflow->email_template_id,
                'parent_id' => null, // Reset parent_id for duplicate
                'status' => 0, // Inactive status
            ]);

            // Duplicate segments
            foreach ($originalWorkflow->segments as $segment) {
                $duplicatedWorkflow->segments()->create([
                    'datapoint_id' => $segment->datapoint_id,
                    'operator' => $segment->operator,
                    'value' => $segment->value,
                ]);
            }

            // Duplicate conditions
            foreach ($originalWorkflow->conditions as $condition) {
                $duplicatedWorkflow->conditions()->create([
                    'datapoint_id' => $condition->datapoint_id,
                    'operator' => $condition->operator,
                    'value' => $condition->value,
                ]);
            }

            // Recursively duplicate child workflows
            $this->duplicateChildWorkflows($originalWorkflow, $duplicatedWorkflow->id);

            DB::commit();

            return response()->json(['message' => 'Workflow duplicated successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to duplicate workflow.', 'error' => $e->getMessage()], 500);
        }
    }

    private function duplicateChildWorkflows($originalWorkflow, $newParentId)
    {
        foreach ($originalWorkflow->children as $childWorkflow) {
            $duplicatedChild = WorkFlow::create([
                'name' => $childWorkflow->name . ' (Copy)',
                'contacts' => $childWorkflow->contacts,
                'frequency_value' => $childWorkflow->frequency_value,
                'frequency_period' => $childWorkflow->frequency_period,
                'frequency_time' => $childWorkflow->frequency_time,
                'AM_PM' => $childWorkflow->AM_PM,
                'execution_type' => $childWorkflow->execution_type,
                'delay_value' => $childWorkflow->delay_value,
                'delay_unit' => $childWorkflow->delay_unit,
                'email_template_id' => $childWorkflow->email_template_id,
                'parent_id' => $newParentId, // Link to new parent workflow
                'status' => 0, // Inactive status
            ]);

            // Duplicate segments
            foreach ($childWorkflow->segments as $segment) {
                $duplicatedChild->segments()->create([
                    'datapoint_id' => $segment->datapoint_id,
                    'operator' => $segment->operator,
                    'value' => $segment->value,
                ]);
            }

            // Duplicate conditions
            foreach ($childWorkflow->conditions as $condition) {
                $duplicatedChild->conditions()->create([
                    'datapoint_id' => $condition->datapoint_id,
                    'operator' => $condition->operator,
                    'value' => $condition->value,
                ]);
            }

            // Recursively handle deeper child workflows
            $this->duplicateChildWorkflows($childWorkflow, $duplicatedChild->id);
        }
    } //this function connected to duplicateWorkflow

    public function saveAsDraft(Request $request)
    {
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'workflows' => 'required|array',
            'workflows.*.name' => 'nullable|string|max:255',
            'workflows.*.contacts' => 'nullable|string|max:255',
            'workflows.*.segments' => 'nullable|array',
            'workflows.*.segments.*.datapoint_id' => 'required|integer|exists:data_points,id',
            'workflows.*.segments.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
            'workflows.*.segments.*.value' => 'nullable|string',
            'workflows.*.conditions' => 'required|array',
            'workflows.*.conditions.*.datapoint_id' => 'required|integer|exists:data_points,id',
            'workflows.*.conditions.*.operator' => 'required|string|in:=,!=,<,>,<=,>=,LIKE,IS NULL,IS NOT NULL',
            'workflows.*.conditions.*.value' => 'nullable|string',
            'workflows.*.frequency_value' => 'nullable|string|max:255',
            'workflows.*.frequency_period' => 'nullable|string|max:255',
            'workflows.*.frequency_time' => 'nullable|string|max:255',
            'workflows.*.AM_PM' => 'nullable|string|max:2',
            'workflows.*.execution_type' => 'required|string|in:immediate,delay,specific',
            'workflows.*.delay_value' => 'nullable|integer',
            'workflows.*.delay_unit' => 'nullable|string|in:minutes,hours',
            'workflows.*.email_template_id' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $previousWorkflowId = null;

        DB::beginTransaction();
        try {
            foreach ($request->workflows as $workflowData) {
                // Create the workflow with status = 2 (draft)
                $workflow = WorkFlow::create([
                    'name' => $workflowData['name'] ?? null,
                    'contacts' => $workflowData['contacts'] ?? null,
                    'frequency_value' => $workflowData['frequency_value'] ?? null,
                    'frequency_period' => $workflowData['frequency_period'] ?? null,
                    'frequency_time' => $workflowData['frequency_time'] ?? null,
                    'AM_PM' => $workflowData['AM_PM'] ?? null,
                    'execution_type' => $workflowData['execution_type'],
                    'delay_value' => $workflowData['delay_value'] ?? null,
                    'delay_unit' => $workflowData['delay_unit'] ?? null,
                    'email_template_id' => $workflowData['email_template_id'] ?? null,
                    'parent_id' => $previousWorkflowId, // Link to the previous workflow
                    'status' => 2, // Draft status
                ]);

                // Save workflow segments
                if (!empty($workflowData['segments'])) {
                    foreach ($workflowData['segments'] as $segment) {
                        $workflow->segments()->create([
                            'datapoint_id' => $segment['datapoint_id'],
                            'operator' => $segment['operator'],
                            'value' => $segment['value'] ?? null,
                        ]);
                    }
                }

                // Save workflow conditions
                foreach ($workflowData['conditions'] as $condition) {
                    $workflow->conditions()->create([
                        'datapoint_id' => $condition['datapoint_id'],
                        'operator' => $condition['operator'],
                        'value' => $condition['value'] ?? null,
                    ]);
                }

                $previousWorkflowId = $workflow->id; // Update for nested workflow
            }

            DB::commit();
            return response()->json(['message' => 'Workflows saved as draft successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to save workflow as draft.', 'error' => $e->getMessage()], 500);
        }
    }

    public function deleteWorkFlow($id)
    {
        $workflow = WorkFlow::with('children')->find($id);

        if (!$workflow) {
            return response()->json(['success' => false, 'message' => 'Workflow not found'], 404);
        }

        // Delete child workflows recursively
        $workflow->children()->each(function ($childWorkflow) {
            $this->deleteWorkFlow($childWorkflow->id);
        });

        // Delete segments and conditions
        $workflow->segments()->delete();
        $workflow->conditions()->delete();

        // Delete the workflow itself
        $workflow->delete();

        return response()->json(['success' => true, 'message' => 'Workflow and nested workflows deleted successfully'], 200);
    }

    public function changeWorkFlowStatus($id, Request $request)
    {
        $workflow = WorkFlow::with('children')->find($id);

        if (!$workflow) {
            return response()->json(['success' => false, 'message' => 'Workflow not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        // Update the status of the main workflow
        $workflow->update(['status' => $request->status]);

        // Update the status of child workflows
        foreach ($workflow->children as $childWorkflow) {
            $childWorkflow->update(['status' => $request->status]);
        }

        return response()->json(['success' => true, 'message' => 'Workflow status updated successfully'], 200);
    }

    //Email Template Section
    
    // public function getAllEmailTemplates()
    // {
    //     $templates = EmailTemplate::all();

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Templates retrieved successfully.',
    //         'data' => $templates,
    //     ], 200);
    // }

    public function getAllEmailTemplates(Request $request)
    {
        $query = EmailTemplate::query();

        // Apply filtering by template_name if provided
        if ($request->has('template_name') && !empty($request->template_name)) {
            $query->where('template_name', 'LIKE', '%' . $request->template_name . '%');
        }

        // Apply sorting based on created_at
        if ($request->has('sort') && in_array($request->sort, ['asc', 'desc'])) {
            $query->orderBy('created_at', $request->sort);
        }

        // Retrieve templates
        $templates = $query->get();

        return response()->json([
            'status' => 'success',
            'message' => 'Templates retrieved successfully.',
            'data' => $templates,
        ], 200);
    }

    // public function createEmailTemplate(Request $request)
    // {
    //     $request->validate([
    //         'template_name' => 'required|string|max:255',
    //         'template_html' => 'nullable',
    //         'template_json' => 'nullable',
    //         'status' => 'nullable|boolean',
    //         'bookmark' => 'nullable|boolean',
    //     ]);

    //     $template = EmailTemplate::create([
    //         'template_name' => $request->input('template_name'),
    //         'template_html' => $request->input('template_html'),
    //         'template_json' => $request->input('template_json'),
    //         'status' => $request->input('status', 0),
    //         'bookmark' => $request->input('bookmark', 0),
    //     ]);

    //     return response()->json([
    //         'status' => 'success',
    //         'message' => 'Template created successfully.',
    //         'data' => $template,
    //     ], 201);
    // }

    public function createEmailTemplate(Request $request)
    {
        $request->validate([
            'template_name' => 'required|string|max:255',
            'template_html' => 'nullable',
            'template_json' => 'nullable',
            'status' => 'nullable|boolean',
            'bookmark' => 'nullable|boolean',
            'subject' => 'nullable|string|max:255', // Added subject validation
        ]);

        $template = EmailTemplate::create([
            'template_name' => $request->input('template_name'),
            'template_html' => $request->input('template_html'),
            'template_json' => $request->input('template_json'),
            'status' => $request->input('status', 0),
            'bookmark' => $request->input('bookmark', 0),
            'subject' => $request->input('subject', 'No Subject'), // Save subject with default value if not provided
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Template created successfully.',
            'data' => $template,
        ], 201);
    }

    public function getEmailTemplateById($id)
    {
        $template = EmailTemplate::find($id);

        if (!$template) {
            return response()->json([
                'status' => 'error',
                'message' => 'Template not found.',
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Template retrieved successfully.',
            'data' => $template,
        ], 200);
    }

    public function updateEmailTemplate(Request $request, $id)
    {
        $request->validate([
            'template_name' => 'required|string|max:255',
            'template_html' => 'nullable|string',
            'template_json' => 'nullable|string',
            'status' => 'nullable|boolean',
            'bookmark' => 'nullable|boolean',
        ]);

        $template = EmailTemplate::find($id);

        if (!$template) {
            return response()->json([
                'status' => 'error',
                'message' => 'Template not found.',
            ], 404);
        }

        $template->update([
            'template_name' => $request->input('template_name'),
            'template_html' => $request->input('template_html'),
            'template_json' => $request->input('template_json'),
            'status' => $request->input('status', 0),
            'bookmark' => $request->input('bookmark', 0),
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Template updated successfully.',
            'data' => $template,
        ], 200);
    }

    public function deleteEmailTemplate($id)
    {
        $template = EmailTemplate::find($id);

        if (!$template) {
            return response()->json([
                'status' => 'error',
                'message' => 'Template not found.',
            ], 404);
        }

        $template->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Template deleted successfully.',
        ], 200);
    }

    public function trackOpen(Request $request)
    {
        \Log::info("Track open triggered"); // Add this log
        // dd(1);
        // Get the user's email and workflow_id from the URL
        $email = $request->query('email');
        $workflowId = $request->query('workflow_id');
        $uniqueId = $request->query('unique_id');

        // Find the record associated with this user and workflow
        $emailSent = EmailSent::where('user_email', $email)
                            ->where('workflow_id', $workflowId)
                            ->where('unique_id', $uniqueId)
                            ->first();

        // If the email exists, mark it as viewed
        if ($emailSent) {
            $emailSent->is_viewed = 1; // Mark as viewed
            $emailSent->save();

            \Log::info("Email opened for {$emailSent->user_email}");
        }

        // Return a transparent 1x1 pixel image to complete the tracking
        $image = imagecreatetruecolor(1, 1); // Create a blank image
        $background = imagecolorallocate($image, 255, 255, 255); // White pixel (1x1)
        imagesetpixel($image, 0, 0, $background); // Set the pixel to white
        header('Content-Type: image/png');
        imagepng($image); // Output the image
        imagedestroy($image); // Free memory
    }
}
