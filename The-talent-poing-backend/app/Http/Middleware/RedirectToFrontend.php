<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RedirectToFrontend
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (preg_match('/^storage\/https:/', $request->path())) {
            // Redirect to the frontend homepage
            return redirect('https://www.thetalentpoint.com/');
        }
        // Otherwise, proceed with the request6
        return $next($request);
    }
}
