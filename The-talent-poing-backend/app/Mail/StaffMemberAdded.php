<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class StaffMemberAdded extends Mailable
{
    use Queueable, SerializesModels;

    public $name;
    public $password;
    public $email;
    public $availableResumeCount;

    /**
     * Create a new message instance.
     *
     * @param  string  $name
     * @param  string  $email
     * @param  int  $availableResumeCount
     * @return void
     */
    public function __construct($name,$password, $email, $availableResumeCount)
    {
        $this->name = $name;
        $this->password = $password;
        $this->email = $email;
        $this->availableResumeCount = $availableResumeCount;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Staff Member Login Details')
                    ->view('emails.staff-member-added');
    }
}
