<?php
namespace App\Mail;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendBirthdayWishes extends Mailable
{
    use Queueable, SerializesModels;
    //public $team_member_email;
    
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->team_member_email = $team_member_email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {   
        return $this->from('<EMAIL>','Talent Point')
                    ->subject("User Birthday Wishes")
                    ->view('mails.BirdthdayWishes');
    }
}