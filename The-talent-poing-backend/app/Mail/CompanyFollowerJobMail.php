<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CompanyFollowerJobMail extends Mailable
{
    use Queueable, SerializesModels;

    public $userName;
    public $jobTitle;
    public $companyName;
    public $experience;
    public $countryName;
    public $jobSlug;
    public $companySlug;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($userName, $jobTitle, $companyName, $experience, $countryName,$jobSlug,$companySlug)
    {
        $this->userName = $userName;
        $this->jobTitle = $jobTitle;
        $this->companyName = $companyName;
        $this->experience = $experience;
        $this->countryName = $countryName;
        $this->jobSlug = $jobSlug;
        $this->companySlug = $companySlug;
        
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('New Job Alert')
            ->view('emails.companyfollowerjobmail'); // Blade view for the email content
    }
}
