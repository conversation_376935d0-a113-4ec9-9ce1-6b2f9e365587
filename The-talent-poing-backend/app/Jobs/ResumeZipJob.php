<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\ResumeDownload;
use App\Models\Resumes;
use ZipArchive;
use Storage;

class ResumeZipJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;



    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $userId;
    protected $selectedUsers;

    public function __construct($userId, $selectedUsers)
    {
        $this->userId = $userId;
        $this->selectedUsers = $selectedUsers;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // Create a temporary zip file
            $zipFileName = 'resumes_' . time() . '.zip';
            $zipPath = storage_path("app/public/{$zipFileName}");

            $zip = new ZipArchive;
            if ($zip->open($zipPath, ZipArchive::CREATE) === TRUE) {
                // Retrieve active resumes, filtered by selected users if provided
                $query = Resumes::where('status', 'active')
                    ->select('resume_pdf_path');

                if (!empty($this->selectedUsers)) {
                    $query->whereIn('user_id', $this->selectedUsers);
                }

                $resumes = $query->get();

                foreach ($resumes as $resume) {
                    $filePath = storage_path("app/public/images/employee/resume/{$resume->resume_pdf_path}");
                    if (file_exists($filePath)) {
                        $zip->addFile($filePath, basename($resume->resume_pdf_path)); // Add file to zip
                    }
                }
                $zip->close();
            }

            // Fetch the pending row for the current user
            $resumeDownload = ResumeDownload::where('user_id', $this->userId)
                ->where('status', 'pending')
                ->first();

            if ($resumeDownload) {
                // Update the existing pending row with the completed status and file path
                $resumeDownload->update([
                    'file_path' => $zipFileName,
                    'status' => 'completed',
                ]);
            }
        } catch (\Exception $e) {
            $this->failed($e); // Call the failed method if an exception occurs
            throw $e; // Rethrow the exception to trigger Laravel's failed job handling
        }
    }


    public function failed(\Exception $exception)
    {
        // Log the failure or notify the user
        \Log::error("Resume ZIP job failed for user {$this->userId}. Error: {$exception->getMessage()}");

        // Update the database entry to mark the job as failed
        ResumeDownload::where('user_id', $this->userId)
            ->where('status', 'pending')
            ->update(['status' => 'failed']);
    }
}
