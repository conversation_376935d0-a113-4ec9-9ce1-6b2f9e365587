<?php

namespace App\Jobs;

use App\Models\DataPoint;
use App\Models\EmailSent;
use App\Models\EmailTemplate;
use App\Models\User;
use App\Models\WorkFlow;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

// class SendEmailAutomationJob implements ShouldQueue
// {
//     use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

//     protected $users;
//     protected $workflow;
//     protected $frequencyValue;
//     protected $frequencyPeriod;
//     protected $frequencyTime;
//     protected $AM_PM;
//     protected $emailTemplateId;

//     /**
//      * Create a new job instance.
//      *
//      * @return void
//      */
//     public function __construct($users, $workflow, $frequencyValue = null, $frequencyPeriod = null, $frequencyTime = null, $AM_PM = null, $emailTemplateId = null)
//     {
//         $this->users = $users;
//         $this->workflow = $workflow;
//         $this->frequencyValue = $frequencyValue;
//         $this->frequencyPeriod = $frequencyPeriod;
//         $this->frequencyTime = $frequencyTime;
//         $this->AM_PM = $AM_PM;
//         $this->emailTemplateId = $emailTemplateId;
//     }

//     /**
//      * Execute the job.
//      *
//      * @return void
//      */
//     public function handle()
//     {
//         // \Log::info("Datapoint ID is: " . $this->workflow->condition_data_point);
//         // Send emails to all users
//         // foreach ($this->users as $user) {
//         //     // Fetch the DataPoint using the condition_data_point ID
//         //     $dataPoint = \App\Models\DataPoint::find($this->workflow->condition_data_point);

//         //     // Check if the DataPoint exists
//         //     if ($dataPoint) {
//         //         // Set the data point name if found
//         //         $dataPointName = $dataPoint->data_point_name;
//         //     } else {
//         //         // Default value if DataPoint not found
//         //         $dataPointName = 'Unknown DataPoint';
//         //     }

//         //     // Log the data point name (whether found or default)
//         //     // \Log::info("Datapoint name for user {$user->name} is: " . $dataPointName);

//         //     Mail::raw("Hello, {$user->name}!\n\nThis is your remainder message.your {$dataPointName} is missing.Please update your profile.\n\nThank you!", function ($message) use ($user) {
//         //         $message->to($user->email)
//         //             ->subject('Your Notification Subject');
//         //     });
//         // }

//         foreach ($this->users as $user) {
//             // Fetch the email template
//             $templateHtml = EmailTemplate::where('id', $this->emailTemplateId)->value('template_html'); // Get your specific email template

//             // Replace placeholders for the user
//             $finalEmailContent = $this->replacePlaceholders($templateHtml, $user->id);

//             // Send the email
//             Mail::raw($finalEmailContent, function ($message) use ($user) {
//                 $message->to($user->email)
//                     ->subject('Your Notification Subject');
//             });
//         }

//         // function replacePlaceholders($templateHtml, $userId)
//         // {
//         //     // Fetch user with all relations
//         //     $user = User::with(['company', 'country', 'work_experience', 'education', 'languages', 'profile'])->findOrFail($userId);

//         //     // Fetch active data points for the user's role
//         //     $dataPoints = DB::table('data_points')
//         //         ->where('user_type', $user->role)
//         //         ->where('status', 2)
//         //         ->get();

//         //     // Prepare dynamic variables
//         //     $variables = [];
//         //     foreach ($dataPoints as $dataPoint) {
//         //         $placeholder = "{{" . $dataPoint->data_point_name . "}}";

//         //         // Fetch the value dynamically based on relation_name and column_name
//         //         if ($dataPoint->relation_name) {
//         //             $relation = $dataPoint->relation_name;
//         //             $column = $dataPoint->column_name;

//         //             $value = $user->$relation->$column ?? '';
//         //         } else {
//         //             $value = $user->{$dataPoint->column_name} ?? '';
//         //         }

//         //         $variables[$placeholder] = $value;
//         //     }

//         //     // Replace placeholders in the email template
//         //     $finalHtml = str_replace(array_keys($variables), array_values($variables), $templateHtml);

//         //     return $finalHtml;
//         // }


//         // Reschedule the job if frequency is set
//         if ($this->frequencyValue && $this->frequencyPeriod) {
//             // Parse the specific time for the next run
//             $frequencyTime = Carbon::createFromFormat('h:i A', $this->frequencyTime . ' ' . $this->AM_PM);
//             $nextRunTime = now()
//                 ->add($this->frequencyValue, $this->frequencyPeriod) // Add frequency interval
//                 ->setTime($frequencyTime->hour, $frequencyTime->minute); // Set specific time

//             \Log::info("Next run time: " . $nextRunTime->toDateTimeString());
//             // Ensure that the next run time is in the future
//             if ($nextRunTime->isPast()) {
//                 $nextRunTime = $nextRunTime->add($this->frequencyValue, $this->frequencyPeriod);
//             }

//             // Dispatch the job for the next occurrence
//             SendEmailAutomationJob::dispatch(
//                 $this->users,
//                 $this->workflow,
//                 $this->frequencyValue,
//                 $this->frequencyPeriod,
//                 $this->frequencyTime,
//                 $this->AM_PM
//             )->delay($nextRunTime);
//         }
//     }

//     function replacePlaceholders($templateHtml, $userId)
//     {
//         // Fetch user with all relations
//         $user = User::with(['company', 'country', 'work_experience', 'education', 'languages', 'profile'])->findOrFail($userId);
//         // \Log::info('User fetched:', $user->toArray());

//         // Fetch active data points for the user's role
//         $dataPoints = DB::table('data_points')
//             // ->where('user_type', $user->role)
//             ->where('status', 1)
//             ->get();
//         // \Log::info('Data points retrieved:', $dataPoints->toArray());

//         // Prepare dynamic variables
//         $variables = [];
//         foreach ($dataPoints as $dataPoint) {
//             $placeholder = "{{" . $dataPoint->data_point_name . "}}";

//             // Fetch the value dynamically based on relation_name and column_name
//             // if ($dataPoint->relation_name) {
//             //     $relation = $dataPoint->relation_name;
//             //     $column = $dataPoint->column_name;

//             //     $value = $user->$relation->$column ?? '';
//             // } else {
//             //     $value = $user->{$dataPoint->column_name} ?? '';
//             // }

//             if ($dataPoint->relation_name) {
//                 $relation = $dataPoint->relation_name;
//                 $column = $dataPoint->column_name;

//                 if ($user->$relation instanceof \Illuminate\Support\Collection) {
//                     // Handle collection, e.g., get the first item
//                     $value = $user->$relation->first()?->$column ?? '';
//                 } else {
//                     $value = $user->$relation->$column ?? '';
//                 }
//             } else {
//                 $value = $user->{$dataPoint->column_name} ?? '';
//             }

//             $variables[$placeholder] = $value;
//         }
//         // \Log::info('Variables constructed:', $variables);

//         // Replace placeholders in the email template
//         $finalHtml = str_replace(array_keys($variables), array_values($variables), $templateHtml);

//         return $finalHtml;
//     }
// }

class SendEmailAutomationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $workflowId;

    public function __construct($workflowId)
    {
        $this->workflowId = $workflowId;
    }

    // public function handle()
    // {
    //     $workflow = WorkFlow::with(['segments', 'conditions'])->find($this->workflowId);

    //     if (!$workflow || $workflow->status !== 1) {
    //         \Log::info("Workflow {$this->workflowId} is inactive or not found.");
    //         return;
    //     }

    //     $usersQuery = User::query(); // Start a fresh user query

    //     // Apply segment filtering
    //     $segmentDataPoints = DataPoint::whereIn('id', $workflow->segments->pluck('datapoint_id'))->get();
    //     $usersQuery->where(function ($query) use ($workflow, $segmentDataPoints) {
    //         foreach ($workflow->segments as $segment) {
    //             $dataPoint = $segmentDataPoints->firstWhere('id', $segment['datapoint_id']);
    //             if (!$dataPoint) continue;

    //             $field = $dataPoint->column_name;
    //             $operator = $segment['operator'];
    //             $value = $segment['value'] ?? null;

    //             $operator === 'IS NULL'
    //                 ? $query->whereNull($field)
    //                 : ($operator === 'IS NOT NULL'
    //                     ? $query->whereNotNull($field)
    //                     : $query->where($field, $operator, $value));
    //         }
    //     });

    //     // Apply condition filtering
    //     $conditionDataPoints = DataPoint::whereIn('id', $workflow->conditions->pluck('datapoint_id'))->get();
    //     $usersQuery->where(function ($query) use ($workflow, $conditionDataPoints) {
    //         foreach ($workflow->conditions as $condition) {
    //             $dataPoint = $conditionDataPoints->firstWhere('id', $condition['datapoint_id']);
    //             if (!$dataPoint) continue;

    //             $field = $dataPoint->column_name;
    //             $operator = $condition['operator'];
    //             $value = $condition['value'] ?? null;

    //             $operator === 'IS NULL'
    //                 ? $query->whereNull($field)
    //                 : ($operator === 'IS NOT NULL'
    //                     ? $query->whereNotNull($field)
    //                     : $query->where($field, $operator, $value));
    //         }
    //     });

    //     $contactsToEmail = $usersQuery->get();

    //     if ($contactsToEmail->isNotEmpty()) {
    //         foreach ($contactsToEmail as $user) {
    //             // Fetch the email template
    //             $templateHtml = EmailTemplate::where('id', $workflow->email_template_id)->value('template_html');

    //             // Replace placeholders for the user
    //             $finalEmailContent = $this->replacePlaceholders($templateHtml, $user->id);

    //             // Send the email
    //             Mail::raw($finalEmailContent, function ($message) use ($user) {
    //                 $message->to($user->email)
    //                     ->subject('Reminder: Update Your Information');
    //             });
    //         }
    //     }

    //     // Reschedule the job if frequency is set
    //     if ($workflow->frequency_value && $workflow->frequency_period) {
    //         $nextRunTime = now()
    //             ->add($workflow->frequency_value, $workflow->frequency_period)
    //             ->setTimeFromTimeString($workflow->frequency_time);

    //         SendEmailAutomationJob::dispatch($this->workflowId)->delay($nextRunTime);
    //     }

    //     \Log::info("Job completed for workflow {$this->workflowId}.");
    // }

    // public function handle()
    // {
    //     \Log::info("Starting job for workflow {$this->workflowId}.");

    //     $workflow = WorkFlow::with(['segments', 'conditions'])->find($this->workflowId);

    //     if (!$workflow || $workflow->status !== 1) {
    //         \Log::info("Workflow {$this->workflowId} is inactive or not found.");
    //         return;
    //     }

    //     \Log::info("Workflow found: {$workflow->id}, status: active.");

    //     $usersQuery = User::query(); // Start a fresh user query

    //     // Apply segment filtering
    //     $segmentDataPoints = DataPoint::whereIn('id', $workflow->segments->pluck('datapoint_id'))->get();
    //     \Log::info("Segment data points for workflow {$workflow->id}: " . $segmentDataPoints->pluck('data_point_name')->join(', '));

    //     $usersQuery->where(function ($query) use ($workflow, $segmentDataPoints) {
    //         foreach ($workflow->segments as $segment) {
    //             $dataPoint = $segmentDataPoints->firstWhere('id', $segment['datapoint_id']);
    //             if (!$dataPoint) continue;

    //             $field = $dataPoint->column_name;
    //             $operator = $segment['operator'];
    //             $value = $segment['value'] ?? null;

    //             \Log::info("Applying segment filter: {$dataPoint->data_point_name} {$operator} {$value}");

    //             if ($operator === 'IS NULL') {
    //                 $query->whereNull($field);
    //             } elseif ($operator === 'IS NOT NULL') {
    //                 $query->whereNotNull($field);
    //             } else {
    //                 $query->where($field, $operator, $value);
    //             }
    //         }
    //     });

    //     $segmentFilteredUsers = $usersQuery->get();
    //     \Log::info("Users after segment filter: " . $segmentFilteredUsers->count());

    //     // Apply condition filtering
    //     $conditionDataPoints = DataPoint::whereIn('id', $workflow->conditions->pluck('datapoint_id'))->get();
    //     \Log::info("Condition data points for workflow {$workflow->id}: " . $conditionDataPoints->pluck('data_point_name')->join(', '));

    //     $usersQuery->where(function ($query) use ($workflow, $conditionDataPoints) {
    //         foreach ($workflow->conditions as $condition) {
    //             $dataPoint = $conditionDataPoints->firstWhere('id', $condition['datapoint_id']);
    //             if (!$dataPoint) continue;

    //             $field = $dataPoint->column_name;
    //             $operator = $condition['operator'];
    //             $value = $condition['value'] ?? null;

    //             \Log::info("Applying condition filter: {$dataPoint->data_point_name} {$operator} {$value}");

    //             if ($operator === 'IS NULL') {
    //                 $query->whereNull($field);
    //             } elseif ($operator === 'IS NOT NULL') {
    //                 $query->whereNotNull($field);
    //             } else {
    //                 $query->where($field, $operator, $value);
    //             }
    //         }
    //     });

    //     $filteredUsers = $usersQuery->get();
    //     \Log::info("Users after condition filter: " . $filteredUsers->count());

    //     if ($filteredUsers->isNotEmpty()) {
    //         foreach ($filteredUsers as $user) {
    //             $templateHtml = EmailTemplate::where('id', $workflow->email_template_id)->value('template_html');
    //             $finalEmailContent = $this->replacePlaceholders($templateHtml, $user->id);

    //             \Log::info("Sending email to: {$user->email} with template: {$workflow->email_template_id}");

    //             Mail::raw($finalEmailContent, function ($message) use ($user) {
    //                 $message->to($user->email)
    //                     ->subject('Reminder: Update Your Information');
    //             });
    //         }
    //     } else {
    //         \Log::info("No users to send emails for workflow {$workflow->id}.");
    //     }

    //     if ($workflow->frequency_value && $workflow->frequency_period) {
    //         $nextRunTime = now()->add($workflow->frequency_value, $workflow->frequency_period)
    //             ->setTimeFromTimeString($workflow->frequency_time);

    //         \Log::info("Next run time for workflow {$workflow->id}: " . $nextRunTime->toDateTimeString());

    //         SendEmailAutomationJob::dispatch($this->workflowId)->delay($nextRunTime);
    //     }

    //     \Log::info("Job completed for workflow {$workflow->id}.");
    // }

    public function handle()
    {
        \Log::info("**** Starting job for workflow {$this->workflowId}.");

        $workflow = WorkFlow::with(['segments', 'conditions'])->find($this->workflowId);

        if (!$workflow || $workflow->status !== 1) {
            \Log::info("Workflow {$this->workflowId} is inactive or not found.");
            return;
        }

        \Log::info("Workflow found: {$workflow->id}, status: active.");

        $usersQuery = User::query(); // Start with all users

        //---- Add contact filtering based on the workflow's contacts field ----//
        if (!empty($workflow->contacts)) {
            // Assuming contacts is stored as a comma-separated string like "1,2"
            $contactTypes = array_map('trim', explode(',', $workflow->contacts));

            // Map the numeric values to role strings
            $roleMap = [
                '1' => 'employee',
                '2' => 'employer'
            ];
            $roles = [];
            foreach ($contactTypes as $contact) {
                if (isset($roleMap[$contact])) {
                    $roles[] = $roleMap[$contact];
                }
            }

            // Filter the users based on their role column
            if (!empty($roles)) {
                $usersQuery->whereIn('role', $roles);
            }
        }

        // Log the user query before executing it
        \Log::info("User  query after contact filter: " . $usersQuery->toSql(), $usersQuery->getBindings());

        $contactFilteredUsers = $usersQuery->get();
        \Log::info("Users after contact filter: " . $contactFilteredUsers->count());

        //---- Apply segment filtering ----//
        $segmentDataPoints = DataPoint::whereIn('id', $workflow->segments->pluck('datapoint_id'))->get();
        \Log::info("Segment data points for workflow {$workflow->id}: " . $segmentDataPoints->pluck('data_point_name')->join(', '));

        $usersQuery->where(function ($query) use ($workflow, $segmentDataPoints) {
            foreach ($workflow->segments as $segment) {
                $dataPoint = $segmentDataPoints->firstWhere('id', $segment['datapoint_id']);
                if (!$dataPoint)
                    continue;

                $field = $dataPoint->column_name;
                $relation = $dataPoint->relation_name;
                $operator = $segment['operator'];
                $value = $segment['value'] ?? null;

                // \Log::info("Applying segment filter: {$dataPoint->data_point_name} {$operator} {$value}");

                if ($relation) {

                    // Log for relationship-based filtering
                    \Log::info("Applying segment filter on relation: {$relation} -> {$dataPoint->data_point_name} {$operator} {$value}");

                    $query->whereHas($relation, function ($q) use ($field, $operator, $value) {
                        if ($operator === 'IS NULL') {
                            $q->whereNull($field);
                        } elseif ($operator === 'IS NOT NULL') {
                            $q->whereNotNull($field);
                        } elseif ($operator === 'IN') {
                            $values = is_array($value) ? $value : explode(',', $value);
                            // \Log::info("Applying IN filter on relation: {$field} IN (" . implode(',', $values) . ")");
                            $q->whereIn($field, $values);
                        } elseif ($operator === 'BETWEEN') {
                            // Ensure value is an array (handle cases where it might be a string)
                            if (is_string($value)) {
                                $value = explode(',', $value); // Convert comma-separated string to array
                            }

                            // Ensure $value is a valid array with exactly 2 values
                            if (is_array($value) && count($value) === 2) {
                                $minValue = trim($value[0]); // Clean up values
                                $maxValue = trim($value[1]);

                                // \Log::info("Applying BETWEEN filter on field: {$field} BETWEEN {$minValue} AND {$maxValue}");

                                $q->whereBetween($field, [$minValue, $maxValue]);
                            } else {
                                // \Log::error("Invalid BETWEEN values for {$field}: " . json_encode($value));
                            }
                        } else {
                            $q->where($field, $operator, $value);
                        }
                    });
                } elseif ($field) {

                    // Log for field-based filtering
                    \Log::info("Applying segment filter on field: {$field} {$operator} {$value}");

                    if ($operator === 'IS NULL') {
                        $query->whereNull($field);
                    } elseif ($operator === 'IS NOT NULL') {
                        $query->whereNotNull($field);
                    } elseif ($operator === 'IN') {
                        $values = is_array($value) ? $value : explode(',', $value);
                        \Log::info("Applying IN filter on field: {$field} IN (" . implode(',', $values) . ")");
                        $query->whereIn($field, $values);
                    } elseif ($operator === 'BETWEEN') {
                        // Ensure value is an array (handle cases where it might be a string)
                        if (is_string($value)) {
                            $value = explode(',', $value); // Convert comma-separated string to array
                        }

                        // Ensure $value is a valid array with exactly 2 values
                        if (is_array($value) && count($value) === 2) {
                            $minValue = trim($value[0]); // Clean up values
                            $maxValue = trim($value[1]);

                            // \Log::info("Applying BETWEEN filter on field: {$field} BETWEEN {$minValue} AND {$maxValue}");

                            $query->whereBetween($field, [$minValue, $maxValue]);
                        } else {
                            // \Log::error("Invalid BETWEEN values for {$field}: " . json_encode($value));
                        }
                    } else {
                        $query->where($field, $operator, $value);
                    }
                }
            }
        });

        // Log the user query before executing it
        \Log::info("User  query after segment filter : " . $usersQuery->toSql(), $usersQuery->getBindings());

        $segmentFilteredUsers = $usersQuery->get();
        \Log::info("Users after segment filter: " . $segmentFilteredUsers->count());

        //---- Apply condition filtering ----//
        $conditionDataPoints = DataPoint::whereIn('id', $workflow->conditions->pluck('datapoint_id'))->get();
        \Log::info("Condition data points for workflow {$workflow->id}: " . $conditionDataPoints->pluck('data_point_name')->join(', '));

        $usersQuery->where(function ($query) use ($workflow, $conditionDataPoints) {
            foreach ($workflow->conditions as $condition) {
                $dataPoint = $conditionDataPoints->firstWhere('id', $condition['datapoint_id']);
                if (!$dataPoint)
                    continue;

                $field = $dataPoint->column_name;
                $relation = $dataPoint->relation_name;
                $operator = $condition['operator'];
                $value = $condition['value'] ?? null;

                if ($relation) {

                    // Log for relationship-based filtering
                    \Log::info("Applying condition filter on relation: {$relation} -> {$dataPoint->data_point_name} {$operator} {$value}");

                    $query->whereHas($relation, function ($q) use ($field, $operator, $value) {
                        if ($operator === 'IS NULL') {
                            $q->whereNull($field);
                        } elseif ($operator === 'IS NOT NULL') {
                            $q->whereNotNull($field);
                        } elseif ($operator === 'IN') {
                            $values = is_array($value) ? $value : explode(',', $value);
                            // \Log::info("Applying IN filter on relation: {$field} IN (" . implode(',', $values) . ")");
                            $q->whereIn($field, $values);
                        } elseif ($operator === 'BETWEEN') {
                            // Ensure value is an array (handle cases where it might be a string)
                            if (is_string($value)) {
                                $value = explode(',', $value); // Convert comma-separated string to array
                            }

                            // Ensure $value is a valid array with exactly 2 values
                            if (is_array($value) && count($value) === 2) {
                                $minValue = trim($value[0]); // Clean up values
                                $maxValue = trim($value[1]);

                                // \Log::info("Applying BETWEEN filter on field: {$field} BETWEEN {$minValue} AND {$maxValue}");

                                $q->whereBetween($field, [$minValue, $maxValue]);
                            } else {
                                // \Log::error("Invalid BETWEEN values for {$field}: " . json_encode($value));
                            }
                        } else {
                            $q->where($field, $operator, $value);
                        }
                    });
                } elseif ($field) {

                    // Log for field-based filtering
                    \Log::info("Applying condition filter on field: {$field} {$operator} {$value}");

                    if ($operator === 'IS NULL') {
                        $query->whereNull($field);
                    } elseif ($operator === 'IS NOT NULL') {
                        $query->whereNotNull($field);
                    } elseif ($operator === 'IN') {
                        $values = is_array($value) ? $value : explode(',', $value);
                        \Log::info("Applying IN filter on field: {$field} IN (" . implode(',', $values) . ")");
                        $query->whereIn($field, $values);
                    } elseif ($operator === 'BETWEEN') {
                        // Ensure value is an array (handle cases where it might be a string)
                        if (is_string($value)) {
                            $value = explode(',', $value); // Convert comma-separated string to array
                        }

                        // Ensure $value is a valid array with exactly 2 values
                        if (is_array($value) && count($value) === 2) {
                            $minValue = trim($value[0]); // Clean up values
                            $maxValue = trim($value[1]);

                            // \Log::info("Applying BETWEEN filter on field: {$field} BETWEEN {$minValue} AND {$maxValue}");

                            $query->whereBetween($field, [$minValue, $maxValue]);
                        } else {
                            // \Log::error("Invalid BETWEEN values for {$field}: " . json_encode($value));
                        }
                    } else {
                        $query->where($field, $operator, $value);
                    }
                }
            }
        });

        // Log the user query after applying conditions
        \Log::info("User  query after applying conditions: " . $usersQuery->toSql(), $usersQuery->getBindings());

        $filteredUsers = $usersQuery->get();
        \Log::info("Users after condition filter: " . $filteredUsers->count());

        if ($filteredUsers->isNotEmpty()) {
            foreach ($filteredUsers as $user) {

                $template = EmailTemplate::find($workflow->email_template_id);

                $templateHtml = $template->template_html;
                $templateSubject = $template->subject;

                // // Replace placeholders in the template HTML with user-specific data
                // $finalEmailContent = $this->replacePlaceholders($templateHtml, $user->id);

                // Generate a unique identifier for tracking
                $uniqueId = md5($user->email . $workflow->id);

                // Create the tracking pixel URL
                $trackingPixelUrl = url('api/email/open') . '?' . http_build_query([
                    'email' => $user->email,
                    'workflow_id' => $workflow->id,
                    'unique_id' => $uniqueId
                ]);

                // Replace the placeholders in the email template with dynamic user data
                $finalEmailContent = $this->replacePlaceholders($templateHtml, $user->id);

                // Embed the tracking pixel URL in the email content
                // $finalEmailContent = str_replace('{{tracking_pixel}}', "<img src='{$trackingPixelUrl}' width='1' height='1' />", $finalEmailContent);

                // Dynamically insert the tracking pixel at the end of the email content (or anywhere you want)
                $finalEmailContent .= "<img src='{$trackingPixelUrl}' width='1' height='1' />"; // Appending to the end

                \Log::info("URL is: {$trackingPixelUrl}");
                // \Log::info("Content is: {$finalEmailContent}");


                \Log::info("Sending email to: {$user->email} with template: {$workflow->email_template_id}");

                // // Use Mail::html() to send the email as HTML
                // Mail::html($finalEmailContent, function ($message) use ($user, $templateSubject) {
                //     $message->to($user->email)
                //         ->subject($templateSubject);
                // });

                try {
                    // Use Mail::html() to send the email as HTML
                    Mail::html($finalEmailContent, function ($message) use ($user, $templateSubject) {
                        $message->to($user->email)
                            ->subject($templateSubject);
                    });

                    // Track email sending success
                    EmailSent::create([
                        'user_email' => $user->email,
                        'workflow_id' => $workflow->id,
                        'unique_id' => $uniqueId,
                        'is_sent' => 1, // Mark as sent
                        'is_viewed' => 0, // Initially mark as not viewed
                        'is_spam' => 0, // Initially mark as not spam
                    ]);

                    \Log::info("Email sent successfully to: {$user->email}");
                } catch (\Exception $e) {
                    // Track email sending failure
                    EmailSent::create([
                        'user_email' => $user->email,
                        'workflow_id' => $workflow->id,
                        'unique_id' => $uniqueId,
                        'is_sent' => 0, // Mark as failed
                        'is_viewed' => 0, // Initially mark as not viewed
                        'is_spam' => 0, // Initially mark as not spam
                    ]);

                    \Log::error("Failed to send email to: {$user->email} for workflow {$workflow->id}. Error: " . $e->getMessage());
                }
            }
        } else {
            \Log::info("No users to send emails for workflow {$workflow->id}.");
        }

        $childWorkflows = WorkFlow::where('parent_id', $workflow->id)->where('status', 1)->get();
        \Log::info("Number of active child workflows for workflow {$workflow->id}: " . $childWorkflows->count());

        if ($childWorkflows->isEmpty()) {
            \Log::info("No active child workflows for workflow {$workflow->id}.");
        } else {
            $remainingContacts = $segmentFilteredUsers->diff($filteredUsers);
            \Log::info("Remaining contacts to pass to child workflows: " . $remainingContacts->count());

            if ($remainingContacts->isEmpty()) {
                \Log::info("No remaining contacts to pass to child workflows for workflow {$workflow->id}.");
            } else {
                foreach ($childWorkflows as $childWorkflow) {
                    \Log::info("Dispatching job for nested workflow {$childWorkflow->id}.");
                    SendEmailAutomationJob::dispatch($childWorkflow->id);
                }
            }
        }

        // // Reschedule the job for the current workflow if frequency is set
        // if ($workflow->frequency_value && $workflow->frequency_period) {
        //     $nextRunTime = now()->add($workflow->frequency_value, $workflow->frequency_period)
        //         ->setTimeFromTimeString($workflow->frequency_time);

        //     \Log::info("*** Next run time for workflow {$workflow->id}: " . $nextRunTime->toDateTimeString());

        //     SendEmailAutomationJob::dispatch($this->workflowId)->delay($nextRunTime);
        // }

        // Assuming the frequency_value is a string and needs to be converted to an integer
        $frequencyValue = (int) $workflow->frequency_value; // Convert to integer

        // Reschedule the job for the current workflow if frequency is set
        if ($frequencyValue && $workflow->frequency_period) {
            // Calculate the next run time based on frequency
            $nextRunTime = now();

            // Determine the frequency period (minutes, hours, days)
            switch ($workflow->frequency_period) {
                case 'minutes':
                    $nextRunTime = $nextRunTime->addMinutes($frequencyValue); // Add minutes
                    break;
                case 'hours':
                    $nextRunTime = $nextRunTime->addHours($frequencyValue); // Add hours
                    break;
                case 'days':
                    // For days, add the frequency to the next run time first
                    $nextRunTime = $nextRunTime->addDays($frequencyValue); // Add days

                    // Set the time based on frequency_time and AM/PM
                    $timeParts = explode(':', $workflow->frequency_time);
                    $hour = (int)$timeParts[0];
                    $minute = (int)$timeParts[1];

                    // Adjust hour based on AM/PM
                    if ($workflow->AM_PM === 'PM' && $hour < 12) {
                        $hour += 12; // Convert to 24-hour format
                    } elseif ($workflow->AM_PM === 'AM' && $hour === 12) {
                        $hour = 0; // Convert 12 AM to 0 hours
                    }

                    // Set the time for the next run
                    $nextRunTime->setTime($hour, $minute);
                    break;

                default:
                    // If frequency_period is not recognized, handle it or log an error
                    \Log::error("Unknown frequency period: " . $workflow->frequency_period);
                    return;
            }

            // If the calculated next run time is in the past, adjust again based on the frequency
            if ($nextRunTime < now()) {
                \Log::warning("Next run time is in the past, adjusting...");

                // Re-adjust based on the frequency period
                switch ($workflow->frequency_period) {
                    case 'minutes':
                        $nextRunTime = $nextRunTime->addMinutes($frequencyValue);
                        break;
                    case 'hours':
                        $nextRunTime = $nextRunTime->addHours($frequencyValue);
                        break;
                    case 'days':
                        $nextRunTime = $nextRunTime->addDays($frequencyValue);
                        break;
                }
            }

            \Log::info("*** Next run time for workflow {$workflow->id}: " . $nextRunTime->toDateTimeString());

            // Dispatch the job with the calculated delay
            SendEmailAutomationJob::dispatch($this->workflowId)->delay($nextRunTime);
        }


        \Log::info("Job completed for workflow {$workflow->id}.");
    }


    private function replacePlaceholders($templateHtml, $userId)
    {
        $user = User::with(['company', 'country', 'work_experience', 'education', 'languages', 'profile'])->findOrFail($userId);
        $dataPoints = DB::table('data_points')->where('status', 1)->get();

        $variables = [];
        foreach ($dataPoints as $dataPoint) {
            $placeholder = "{{" . $dataPoint->data_point_name . "}}";
            $value = '';

            if ($dataPoint->relation_name) {
                $relation = $user->{$dataPoint->relation_name};
                if ($relation instanceof \Illuminate\Database\Eloquent\Collection) {
                    // Handle collections (e.g., hasMany)
                    $value = $relation->first() ? $relation->first()->{$dataPoint->column_name} ?? '' : '';
                } else {
                    // Handle single relation (e.g., belongsTo, hasOne)
                    $value = $relation->{$dataPoint->column_name} ?? '';
                }
            } else {
                $value = $user->{$dataPoint->column_name} ?? '';
            }

            $variables[$placeholder] = $value;
        }

        return str_replace(array_keys($variables), array_values($variables), $templateHtml);
    }
}
