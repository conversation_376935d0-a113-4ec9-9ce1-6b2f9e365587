<?php

namespace App\Jobs;

use App\Exports\UsersExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UserExportJob implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $userIds;
    protected $format;

    public function __construct($userIds, $format)
    {
        $this->userIds = $userIds;
        $this->format = $format;
    }

    public function handle()
    {
        // File path for the export
        $filePath = "downloads/users_data." . ($this->format === 'csv' ? 'csv' : 'xlsx');

        // Create the export instance
        $export = new UsersExport($this->userIds);

        // Use chunked export
        Excel::store($export, $filePath, 'public');
    }
}
