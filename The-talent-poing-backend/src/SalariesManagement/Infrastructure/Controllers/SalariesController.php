<?php

namespace Src\SalariesManagement\Infrastructure\Controllers;

use App\Models\Company;
use App\Models\Country;
use App\Models\Sector;
use App\Models\Job;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\AppFramework\ApiController;
use Src\EmployerManagement\Infrastructure\Resources\CompanyShortResource;
use Src\JobsManagement\Infrastructure\Resources\JobShortResource;

/**
 * @group Salaries
 */
class SalariesController extends ApiController
{

    /**
     * Get salary insights
     *
     * Return all information by sector and country
     *
     * @param Request $request
     * @queryParam sector_id required Example: 31
     * @queryParam country_id required Example: 45
     * @response 400 scenario="sector not exists" {"error": "The sector selected not exists"}
     * @return JsonResponse
     */
    public function getSalaryInsights(Request $request): JsonResponse
    {
        $sectorID = $request->get('sector_id');
        $countryID = $request->get('country_id');

        $sector = (new Sector)->where('id', $sectorID)->first();
        $country = (new Country)->where('id', $countryID)->first();
        $company = (new Company)->with('sector')->first();

        if ($sector && $country) {
            $jobs = (new Job)
                ->with('company.logo')
                ->where('sector_id', $sector->id)
                ->where('job_country', $country->id)
                ->get(['monthly_fixed_salary_min', 'experience', 'company_id']);

            $total = $jobs->count();

            $groupByExperience = [];
            $groupByCompany = [];

            foreach ($jobs as $job) {
                $expKey = $job->experience;
                if(!$expKey){
                    $expKey = 'any';
                }
                if(!isset($groupByExperience[$expKey])) {
                    $groupByExperience[$expKey] = [];
                }
                $groupByExperience[$expKey][] = $job->monthly_fixed_salary_min;

                $companyKey = $job->company_id;
                if(!isset($groupByCompany[$companyKey])) {
                    $groupByCompany[$companyKey] = [
                        'company' => $job->company,
                        'salaries' => [],
                    ];
                }
                $groupByCompany[$companyKey]['salaries'][] = $job->monthly_fixed_salary_min;
            }

            $experienceBased = [];
            $companyBased = [];

            foreach ($groupByExperience as $key => $item) {
                $experienceBased[] = [
                    'average' => array_sum($item) / count($item),
                    'count' => count($item),
                    'time' => $key,
                ];
            }

            foreach ($groupByCompany as $key => $item) {
                $companyBased[] = [
                    'average' => array_sum($item['salaries']) / count($item),
                    'company' => new CompanyShortResource($item['company']),
                ];
            }
            if($total > 0){
                $data = [
                    'sector' => $sector,
                    'country' => $country,
                    'salary' => [
                        'average' => $total > 0 ? $jobs->sum('monthly_fixed_salary_min') / $total : 0,
                        'min' => $jobs->min()->monthly_fixed_salary_min,
                        'max' => $jobs->max()->monthly_fixed_salary_min,
                        'total' => $total,
                    ],
                    'based_experience' => $experienceBased,
                    'based_company' => $companyBased,
                ];
            } else {
                $data = [
                    'sector' => $sector,
                    'country' => $country,
                    'salary' => [
                        'average' => 0,
                        'min' => 0,
                        'max' => 0,
                        'total' => 0,
                    ],
                    'based_experience' => [],
                    'based_company' => [],
                ];
            }

            //dd($data);
            return $this->respondWithSuccess($data);
        } else {
            return $this->respondError('The sector selected not exists');
        }
    }

    /**
     * Get salary similar career paths
     *
     * Returns a list of other similar industries with the average salary based on published jobs offers
     *
     * @param Request $request
     * @queryParam sector_id Number required Selected sector - Example: 1
     * @queryParam country_id Number required Selected country - Example: 1
     * @queryParam num_items Number of items returned - Example: 3
     * @response 400 scenario="sector not exists" {"error": "The sector selected not exists"}
     * @response 422 scenario="Missing arguments" {
     *   "message": "The sector id field is required.",
     *   "errors": {
     *     "sector_id": [ "The sector id field is required." ]
     *     "country_id": [ "The country id field is required." ]
     *   }
     *  }
     * @return JsonResponse
     */
    public function getSimilarCareerPaths(Request $request): JsonResponse
    {
        $request->validate([
            'sector_id' => 'required',
            'country_id' => 'required',
        ]);

        $sectorID = $request->get('sector_id');
        $countryID = $request->get('country_id');
        $numItems = $request->get('num_items', 3);

        $sector = (new Sector)->where('id', $sectorID)->first();
        $country = (new Country)->where('id', $countryID)->first();

        if ($sector && $country) {
            $similarSectors = (new Sector)
                //->whereRaw('MATCH(sector_name) AGAINST("%' . $sector->sector_name . '" IN NATURAL LANGUAGE MODE)')
                ->where('sector_name', 'like', '%' . $sector->sector_name . '%')
                ->limit($numItems)
                ->get();

            $result = [];
            foreach ($similarSectors as $similarSector) {
                $jobs = (new Job)
                    ->where('sector_id', $similarSector->id)
                    ->where('job_country', $country->id)
                    ->get('monthly_fixed_salary_min');

                $result[] = [
                    'sector' => $similarSector,
                    'salary_average' => $jobs->count() > 0 ? $jobs->sum('monthly_fixed_salary_min') / $jobs->count() : 0,
                    'total' => $jobs->count(),
                ];
            }

            return $this->respondWithSuccess($result);
        } else {
            return $this->respondError('The sector selected not exists');
        }
    }

    /**
     * Get latest job openings
     *
     * Returns a list of recent job offers on the selected country and sector
     *
     * @param Request $request
     * @queryParam sector_id Number required Selected sector - Example: 31
     * @queryParam country_id Number required Selected country - Example: 45
     * @queryParam num_items Number of items returned - Example: 3
     * @response 422 scenario="Missing arguments" {
     *  "message": "The sector id field is required.",
     *  "errors": {
     *    "sector_id": [ "The sector id field is required." ]
     *    "country_id": [ "The country id field is required." ]
     *  }
     * }
     * @return JsonResponse
     */
    public function latestOpenings(Request $request): JsonResponse
    {
        $request->validate([
            'sector_id' => 'required',
            'country_id' => 'required',
        ]);

        $sectorID = $request->get('sector_id');
        $countryID = $request->get('country_id');
        $numItems = $request->get('num_items', 3);

        $country = (new Country)->where('id', $countryID)->first();

        if ($sectorID && $country) {
            $jobs = (new Job)
                ->with('related_sector', 'company.logo', 'country')
                ->where('sector_id', $sectorID)
                ->where('job_country', $country->id)
                ->orderBy('created_at', 'desc')
                ->limit($numItems)
                ->get();

            return $this->respondWithSuccess(JobShortResource::collection($jobs));
        } else {
            return $this->respondError('The sector selected not exists');
        }
    }
}
