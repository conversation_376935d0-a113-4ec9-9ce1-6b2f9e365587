<?php

namespace Src\JobsManagement\Application;

use App\Models\Job;
use App\Models\SavedJobs;
use App\Models\User;
use App\Models\Country;
use App\Models\Cities;
use App\Models\Skills;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Src\JobsManagement\Infrastructure\Resources\JobSearchResource;

class JobSearchService
{
    /**
     * @param User|null $user
     * @param null $keywords
     * @param null $country
     * @param null $city
     * @param null $salary
     * @param null $currency
     * @param null $sector
     * @param null $experience
     * @param null $jobType
     * @param null $skill
     * @param int $resultsPerPage
     * @return AnonymousResourceCollection
     */
    public function __invoke(
        User $user = null,
        $keywords = null,
        $country = null,
        $city = null,
        $salary = null,
        $currency = null,
        $sector = null,
        $experience = null,
        $jobType = null,
        $skill = null,
        $resultsPerPage = 10
    ): AnonymousResourceCollection {
        $jobs = (new Job)
            ->with('country', 'company.logo')
            ->where('job_status', Job::$ACTIVE)
            ->orderBy('is_featured', 'desc')
            ->orderBy('id', 'desc');

        if ($keywords) {
            $jobs->where('job_title', 'like', '%' . $keywords . '%');
        }

        if ($country) {
            $countryId = '';
            if (is_numeric($country)) {
                $country = $country;
            } else {
                $countryData = (new Country)->where('country_name', 'like', '%' . $country . '%')->where('status', 'active')->first();
                $countryId = $countryData->id;
            }
            $jobs->where('job_country', $countryId ? $countryId : $country);
        }

        if ($city) {
            $cityId = '';
            if (is_numeric($city)) {
                $cityId = $city;
            } else {
                $cityData = (new Cities)->where('city_name', 'like', '%' . $city . '%')->where('status', 'active')->first();
                $cityId = $cityData->id;
            }
            $jobs->where('job_city', $cityId);
        }

        if ($salary && $salary[0]) {
            $jobs->where('monthly_fixed_salary_min', '>=', $salary[0]);
        }

        if ($salary && $salary[1]) {
            $jobs->where('monthly_fixed_salary_max', '<=', $salary[1]);
        }

        if ($currency) {
            $jobs->where('monthly_fixed_salary_currency', 'like', '%' . $currency . '%');
        }

        if ($jobType) {
            if (is_array($jobType)) {
                $jobs->whereIn('job_type', $jobType);
            } else {
                $jobs->where('job_type', $jobType);
            }
        }

        if ($skill) {
            if (is_array($skill)) {
                $skillId = array();
                if (is_numeric($skill)) {
                    $skillId = $skill;
                } else {
                    $skillData = (new Skills)->where('skills', 'like', '%' . $skill . '%')->where('status', 'active')->first();
                    $skillId = $skillData->id;
                }
                $jobs->whereIn('skills_required', $skillId);
            } else {
                $skillId = '';
                if (is_numeric($skill)) {
                    $skillId = $skill;
                } else {
                    $skillData = (new Skills)->where('skills', 'like', '%' . $skill . '%')->where('status', 'active')->first();
                    $skillId = $skillData->id;
                }
                $jobs->where('skills_required', $skillId);
            }
        }

        if ($sector) {
            $sectorArray = explode(',', $sector);
            $jobs = $jobs->whereIn('sector_id', $sectorArray);
        }

        $ids = $jobs->pluck('id');
        $paginatedResult = $jobs->paginate();
        $saved = null;
        if ($user) {
            $saved = (new SavedJobs())
                ->where('user_id', $user->id)
                ->whereIn('job_id', $ids)
                ->pluck('job_id');
        }

        return JobSearchResource::collection($paginatedResult)->additional(['favorites' => $saved]);
    }
}
