<?php

namespace Src\JobsManagement\Application;

use App\Models\Job;
use Exception;
use Illuminate\Database\Eloquent\Model;

class FindJobService
{
    /**
     * @param int $id
     * @param bool $includeDeleted
     * @return Model|Job
     * @throws Exception
     */
    public function __invoke(int $id, bool $includeDeleted = false): Model|Job
    {
        $job = (new Job)
            ->with('country', 'company')
            ->where('id', $id)
            ->first();
        if ($job) {
            $this->ensureStatusNotDeleted($job, $includeDeleted);
            return $job;
        } else {
            throw new Exception('The job not exists');
        }
    }

    /**
     * @throws Exception
     */
    private function ensureStatusNotDeleted($job, bool $includeDeleted): void
    {
        if ($job->job_status === 'deleted' && !$includeDeleted) {
            throw new Exception('The job is deleted');
        }
    }
}
