<?php

namespace Src\JobsManagement\Application;

use App\Models\Job;
use App\Models\SavedJobs;
use App\Models\User;

/**
 * Save Job as favorite for a user
 */
class ToggleSaveJobService
{
    public function __invoke(User $user, Job $job): SavedJobs
    {
        $exist = (new SavedJobs)
            ->where('user_id', $user->id)
            ->where('job_id', $job->id)
            ->first();
        if ($exist) {
            $exist->delete();
            return $exist;
        } else {
            $savedJob = new SavedJobs;
            $savedJob->user_id = $user->id;
            $savedJob->company_id = $job->company_id;
            $savedJob->job_id = $job->id;
            $savedJob->status = 'active';
            $savedJob->save();
            return $savedJob;
        }
    }
}
