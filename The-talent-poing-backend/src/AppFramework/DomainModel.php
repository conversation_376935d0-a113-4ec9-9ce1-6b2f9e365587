<?php

namespace Src\AppFramework;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

/**
 * Src\ConnectFramework\DomainModel
 *
 * @method static Builder|DomainModel newModelQuery()
 * @method static Builder|DomainModel newQuery()
 * @method static Builder|DomainModel query()
 * @mixin Eloquent
 * @mixin \Eloquent
 */
class DomainModel extends Model
{
    protected $keyType = 'string';
    protected $primaryKey = 'uuid';
    public $incrementing = false;

    public static function boot(): void
    {
        parent::boot();
        self::creating(function ($model) {
            $model->uuid = Str::uuid()->toString();
        });
    }
}
