<?php

namespace Src\JobsManagement\Infrastructure\Resources;

use App\Models\Job;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use Src\EmployerManagement\Infrastructure\Resources\CompanyResource;
use Src\EmployerManagement\Infrastructure\Resources\CompanyShortResource;

/**
 * @mixin Job
 */
class JobSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            "id" => $this->id,
            "user_id" => $this->user_id,
            "company_id" => $this->company_id,
            "company" => new CompanyShortResource($this->whenLoaded('company')),
            "industry" => new IndustryResource($this->whenLoaded('related_industry')),
            "country" => $this->whenLoaded('country'),
            "industry_id" => $this->industry,
            "sector_id" => $this->sector_id,
            "job_title" => $this->job_title,
            "job_slug" => $this->job_slug,
            "job_description" => $this->job_description,
            "type_of_position" => $this->type_of_position,
            "job_country" => $this->job_country,
            "experience" => $this->experience,
            "skills_required" => $this->skills_required,
            "monthly_fixed_salary_currency" => $this->monthly_fixed_salary_currency,
            "monthly_fixed_salary_min" => $this->monthly_fixed_salary_min,
            "monthly_fixed_salary_max" => $this->monthly_fixed_salary_max,
            "available_vacancies" => $this->available_vacancies,
            "deadline" => $this->deadline,
            "is_featured" => $this->is_featured,
            "hide_employer_details" => $this->hide_employer_details,
            "background_banner_image" => $this->background_banner_image,
            "meta_tag" => $this->meta_tag,
            "meta_desc" => $this->meta_desc,
            "job_type" => $this->job_type,
            "job_status" => $this->job_status,
            "postal_code" => $this->postal_code,
            "street_address" => $this->street_address,
            "created_at" => $this->created_at,
            "updated_at" => $this->updated_at,
            "job_city" => $this->job_city,
            "is_saved" => $this->is_saved,
            

        ];
    }
}
