<?php

namespace Src\JobsManagement\Infrastructure\Resources;

use App\Models\Job;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use Src\EmployerManagement\Infrastructure\Resources\CompanyResource;
use Src\EmployerManagement\Infrastructure\Resources\CompanyShortResource;

/**
 * @mixin Job
 */
class JobShortResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request): array|JsonSerializable|Arrayable
    {
        return [
            "id" => $this->id,
            "company" => new CompanyShortResource($this->whenLoaded('company')),
            "industry" => new IndustryResource($this->whenLoaded('related_industry')),
            "country" => $this->whenLoaded('country'),
            "job_title" => $this->job_title,
            "job_slug" => $this->job_slug,
            "experience" => $this->experience,
            "skills_required" => $this->skills_required,
            "monthly_fixed_salary_currency" => $this->monthly_fixed_salary_currency,
            "monthly_fixed_salary_min" => $this->monthly_fixed_salary_min,
            "monthly_fixed_salary_max" => $this->monthly_fixed_salary_max,
            "is_featured" => $this->is_featured,
            "job_status" => $this->job_status,
        ];
    }
}
