<?php

namespace Src\JobsManagement\Application;

use App\Models\Job;
use App\Models\SavedJobs;
use App\Models\User;
use App\Models\Country;
use App\Models\Cities;
use App\Models\JobCountryFaqs;
use App\Models\Skills;
use App\Models\UserSearchJobs;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Src\JobsManagement\Infrastructure\Resources\JobSearchResource;
use Carbon\Carbon;

class JobSearchService
{
    /**
     * @param User|null $user
     * @param null $keywords
     * @param null $country
     * @param null $city
     * @param null $salary
     * @param null $currency
     * @param null $sector
     * @param null $experience
     * @param null $jobType
     * @param null $skill
     * @param null $company_id
     * @param null $job_id
     * @param int $resultsPerPage
     * @return AnonymousResourceCollection
     */
    public function __invoke(
        User $user = null,
        $postal_code = null,
        $street_address  = null,
        $keywords = null,
        $country = null,
        $city = null,
        $salary = null,
        $currency = null,
        $sector = null,
        $experience = null,
        $sortby = null,
        $jobType = null,
        $job_id = null,
        $faqLocation = null,
        $page_size = null,
        $skill = null,
        $company_id = null,

        int $resultsPerPage = 10
    ): AnonymousResourceCollection {
        $jobs = (new Job)
            ->with('country', 'company.logo', 'city', 'related_sector', 'company') // new adding company, related_sector
            ->where('job_status', Job::$ACTIVE)
            ->active()
            ->orderBy('is_featured', 'desc')
            ->orderBy('id', 'desc');



        if ($job_id) {
            $jobs->where('id', $job_id);
        }

        // if ($keywords) {
        //     if (is_array($keywords)) {
        //         $jobs->where(function($query) use ($keywords) {
        //             foreach ($keywords as $keyword) {
        //                 $query->orWhere('job_title', 'like', '%' . $keyword . '%');
        //             }
        //         });
        //     } else {
        //         $jobs->where('job_title', 'like', '%' . $keywords . '%');
        //     }
        // }

        // this portion is new for keyword
        if ($keywords) {
            $jobs->where(function($query) use ($keywords) {
                if (is_array($keywords)) {
                    foreach ($keywords as $keyword) {
                        $query->orWhere('job_title', 'like', '%' . $keyword . '%')
                              ->orWhereHas('company', function ($q) use ($keyword) {
                                  $q->where('company_name', 'like', '%' . $keyword . '%');
                              });
                    }
                } else {
                    $query->where('job_title', 'like', '%' . $keywords . '%')
                          ->orWhereHas('company', function ($q) use ($keywords) {
                              $q->where('company_name', 'like', '%' . $keywords . '%');
                          })
                          ->orWhereHas('related_sector', function ($q) use ($keywords) {
                            $q->where('sector_name', 'like', '%' . $keywords . '%');
                        });
                }
            });
        }

        if ($company_id) {
            $jobs->where('company_id', '=', $company_id);
        }

        if ($experience) {
            $minExperience = $experience[0];
            $maxExperience = $experience[1];
            $jobs->whereRaw('CAST(SUBSTRING_INDEX(experience, "-", 1) AS UNSIGNED) >= ?', [$minExperience])
            ->whereRaw('CAST(SUBSTRING_INDEX(experience, "-", -1) AS UNSIGNED) <= ?', [$maxExperience]);
        }

        if($sortby) {
            $sortby = intval($sortby);
                // Check if the $sortby value is 24, then use hours logic
                if ($sortby == 24) {
                    $jobs->where('created_at', '>=', Carbon::now()->subHours($sortby));
                } 
                // Else, assume it's in days
                else {
                    $jobs->where('created_at', '>=', Carbon::now()->subDays($sortby));
                }
            
        }


        if ($country) {
            $countryId = '';
            if (is_numeric($country)) {
                $country = $country;
            } else {
                $countryData = (new Country)->where('country_name', 'like', '%' . $country . '%')->where('status', 'active')->first();
                if ($countryData) {
                    $countryId = $countryData->id;
                }
            }

            $jobs->where('job_country', $countryId ? $countryId : $country);
        }

        if ($city) {
            $cityId = '';
            if (is_numeric($city)) {
                $cityId = $city;
            } else {
                $cityData = (new Cities)->where('city_name', 'like', '%' . $city . '%')->where('status', 'active')->first();
                if ($cityData) {
                    $cityId = $cityData->id;
                }
                $jobs->where('job_city', $cityId);
            }
        }

        if ($salary && $salary[0]) {
            $jobs->where('monthly_fixed_salary_min', '>=', $salary[0]);
        }

        if ($salary && $salary[1]) {
            $jobs->where('monthly_fixed_salary_max', '<=', $salary[1]);
        }

        if ($currency) {
            $jobs->where('monthly_fixed_salary_currency', 'like', '%' . $currency . '%');
        }

        if ($jobType) {
            if (is_array($jobType)) {
                $jobs->whereIn('job_type', $jobType);
            } else {
                $jobs->where('job_type', $jobType);
            }
        }

        if ($skill) {
            if (is_array($skill)) {
                $skillId = array();
                if (is_numeric($skill)) {
                    $skillId = $skill;
                } else {
                    $skillData = (new Skills)->where('skills', 'like', '%' . $skill . '%')->where('status', 'active')->first();
                    $skillId = $skillData->id;
                }
                $jobs->whereIn('skills_required', $skillId);
            } else {
                $skillId = '';
                if (is_numeric($skill)) {
                    $skillId = $skill;
                } else {
                    $skillData = (new Skills)->where('skills', 'like', '%' . $skill . '%')->where('status', 'active')->first();
                    $skillId = $skillData->id;
                }
                $jobs->where('skills_required', $skillId);
            }
        }

        if ($sector) {
            $sectorArray = explode(',', $sector);
            $jobs = $jobs->whereIn('sector_id', $sectorArray);
        }

        $ids = $jobs->pluck('id');
        $paginatedResult = $jobs->paginate($page_size);
        $saved = null;
        if ($user && $ids) {

            $saved = (new SavedJobs())
                ->where('user_id', $user->id)
                ->whereIn('job_id', $ids)
                ->pluck('job_id');
        }

        /*for trending jobs */
        $sectorNames = UserSearchJobs::whereNotNull('sector_name')
        ->orderByDesc('updated_at')  // Order by updated_at in descending order
        ->get()
        ->unique('sector_name')      // Get unique sector names
        ->take(8)
        ->pluck('sector_name');
        

        // Step 2: Prepare an array to hold sector names with job counts
        $sectorNamesWithCounts = [];

        // Step 3: Loop through each sector name and count jobs that match
        foreach ($sectorNames as $sector_name) {
            // Count jobs where 'job_title' contains the sector name
            // $jobCount = Job::where('job_title', 'like', '%' . $sector_name . '%')->where('job_status', 'active')->count();

            // new adding this
            $jobCount = Job::where(function($query) use ($sector_name) {
                $query->where('job_title', 'like', '%' . $sector_name . '%')
                      ->orWhereHas('company', function ($q) use ($sector_name) {
                          $q->where('company_name', 'like', '%' . $sector_name . '%');
                      })
                      ->orWhereHas('related_sector', function ($q) use ($sector_name) {
                          $q->where('sector_name', 'like', '%' . $sector_name . '%');
                      });
            })
            ->where('job_status', 'active')
            ->count();

            // Only push sector names that have more than 0 jobs
            if ($jobCount > 0) {
                $sectorNamesWithCounts[] = [
                    'sector_name' => $sector_name,
                    'job_count' => $jobCount
                ];
            }
        }
        /*end here*/


        return JobSearchResource::collection($paginatedResult)
            ->additional([
                'favorites' => $saved,
                // 'trending' => UserSearchJobs::whereNotNull('sector_name')
                //     ->orderByDesc('updated_at')
                //     ->take(8)
                //     ->get()
                //     ->pluck('sector_name'),
                'trending' => $sectorNamesWithCounts,
                'faq' => JobCountryFaqs::when(!empty($faqLocation), function ($query) use ($faqLocation) {
                    $query->where('location_name', $faqLocation);
                }, function ($query) {
                    $query->where('location_name', 'middle East');
                })
                    ->get()
            ]);
    }
}
