<?php

namespace Src\FileManagement\Infrastructure\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Src\FileManagement\Domain\File;

/**
 * @mixin File
 */
class FileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'uuid' => $this->uuid,
            'name' => $this->name,
            'size' => $this->size,
            'type' => $this->type,
            'description' => $this->description,
            //'public' => url('containers/' . $tenant . str_replace('containers', '', $this->source)),
            'source' => env('APP_URL') . Storage::url($this->source),
            'thumbnail' => env('APP_URL') . Storage::url($this->thumbnail),
            'created_at' => $this->created_at,
        ];
    }
}
