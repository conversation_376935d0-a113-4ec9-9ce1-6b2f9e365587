<?php

namespace App\Http\Controllers\Api\V1\FileManagement;

use App\Http\Controllers\Controller;
use App\Http\Resources\FileManagement\FileContainerResource;
use App\Http\Resources\FileManagement\FileResource;
use App\Models\User;
use Exception;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Src\FileManagement\Application\ChangeContainerVisibilityService;
use Src\FileManagement\Application\CreateContainerService;
use Src\FileManagement\Application\DestroyContainerUseCase;
use Src\FileManagement\Application\FindContainerService;
use Src\FileManagement\Application\GetContainerContentService;
use Src\FileManagement\Application\GetParentContainersUseCase;
use Src\FileManagement\Application\GetTrashContentUseCase;

class FileContainersController extends Controller
{
    use ApiResponseHelpers;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $authUser = (new User)->find(Auth::id());
            $containers = (new GetParentContainersUseCase($authUser))();

            if (count($containers) === 0) {
                return $this->respondNoContent();
            }

            return $this->respondWithSuccess(FileContainerResource::collection($containers));
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'container_uuid' => 'sometimes',
            'name' => 'required',
        ]);

        try {
            $authUser = (new User)->find(Auth::id());
            $name = $request->get('name');
            $Uuid = $request->get('container_uuid');
            $isPublic = $request->get('visibility') === 'public';

            $containers = (new CreateContainerService($authUser))($name, $isPublic, $Uuid);
            return $this->respondWithSuccess(new FileContainerResource($containers));
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $authUser = (new User)->find(Auth::id());

            return $this->respondWithSuccess();
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $request->validate([
            'name' => 'required',
        ]);

        try {
            //$authUser = (new User)->find(Auth::id());
            return $this->respondWithSuccess();
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $authUser = (new User)->find(Auth::id());
            $container = (new DestroyContainerUseCase($authUser))($id);
            return $this->respondWithSuccess($container);
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    /**
     * Return all the content of a container
     * @param string $uuid
     * @return JsonResponse
     */
    public function view(string $uuid): JsonResponse
    {
        try {
            $authUser = (new User)->find(Auth::id());
            $contents = (new GetContainerContentService($authUser))($uuid);
            $data = [
                'container' => $contents['container'],
                'containers' => $contents['containers'],
                'files' => FileResource::collection($contents['files'])
            ];
            return $this->respondWithSuccess($data);
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    public function getDeletedElements(): JsonResponse
    {
        try {
            $authUser = (new User)->find(Auth::id());
            $contents = (new GetTrashContentUseCase($authUser))();
            $data = [
                'container' => $contents['container'],
                'containers' => $contents['containers'],
                'files' => FileResource::collection($contents['files'])
            ];
            return $this->respondWithSuccess($data);
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }

    public function changeVisibility(Request $request, string $uuid): JsonResponse
    {
        $isPublic = $request->get('is_public');
        try {
            $container = (new FindContainerService)($uuid);
            (new ChangeContainerVisibilityService)($container, $isPublic);

            return $this->respondWithSuccess();
        } catch (Exception $exception) {
            return $this->respondError($exception->getMessage());
        }
    }
}
