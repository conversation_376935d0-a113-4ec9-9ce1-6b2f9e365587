{"private": true, "scripts": {"dev": "vite", "build": "vite build", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@popperjs/core": "^2.11.6", "@vitejs/plugin-vue": "^4.0.0", "axios": "^0.25", "bootstrap": "^5.2.3", "laravel-mix": "^6.0.6", "laravel-vite-plugin": "^0.7.4", "lodash": "^4.17.19", "postcss": "^8.1.14", "resolve-url-loader": "^5.0.0", "sass": "^1.56.1", "sass-loader": "^12.1.0", "vite": "^4.2.2", "vue": "^3.2.37"}}