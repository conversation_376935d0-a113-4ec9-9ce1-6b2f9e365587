<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogCategory;
use Carbon\Carbon;

class BlogCategorySeeder extends Seeder
{
    public function run(): void
    {
        BlogCategory::create( [
            'category_name'=>'Career Advice',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        BlogCategory::create( [
            'category_name'=>'Resume Tips',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        BlogCategory::create( [
            'category_name'=>'Expert Speak',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        BlogCategory::create( [
            'category_name'=>'News/ Updates',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
    }
}
