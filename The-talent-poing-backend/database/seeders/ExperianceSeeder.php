<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ExperianceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            'fresher',
            '0-1',
            '2-3',
            '3-5',
            '5-7',
            '7-10',
            '10-15',
            '15-20',
            '20+',
        ];

        foreach ($data as $exp) {
            DB::table('default_experience')->insert([
                'name' => $exp,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
