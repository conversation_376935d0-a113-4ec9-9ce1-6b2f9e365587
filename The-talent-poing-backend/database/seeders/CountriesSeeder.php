<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Country;
use Carbon\Carbon;

class CountriesSeeder extends Seeder

{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Country::create([

            'id' => 1,
            'country_name' => 'Afghanistan',
            'capital' => 'Kabul',
            'currency' => 'Afghani (AFN)',
            'flag' => 'twemoji_flag-afghanistan.png',
            'slug' => 'afghanistan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()

        ]);

        Country::create([
            'id' => 2,
            'country_name' => 'Armenia',
            'capital' => 'Yerevan',
            'currency' => 'Armenian Dram (AMD)',
            'flag' => 'twemoji_flag-armenia.png',
            'slug' => 'armenia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 3,
            'country_name' => 'Azerbaijan',
            'capital' => 'Baku',
            'currency' => 'Azerbaijani Manat (AZN)',
            'flag' => 'twemoji_flag-azerbaijan.png',
            'slug' => 'azerbaijan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 4,
            'country_name' => 'Bahrain',
            'capital' => 'Manama',
            'currency' => 'Bahraini Dinar (BHD)',
            'flag' => 'twemoji_flag-bahrain.png',
            'slug' => 'bahrain',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 5,
            'country_name' => 'Bangladesh',
            'capital' => 'Dhaka',
            'currency' => 'Bangladeshi Taka (BDT)',
            'flag' => 'twemoji_flag-bangladesh.png',
            'slug' => 'bangladesh',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 6,
            'country_name' => 'Bhutan',
            'capital' => 'Thimphu',
            'currency' => 'Ngultrum (BTN)',
            'flag' => 'twemoji_flag-bhutan.png',
            'slug' => 'bhutan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 7,
            'country_name' => 'Brunei',
            'capital' => 'Bandar Seri Begawan',
            'currency' => 'Brunei dollar (BND)',
            'flag' => 'twemoji_flag-brunei.png',
            'slug' => 'brunei',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 8,
            'country_name' => 'Cambodia',
            'capital' => 'Phnom Penh',
            'currency' => 'Cambodian Riel (KHR)',
            'flag' => 'twemoji_flag-cambodia.png',
            'slug' => 'cambodia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 9,
            'country_name' => 'China',
            'capital' => 'Beijing',
            'currency' => 'Chinese Yuan (CNY)',
            'flag' => 'twemoji_flag-china.png',
            'slug' => 'china',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 10,
            'country_name' => 'Cyprus',
            'capital' => 'Nicosia',
            'currency' => 'Euros (EUR)',
            'flag' => 'twemoji_flag-cyprus.png',
            'slug' => 'cyprus',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 11,
            'country_name' => 'Georgia',
            'capital' => 'Tbilisi',
            'currency' => 'Georgian Lari (GEL)',
            'flag' => 'twemoji_flag-georgia.png',
            'slug' => 'georgia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 12,
            'country_name' => 'India',
            'capital' => 'New Delhi',
            'currency' => 'Indian Rupee (INR)',
            'flag' => 'twemoji_flag-india.png',
            'slug' => 'india',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 13,
            'country_name' => 'Indonesia',
            'capital' => 'Jakarta',
            'currency' => 'Indonesian Rupiah (IDR)',
            'flag' => 'twemoji_flag-indonesia.png',
            'slug' => 'indonesia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 14,
            'country_name' => 'Iran',
            'capital' => 'Tehran',
            'currency' => 'Iranian Rial (IRR)',
            'flag' => 'twemoji_flag-iran.png',
            'slug' => 'iran',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 15,
            'country_name' => 'Iraq',
            'capital' => 'Baghdad',
            'currency' => 'Iraqi Dinar (IQD)',
            'flag' => 'twemoji_flag-iraq.png',
            'slug' => 'iraq',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 16,
            'country_name' => 'Israel',
            'capital' => 'Jerusalem',
            'currency' => 'Israeli Shekel (ILS)',
            'flag' => 'twemoji_flag-israel.png',
            'slug' => 'israel',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 17,
            'country_name' => 'Japan',
            'capital' => 'Tokyo',
            'currency' => 'Yen (JPY)',
            'flag' => 'twemoji_flag-japan.png',
            'slug' => 'japan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 18,
            'country_name' => 'Jordan',
            'capital' => 'Amman',
            'currency' => 'Jordanian Dinar (JOD)',
            'flag' => 'twemoji_flag-jordan.png',
            'slug' => 'jordan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 19,
            'country_name' => 'Kazakhstan',
            'capital' => 'Astana Kazakh',
            'currency' => 'Kazakhstani Tenge (KZT)',
            'flag' => 'twemoji_flag-kazakhstan.png',
            'slug' => 'kazakhstan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 20,
            'country_name' => 'Korea',
            'capital' => 'Seoul',
            'currency' => 'South Korean Won (KRW)',
            'flag' => 'twemoji_flag-south-korea.png',
            'slug' => 'korea',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 21,
            'country_name' => 'Kuwait',
            'capital' => 'Kuwait City',
            'currency' => 'Kuwaiti Dinar (KWD)',
            'flag' => 'twemoji_flag-kuwait.png',
            'slug' => 'kuwait',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 22,
            'country_name' => 'Kyrgyzstan',
            'capital' => 'Bishkek',
            'currency' => 'Kyrgyzstani Som (KGS)',
            'flag' => 'twemoji_flag-kyrgyzstan.png',
            'slug' => 'kyrgyzstan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 23,
            'country_name' => 'Laos',
            'capital' => 'Vientiane',
            'currency' => 'Laotian Kip (LAK)',
            'flag' => 'twemoji_flag-laos.png',
            'slug' => 'laos',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 24,
            'country_name' => 'Lebanon Lebanese Republic',
            'capital' => 'Beirut',
            'currency' => 'Lebanese Pound (LBP)',
            'flag' => 'twemoji_flag-lebanon.png',
            'slug' => 'lebanon-lebanese-republic',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 25,
            'country_name' => 'Malaysia',
            'capital' => 'Kuala Lumpur',
            'currency' => 'Malaysian Ringgit (MYR)',
            'flag' => 'twemoji_flag-malaysia.png',
            'slug' => 'malaysia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 26,
            'country_name' => 'Maldives',
            'capital' => 'Male / Male',
            'currency' => 'Maldivian Rufiyaa (MVR)',
            'flag' => 'twemoji_flag-maldives.png',
            'slug' => 'maldives',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 27,
            'country_name' => 'Mongolia',
            'capital' => 'Ulaanbaatar',
            'currency' => 'Mongolian Togrog or Turgik (MNT)',
            'flag' => 'twemoji_flag-mongolia.png',
            'slug' => 'mongolia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 28,
            'country_name' => 'Myanmar',
            'capital' => 'Nay Pyi Taw',
            'currency' => 'Myanmar Kyat (MMK)',
            'flag' => 'twemoji_flag-myanmar-burma.png',
            'slug' => 'myanmar',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 29,
            'country_name' => 'Nepal',
            'capital' => 'Kathmandu',
            'currency' => 'Nepalese Rupee (Rs)',
            'flag' => 'twemoji_flag-nepal.png',
            'slug' => 'nepal',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 30,
            'country_name' => 'Oman',
            'capital' => 'Muscat',
            'currency' => 'Omani Rial (OMR)',
            'flag' => 'twemoji_flag-oman.png',
            'slug' => 'oman',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 31,
            'country_name' => 'Pakistan',
            'capital' => 'Islamabad',
            'currency' => 'Pakistan Rupee (PKR)',
            'flag' => 'twemoji_flag-pakistan.png',
            'slug' => 'pakistan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 32,
            'country_name' => 'Palestine',
            'capital' => 'Jerusalem (claimed)',
            'currency' => 'Israeli new shekel (ILS)',
            'flag' => 'twemoji_flag-palestinian-territories.png',
            'slug' => 'palestine',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 33,
            'country_name' => 'Philippines',
            'capital' => 'Manila',
            'currency' => 'Philippine Peso (PHP)',
            'flag' => 'twemoji_flag-philippines.png',
            'slug' => 'philippines',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 34,
            'country_name' => 'Qatar',
            'capital' => 'Doha',
            'currency' => 'Qatari Riyal (QAR)',
            'flag' => 'twemoji_flag-qatar.png',
            'slug' => 'qatar',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 35,
            'country_name' => 'Russia',
            'capital' => 'Moscow',
            'currency' => 'Russian Ruble (RUB)',
            'flag' => 'twemoji_flag-russia.png',
            'slug' => 'russia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 36,
            'country_name' => 'Saudi Arabia',
            'capital' => 'Riyadh',
            'currency' => 'Saudi Riyal (SAR)',
            'flag' => 'twemoji_flag-saudi-arabia.png',
            'slug' => 'saudi-arabia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 37,
            'country_name' => 'Singapore',
            'capital' => 'Singapore',
            'currency' => 'Singapore Dollar (SGD)',
            'flag' => 'twemoji_flag-singapore.png',
            'slug' => 'singapore',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 38,
            'country_name' => 'Sri Lanka',
            'capital' => 'Sri Jayawardenapura Kotte',
            'currency' => 'Sri Lankan Rupee (LKR)',
            'flag' => 'twemoji_flag-sri-lanka.png',
            'slug' => 'sri-lanka',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 39,
            'country_name' => 'Syria',
            'capital' => 'Damascus',
            'currency' => 'Syrian Pound (SYP)',
            'flag' => 'twemoji_flag-syria.png',
            'slug' => 'syria',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 40,
            'country_name' => 'Tajikistan',
            'capital' => 'Dushanbe',
            'currency' => 'Tajikistani Somoni (TJS)',
            'flag' => 'twemoji_flag-tajikistan.png',
            'slug' => 'tajikistan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 41,
            'country_name' => 'Thailand',
            'capital' => 'Bangkok',
            'currency' => 'Thai Bhat (THB)',
            'flag' => 'twemoji_flag-thailand.png',
            'slug' => 'thailand',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 42,
            'country_name' => 'Timor-Leste/East Timor',
            'capital' => 'Dili',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-timor-leste.png',
            'slug' => 'timor-lesteeast-timor',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 43,
            'country_name' => 'Turkey',
            'capital' => 'Ankara',
            'currency' => 'Turkish Lira (TRY)',
            'flag' => 'twemoji_flag-turkey.png',
            'slug' => 'turkey',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 44,
            'country_name' => 'Tur kmenistan',
            'capital' => 'Ashgabat',
            'currency' => 'Turkmenistan Manat (TMT)',
            'flag' => 'twemoji_flag-tur-kmenistan.png',
            'slug' => 'tur-kmenistan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 45,
            'country_name' => 'United Arab Emirates',
            'capital' => 'Abu Dhabi',
            'currency' => 'UAE Dirham (AED)',
            'flag' => 'twemoji_flag-united-arab-emirates.png',
            'slug' => 'united-arab-emirates',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 46,
            'country_name' => 'Uzbekistan',
            'capital' => 'Tashkent',
            'currency' => 'Uzbekistani Som (UZS)',
            'flag' => 'twemoji_flag-uzbekistan.png',
            'slug' => 'uzbekistan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 47,
            'country_name' => 'Vietnam / Viet Nam',
            'capital' => 'Hanoi',
            'currency' => 'Vietnamese Dong (VND)',
            'flag' => 'twemoji_flag-vietnam.png',
            'slug' => 'vietnam-viet-nam',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 48,
            'country_name' => 'Yemen',
            'capital' => 'Sanaa',
            'currency' => 'Yemeni Rial (YER)',
            'flag' => 'twemoji_flag-yemen.png',
            'slug' => 'yemen',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 49,
            'country_name' => 'Algeria',
            'capital' => 'Algiers',
            'currency' => 'Algerian Dinar (DZD)',
            'flag' => 'twemoji_flag-algeria.png',
            'slug' => 'algeria',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 50,
            'country_name' => 'Angola',
            'capital' => 'Luanda',
            'currency' => 'Angolan Kwanza (AOA)',
            'flag' => 'twemoji_flag-angola.png',
            'slug' => 'angola',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 51,
            'country_name' => 'Benin',
            'capital' => 'Porto-Novo',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-benin.png',
            'slug' => 'benin',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 52,
            'country_name' => 'Botswana',
            'capital' => 'Gaborone',
            'currency' => 'Botswana Pula (BWP)',
            'flag' => 'twemoji_flag-botswana.png',
            'slug' => 'botswana',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 53,
            'country_name' => 'Burkina Faso',
            'capital' => 'Ouagadougou',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-burkina-faso.png',
            'slug' => 'burkina-faso',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()

        ]);


        Country::create([
            'id' => 54,
            'country_name' => 'Burundi',
            'capital' => 'Bujumbura',
            'currency' => 'Burundian Franc (BIF)',
            'flag' => 'twemoji_flag-burundi.png',
            'slug' => 'burundi',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 55,
            'country_name' => 'Cameroon',
            'capital' => 'Yaounde',
            'currency' => 'Central African CFA Franc (XAF)',
            'flag' => 'twemoji_flag-cameroon.png',
            'slug' => 'cameroon',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 56,
            'country_name' => 'Cape Verde',
            'capital' => 'Praia',
            'currency' => 'Cape Verdean Escudo (CVE)',
            'flag' => 'twemoji_flag-cape-verde.png',
            'slug' => 'cape-verde',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 57,
            'country_name' => 'Central African Rep.',
            'capital' => 'Bangui',
            'currency' => 'Central African CFA franc (XAF)',
            'flag' => 'twemoji_flag-central-african-republic.png',
            'slug' => 'central-african-rep',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 58,
            'country_name' => 'Chad',
            'capital' => 'N Djamena',
            'currency' => 'Central African CFA Franc (XAF)',
            'flag' => 'twemoji_flag-chad.png',
            'slug' => 'chad',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 59,
            'country_name' => 'Comoros',
            'capital' => 'Moroni',
            'currency' => 'Comorian franc (KMF)',
            'flag' => 'twemoji_flag-comoros.png',
            'slug' => 'comoros',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 60,
            'country_name' => 'Congo',
            'capital' => 'Brazzaville',
            'currency' => 'Congolese Franc (CDF)',
            'flag' => 'twemoji_flag-congo-brazzaville.png',
            'slug' => 'congo',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 61,
            'country_name' => 'Côte d Ivoire',
            'capital' => 'Yamoussoukro',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-cote-divoire.png',
            'slug' => 'cote-divoire',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 62,
            'country_name' => 'Democratic Republic of Congo',
            'capital' => 'Kinshasa',
            'currency' => 'Congolese Franc (CDF)',
            'flag' => 'twemoji_flag-congo-kinshasa.png',
            'slug' => 'democratic-republic-of-congo',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 63,
            'country_name' => 'Djibouti',
            'capital' => 'Djibouti',
            'currency' => 'Djiboutian franc (DJF)',
            'flag' => 'twemoji_flag-djibouti.png',
            'slug' => 'djibouti',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 64,
            'country_name' => 'Egypt',
            'capital' => 'Cairo',
            'currency' => 'Egyptian Pound (EGP)',
            'flag' => 'twemoji_flag-egypt.png',
            'slug' => 'egypt',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 65,
            'country_name' => 'Equatorial Guinea',
            'capital' => 'Malabo',
            'currency' => 'Central African CFA franc (CFA)',
            'flag' => 'twemoji_flag-equatorial-guinea.png',
            'slug' => 'equatorial-guinea',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 66,
            'country_name' => 'Eritrea',
            'capital' => 'Asmara',
            'currency' => 'Eritrean nakfa (ERN)',
            'flag' => 'twemoji_flag-eritrea.png',
            'slug' => 'eritrea',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 67,
            'country_name' => 'Ethiopia',
            'capital' => 'Addis Ababa',
            'currency' => 'Ethiopian Birr (ETB)',
            'flag' => 'twemoji_flag-ethiopia.png',
            'slug' => 'ethiopia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 68,
            'country_name' => 'Gabon',
            'capital' => 'Libreville',
            'currency' => 'Central African Franc (XFA)',
            'flag' => 'twemoji_flag-gabon.png',
            'slug' => 'gabon',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 69,
            'country_name' => 'Gambia',
            'capital' => 'Banjul',
            'currency' => 'Gambian dalasi (GMD)',
            'flag' => 'twemoji_flag-gambia.png',
            'slug' => 'gambia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 70,
            'country_name' => 'Ghana',
            'capital' => 'Accra',
            'currency' => 'Ghanaian Cedi (GHS)',
            'flag' => 'twemoji_flag-ghana.png',
            'slug' => 'ghana',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 71,
            'country_name' => 'Guinea',
            'capital' => 'Conakry',
            'currency' => 'Guinean Franc (GNF)',
            'flag' => 'twemoji_flag-guinea.png',
            'slug' => 'guinea',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 72,
            'country_name' => 'Guinea-Bissau',
            'capital' => 'Bissau',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-guinea-bissau.png',
            'slug' => 'guinea-bissau',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 73,
            'country_name' => 'Kenya',
            'capital' => 'Nairobi',
            'currency' => 'Kenyan Shilling (KES)',
            'flag' => 'twemoji_flag-kenya.png',
            'slug' => 'kenya',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 74,
            'country_name' => 'Lesotho',
            'capital' => 'Maseru',
            'currency' => 'Lesotho Loti (LSL)',
            'flag' => 'twemoji_flag-lesotho.png',
            'slug' => 'lesotho',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 75,
            'country_name' => 'Liberia',
            'capital' => 'Monrovia',
            'currency' => 'Liberian Dollar (LRD)',
            'flag' => 'twemoji_flag-liberia.png',
            'slug' => 'liberia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 76,
            'country_name' => 'Libya',
            'capital' => 'Tripoli',
            'currency' => 'Libyan Dinar (LYD)',
            'flag' => 'twemoji_flag-libya.png',
            'slug' => 'libya',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 77,
            'country_name' => 'Madagascar',
            'capital' => 'Antananarivo',
            'currency' => 'Malagasy Ariary (Ar/MGA)',
            'flag' => 'twemoji_flag-madagascar.png',
            'slug' => 'madagascar',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 78,
            'country_name' => 'Malawi',
            'capital' => 'Lilongwe',
            'currency' => ' Malawian Kwacha (MWK)',
            'flag' => 'twemoji_flag-malawi.png',
            'slug' => 'malawi',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 79,
            'country_name' => 'Mali',
            'capital' => 'Bamako',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-mali.png',
            'slug' => 'mali',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 80,
            'country_name' => 'Mauritania',
            'capital' => 'Nouakchott',
            'currency' => 'Mauritanian Ouguiya (MRU)',
            'flag' => 'twemoji_flag-mauritania.png',
            'slug' => 'mauritania',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 81,
            'country_name' => 'Mauritius',
            'capital' => 'Mauritius',
            'currency' => 'Mauritian Rupee (MUR)',
            'flag' => 'twemoji_flag-mauritius.png',
            'slug' => 'mauritius',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 82,
            'country_name' => 'Morocco',
            'capital' => 'Rabat',
            'currency' => 'Moroccan Dirham (MAD)',
            'flag' => 'twemoji_flag-morocco.png',
            'slug' => 'morocco',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 83,
            'country_name' => 'Mozambique',
            'capital' => 'Maputo',
            'currency' => 'Mozambican Metical (MZN)',
            'flag' => 'twemoji_flag-mozambique.png',
            'slug' => 'mozambique',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 84,
            'country_name' => 'Namibia',
            'capital' => 'Windhoek',
            'currency' => 'Namibian Dollar (NAD)',
            'flag' => 'twemoji_flag-namibia.png',
            'slug' => 'namibia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 85,
            'country_name' => 'Niger',
            'capital' => 'Niamey',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-niger.png',
            'slug' => 'niger',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 86,
            'country_name' => 'Nigeria',
            'capital' => 'Abuja',
            'currency' => 'Nigerian Naira (NGN)',
            'flag' => 'twemoji_flag-nigeria.png',
            'slug' => 'nigeria',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 87,
            'country_name' => 'Rwanda',
            'capital' => 'Kigali',
            'currency' => 'Rwandan Franc (RWF)',
            'flag' => 'twemoji_flag-rwanda.png',
            'slug' => 'rwanda',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 88,
            'country_name' => 'Sao Tome & Principe',
            'capital' => 'Sao Tome',
            'currency' => ' Sao Tome and Principe Dobra (STN)',
            'flag' => 'twemoji_flag-sao-tome-and-principe.png',
            'slug' => 'sao-tome-principe',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 89,
            'country_name' => 'Senegal',
            'capital' => 'Dakar',
            'currency' => 'West African CFA Franc (XOF)',
            'flag' => 'twemoji_flag-senegal.png',
            'slug' => 'senegal',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 90,
            'country_name' => 'Seychelles',
            'capital' => 'Victoria',
            'currency' => 'Seychellois Rupee (SCR)',
            'flag' => 'twemoji_flag-seychelles.png',
            'slug' => 'seychelles',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 91,
            'country_name' => 'Sierra Leone',
            'capital' => 'Freetown',
            'currency' => 'Sierra Leonean Leone (SLL)',
            'flag' => 'twemoji_flag-sierra-leone.png',
            'slug' => 'sierra-leone',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 92,
            'country_name' => 'Somalia',
            'capital' => 'Mogadishu',
            'currency' => 'Somali Shilling',
            'flag' => 'twemoji_flag-somalia.png',
            'slug' => 'somalia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 93,
            'country_name' => 'South Africa',
            'capital' => 'Pretoria',
            'currency' => 'South African Rand (ZAR)',
            'flag' => 'twemoji_flag-south-africa.png',
            'slug' => 'south-africa',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 94,
            'country_name' => 'South Sudan',
            'capital' => 'Juba',
            'currency' => 'South Sudanese pound (SS£)',
            'flag' => 'twemoji_flag-south-sudan.png',
            'slug' => 'south-sudan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 95,
            'country_name' => 'Sudan',
            'capital' => 'Khartoum',
            'currency' => 'Sudanese Pound (SDG)',
            'flag' => 'twemoji_flag-sudan.png',
            'slug' => 'sudan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 96,
            'country_name' => 'Swaziland',
            'capital' => 'Mbabane',
            'currency' => 'Lilangeni (SZL)',
            'flag' => 'twemoji_flag-eswatini.png',
            'slug' => 'swaziland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 97,
            'country_name' => 'Tanzania',
            'capital' => 'Dodoma',
            'currency' => 'Tanzanian Shilling (TZS)',
            'flag' => 'twemoji_flag-tanzania.png',
            'slug' => 'tanzania',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 98,
            'country_name' => 'Togo',
            'capital' => 'Lomé',
            'currency' => 'Togolese Franc (XOF)',
            'flag' => 'twemoji_flag-togo.png',
            'slug' => 'togo',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 99,
            'country_name' => 'Tunisia',
            'capital' => 'Tunis',
            'currency' => 'Tunisian Dinar (TND)',
            'flag' => 'twemoji_flag-tunisia.png',
            'slug' => 'tunisia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 100,
            'country_name' => 'Uganda',
            'capital' => 'Kampala',
            'currency' => 'Ugandan Shilling (UGX)',
            'flag' => 'twemoji_flag-uganda.png',
            'slug' => 'uganda',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 101,
            'country_name' => 'Zambia',
            'capital' => 'Lusaka',
            'currency' => 'Zambian Kwacha (ZMW)',
            'flag' => 'twemoji_flag-zambia.png',
            'slug' => 'zambia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 102,
            'country_name' => 'Zimbabwe',
            'capital' => 'Harare',
            'currency' => 'Zimbabwe Dollar (ZWD)',
            'flag' => 'twemoji_flag-zimbabwe.png',
            'slug' => 'zimbabwe',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 103,
            'country_name' => 'Antigua and Barbuda',
            'capital' => 'St. John s',
            'currency' => 'Eastern Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-antigua-and-barbuda.png',
            'slug' => 'antigua-and-barbuda',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 104,
            'country_name' => 'Bahamas',
            'capital' => 'Nassau',
            'currency' => 'Bahamian dollar (BSD)',
            'flag' => 'twemoji_flag-bahamas.png',
            'slug' => 'bahamas',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 105,
            'country_name' => 'Barbados',
            'capital' => 'Bridgetown',
            'currency' => 'Barbados Dollar (BBD)',
            'flag' => 'twemoji_flag-barbados.png',
            'slug' => 'barbados',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 106,
            'country_name' => 'Belize',
            'capital' => 'Belmopan',
            'currency' => 'Belize Dollar (BZD)',
            'flag' => 'twemoji_flag-belize.png',
            'slug' => 'belize',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 107,
            'country_name' => 'Canada',
            'capital' => 'Ottawa',
            'currency' => 'Canadian Dollar (CAD)',
            'flag' => 'twemoji_flag-canada.png',
            'slug' => 'canada',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 108,
            'country_name' => 'Costa Rica',
            'capital' => 'San José',
            'currency' => 'Costa Rican Colón (CRC)',
            'flag' => 'twemoji_flag-costa-rica.png',
            'slug' => 'costa-rica',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 109,
            'country_name' => 'Cuba',
            'capital' => 'Havana',
            'currency' => 'Cuban Peso (CUP)',
            'flag' => 'twemoji_flag-cuba.png',
            'slug' => 'cuba',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 110,
            'country_name' => 'Dominica',
            'capital' => 'Roseau',
            'currency' => 'Eastern Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-dominica.png',
            'slug' => 'dominica',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 111,
            'country_name' => 'Dominican Republic',
            'capital' => 'Santo Domingo',
            'currency' => 'Dominican Peso (DOP)',
            'flag' => 'twemoji_flag-dominican-republic.png',
            'slug' => 'dominican-republic',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 112,
            'country_name' => 'El Salvador',
            'capital' => 'San Salvador',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-el-salvador.png',
            'slug' => 'el-salvador',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 113,
            'country_name' => 'Grenada',
            'capital' => 'St. George s',
            'currency' => 'Eastern Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-grenada.png',
            'slug' => 'grenada',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 114,
            'country_name' => 'Guatemala',
            'capital' => 'Guatemala City',
            'currency' => 'Quetzal (GTQ)',
            'flag' => 'twemoji_flag-guatemala.png',
            'slug' => 'guatemala',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 115,
            'country_name' => 'Haiti',
            'capital' => 'Port au Prince',
            'currency' => 'Haitian gourde (HTG)',
            'flag' => 'twemoji_flag-haiti.png',
            'slug' => 'haiti',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 116,
            'country_name' => 'Honduras',
            'capital' => 'Tegucigalpa',
            'currency' => 'Lempira (HNL)',
            'flag' => 'twemoji_flag-honduras.png',
            'slug' => 'honduras',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 117,
            'country_name' => 'Jamaica',
            'capital' => 'Kingston',
            'currency' => 'Jamaican Dollar (JMD)',
            'flag' => 'twemoji_flag-jamaica.png',
            'slug' => 'jamaica',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 118,
            'country_name' => 'Mexico',
            'capital' => 'Mexico City',
            'currency' => 'Mexican Peso (MXN)',
            'flag' => 'twemoji_flag-mexico.png',
            'slug' => 'mexico',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 119,
            'country_name' => 'Nicaragua',
            'capital' => 'Managua',
            'currency' => 'Nicaraguan Córdoba (NIO)',
            'flag' => 'twemoji_flag-nicaragua.png',
            'slug' => 'nicaragua',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 120,
            'country_name' => 'Panama',
            'capital' => 'Panama City',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-panama.png',
            'slug' => 'panama',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 121,
            'country_name' => 'Saint Kitts and Nevis',
            'capital' => 'Basseterre',
            'currency' => 'East Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-st-kitts-and-nevis.png',
            'slug' => 'saint-kitts-and-nevis',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 122,
            'country_name' => 'Saint Lucia',
            'capital' => 'Castries',
            'currency' => 'Eastern Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-st-lucia.png',
            'slug' => 'saint-lucia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 123,
            'country_name' => 'Saint Vincent and the Grenadines',
            'capital' => 'Kingstown',
            'currency' => 'East Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-st-vincent-and-grenadines.png',
            'slug' => 'saint-vincent-and-the-grenadines',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 124,
            'country_name' => 'Trinidad and Tobago',
            'capital' => 'Port of Spain',
            'currency' => 'Trinidad and Tobago Dollar (TTD)',
            'flag' => 'twemoji_flag-trinidad-and-tobago.png',
            'slug' => 'trinidad-and-tobago',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 125,
            'country_name' => 'United States of America',
            'capital' => 'Washington D.C.',
            'currency' => 'United State Dollar (USD)',
            'flag' => 'twemoji_flag-united-states.png',
            'slug' => 'united-states-of-america',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 126,
            'country_name' => 'Argentina',
            'capital' => 'Buenos Aires',
            'currency' => 'Argentine Peso (ARS)',
            'flag' => 'twemoji_flag-argentina.png',
            'slug' => 'argentina',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 127,
            'country_name' => 'Bolivia',
            'capital' => 'La Paz',
            'currency' => 'Boliviano (BOB)',
            'flag' => 'twemoji_flag-bolivia.png',
            'slug' => 'bolivia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 128,
            'country_name' => 'Brazil',
            'capital' => 'Brasilia',
            'currency' => 'Brazilian Real (BRL)',
            'flag' => 'twemoji_flag-brazil.png',
            'slug' => 'brazil',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 129,
            'country_name' => 'Chile',
            'capital' => 'Santiago',
            'currency' => 'Chilean Peso (CLP)',
            'flag' => 'twemoji_flag-chile.png',
            'slug' => 'chile',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 130,
            'country_name' => 'Colombia',
            'capital' => 'Bogota',
            'currency' => 'Colombian Peso (COP)',
            'flag' => 'twemoji_flag-colombia.png',
            'slug' => 'colombia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 131,
            'country_name' => 'Ecuador',
            'capital' => 'Quito',
            'currency' => 'United State Dollar (USD)',
            'flag' => 'twemoji_flag-ecuador.png',
            'slug' => 'ecuador',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 132,
            'country_name' => 'Guyana',
            'capital' => 'Georgetown',
            'currency' => 'Guyanese Dollar (GYD)',
            'flag' => 'twemoji_flag-guyana.png',
            'slug' => 'guyana',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 133,
            'country_name' => 'Paraguay',
            'capital' => 'Asuncion',
            'currency' => 'Paraguayan Guaraní (PYG)',
            'flag' => 'twemoji_flag-paraguay.png',
            'slug' => 'paraguay',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 134,
            'country_name' => 'Peru',
            'capital' => 'Lima',
            'currency' => 'Peruvian Sol (PEN)',
            'flag' => 'twemoji_flag-peru.png',
            'slug' => 'peru',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 135,
            'country_name' => 'Suriname',
            'capital' => 'Paramaribo',
            'currency' => 'Surinamese Dollar (SRD)',
            'flag' => 'twemoji_flag-suriname.png',
            'slug' => 'suriname',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 136,
            'country_name' => 'Uruguay',
            'capital' => 'Montevideo',
            'currency' => 'Uruguayan Peso (UYU)',
            'flag' => 'twemoji_flag-uruguay.png',
            'slug' => 'uruguay',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 137,
            'country_name' => 'Venezuela',
            'capital' => 'Caracas',
            'currency' => 'Venezuelan Bolivar (VES)',
            'flag' => 'twemoji_flag-venezuela.png',
            'slug' => 'venezuela',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 138,
            'country_name' => 'French Guiana',
            'capital' => 'Cayenne',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-french-guiana.png',
            'slug' => 'french-guiana',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 139,
            'country_name' => 'Falkland Islands',
            'capital' => 'Stanley',
            'currency' => 'Falkland Islands Pound (FKP)',
            'flag' => 'twemoji_flag-falkland-islands.png',
            'slug' => 'falkland-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 140,
            'country_name' => 'Albania',
            'capital' => 'Tirane',
            'currency' => 'Albanian Lek (ALL)',
            'flag' => 'twemoji_flag-albania.png',
            'slug' => 'albania',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 141,
            'country_name' => 'Andorra',
            'capital' => 'Andorra-la-vella',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-andorra.png',
            'slug' => 'andorra',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 143,
            'country_name' => 'Austria',
            'capital' => 'Vienna',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-austria.png',
            'slug' => 'austria',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 145,
            'country_name' => 'Belarus',
            'capital' => 'Minsk',
            'currency' => 'Belarusian ruble (BYN)',
            'flag' => 'twemoji_flag-belarus.png',
            'slug' => 'belarus',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 146,
            'country_name' => 'Belgium',
            'capital' => 'Brussels',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-belgium.png',
            'slug' => 'belgium',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 147,
            'country_name' => 'Bosnia & Herzegovina',
            'capital' => 'Sarajevo',
            'currency' => 'Bosnian Herzegovinian Convertible Mark (BAM)',
            'flag' => 'twemoji_flag-bosnia-and-herzegovina.png',
            'slug' => 'bosnia-herzegovina',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 148,
            'country_name' => 'Bulgaria',
            'capital' => 'Sofia',
            'currency' => 'Bulgarian Lev (BGN)',
            'flag' => 'twemoji_flag-bulgaria.png',
            'slug' => 'bulgaria',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 149,
            'country_name' => 'Croatia',
            'capital' => 'Zagreb',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-croatia.png',
            'slug' => 'croatia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 151,
            'country_name' => 'Czech Republic',
            'capital' => 'Prague',
            'currency' => 'Crown - Czech (CZK)',
            'flag' => 'twemoji_flag-czechia.png',
            'slug' => 'czech-republic',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 152,
            'country_name' => 'Denmark',
            'capital' => 'Copenhagen',
            'currency' => 'Danish Krone (DKK)',
            'flag' => 'twemoji_flag-denmark.png',
            'slug' => 'denmark',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 153,
            'country_name' => 'Estonia',
            'capital' => 'Tallinn',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-estonia.png',
            'slug' => 'estonia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 154,
            'country_name' => 'Finland',
            'capital' => 'Helsinki',
            'currency' => 'Euros (EUR)',
            'flag' => 'twemoji_flag-finland.png',
            'slug' => 'finland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 155,
            'country_name' => 'France',
            'capital' => 'Paris',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-france.png',
            'slug' => 'france',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 158,
            'country_name' => 'Germany',
            'capital' => 'Berlin',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-germany.png',
            'slug' => 'germany',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 159,
            'country_name' => 'Greece',
            'capital' => 'Athens',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-greece.png',
            'slug' => 'greece',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 160,
            'country_name' => 'Hungary',
            'capital' => 'Budapest',
            'currency' => 'Florin (HUF)',
            'flag' => 'twemoji_flag-hungary.png',
            'slug' => 'hungary',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 161,
            'country_name' => 'Iceland',
            'capital' => 'Reykjavik',
            'currency' => 'Icelandic Króna (ISK)',
            'flag' => 'twemoji_flag-iceland.png',
            'slug' => 'iceland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 162,
            'country_name' => 'Italy',
            'capital' => 'Rome',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-italy.png',
            'slug' => 'italy',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 163,
            'country_name' => 'Latvia',
            'capital' => 'Riga',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-latvia.png',
            'slug' => 'latvia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 164,
            'country_name' => 'Liechtenstein',
            'capital' => 'Vaduz',
            'currency' => 'Swiss franc (CHF)',
            'flag' => 'twemoji_flag-liechtenstein.png',
            'slug' => 'liechtenstein',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 165,
            'country_name' => 'Lithuania',
            'capital' => 'Vilnius',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-lithuania.png',
            'slug' => 'lithuania',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 166,
            'country_name' => 'Luxembourg',
            'capital' => 'Luxembourg',
            'currency' => 'Euros (EUR)',
            'flag' => 'twemoji_flag-luxembourg.png',
            'slug' => 'luxembourg',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 167,
            'country_name' => 'Macedonia',
            'capital' => 'Skopje',
            'currency' => 'Macedonian Denar (MKD)',
            'flag' => 'twemoji_flag-north-macedonia.png',
            'slug' => 'macedonia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 168,
            'country_name' => 'Malta',
            'capital' => 'Valletta',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-malta.png',
            'slug' => 'malta',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 169,
            'country_name' => 'Martinique',
            'capital' => 'Fort-de-France',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-martinique.png',
            'slug' => 'martinique',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 170,
            'country_name' => 'Moldova',
            'capital' => 'Kishinev',
            'currency' => 'Moldovan leu (MDL)',
            'flag' => 'twemoji_flag-moldova.png',
            'slug' => 'moldova',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 171,
            'country_name' => 'Monaco',
            'capital' => 'Monaco',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-monaco.png',
            'slug' => 'monaco',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 172,
            'country_name' => 'Netherlands',
            'capital' => 'Amsterdam',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-netherlands.png',
            'slug' => 'netherlands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 173,
            'country_name' => 'Norway',
            'capital' => 'Oslo',
            'currency' => 'Norwegian Krone (NOK)',
            'flag' => 'twemoji_flag-norway.png',
            'slug' => 'norway',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 174,
            'country_name' => 'Northern Ireland',
            'capital' => 'Belfast',
            'currency' => 'British Pound (GBP)',
            'flag' => 'twemoji_flag-united-kingdom.png',
            'slug' => 'northern-ireland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 175,
            'country_name' => 'Poland',
            'capital' => 'Warsaw',
            'currency' => 'Polish Zloty (PLN)',
            'flag' => 'twemoji_flag-poland.png',
            'slug' => 'poland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 176,
            'country_name' => 'Portugal',
            'capital' => 'Lisbon',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-portugal.png',
            'slug' => 'portugal',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 177,
            'country_name' => 'Romania',
            'capital' => 'Bucharest',
            'currency' => 'Romanian Leu (RON)',
            'flag' => 'twemoji_flag-romania.png',
            'slug' => 'romania',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 179,
            'country_name' => 'San Marino',
            'capital' => 'San Marino',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-san-marino.png',
            'slug' => 'san-marino',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 180,
            'country_name' => 'Scotland',
            'capital' => 'Edinburgh',
            'currency' => 'Pound Sterling (GBP)',
            'flag' => 'twemoji_flag-scotland.png',
            'slug' => 'scotland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 181,
            'country_name' => 'Slovakia',
            'capital' => 'Bratislava',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-slovakia.png',
            'slug' => 'slovakia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 182,
            'country_name' => 'Slovenia',
            'capital' => 'Ljubljana',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-slovenia.png',
            'slug' => 'slovenia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 183,
            'country_name' => 'Spain',
            'capital' => 'Madrid',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-spain.png',
            'slug' => 'spain',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 184,
            'country_name' => 'Sweden',
            'capital' => 'Stockholm',
            'currency' => 'Swedish Krona (SEK)',
            'flag' => 'twemoji_flag-sweden.png',
            'slug' => 'sweden',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 185,
            'country_name' => 'Switzerland',
            'capital' => 'Berne',
            'currency' => 'Swiss Franc (CHF)',
            'flag' => 'twemoji_flag-switzerland.png',
            'slug' => 'switzerland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 187,
            'country_name' => 'Ukraine',
            'capital' => 'Kiev',
            'currency' => 'Ukrainian Hryvnia (UAH)',
            'flag' => 'twemoji_flag-ukraine.png',
            'slug' => 'ukraine',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 188,
            'country_name' => 'United Kingdom',
            'capital' => 'London',
            'currency' => 'Pound Sterling (GBP)',
            'flag' => 'twemoji_flag-united-kingdom.png',
            'slug' => 'united-kingdom',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 190,
            'country_name' => 'Vatican City',
            'capital' => 'Vatican City',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-vatican-city.png',
            'slug' => 'vatican-city',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 191,
            'country_name' => 'Yugoslavia',
            'capital' => 'Belgrade',
            'currency' => 'Yugoslav dinar (YUD)',
            'flag' => 'twemoji_flag-syria.png',
            'slug' => 'yugoslavia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 192,
            'country_name' => 'Australia',
            'capital' => 'Canberra',
            'currency' => 'Australian Dollar (AUD)',
            'flag' => 'twemoji_flag-australia.png',
            'slug' => 'australia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 193,
            'country_name' => 'Fiji',
            'capital' => 'Suva',
            'currency' => 'Fijian Dollar (FJD)',
            'flag' => 'twemoji_flag-fiji.png',
            'slug' => 'fiji',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 194,
            'country_name' => 'Kiribati',
            'capital' => 'Tarawa',
            'currency' => 'Australian Dollar (AUD)',
            'flag' => 'twemoji_flag-kiribati.png',
            'slug' => 'kiribati',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 195,
            'country_name' => 'Marshall Islands',
            'capital' => 'Majuro',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-marshall-islands.png',
            'slug' => 'marshall-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 196,
            'country_name' => 'Micronesia',
            'capital' => 'Palikir',
            'currency' => 'United States dollar (USD)',
            'flag' => 'twemoji_flag-micronesia.png',
            'slug' => 'micronesia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 197,
            'country_name' => 'Nauru',
            'capital' => 'Yaren',
            'currency' => 'Australian Dollar (AUD)',
            'flag' => 'twemoji_flag-nauru.png',
            'slug' => 'nauru',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 198,
            'country_name' => 'New Zealand',
            'capital' => 'Wellington',
            'currency' => 'New Zealand Dollar (NZD)',
            'flag' => 'twemoji_flag-new-zealand.png',
            'slug' => 'new-zealand',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 199,
            'country_name' => 'Palau',
            'capital' => 'Koror',
            'currency' => 'Fortnightly (USD)',
            'flag' => 'twemoji_flag-palau.png',
            'slug' => 'palau',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 200,
            'country_name' => 'Papua New Guinea',
            'capital' => 'Port Moresby',
            'currency' => 'Kina (PGK)',
            'flag' => 'twemoji_flag-papua-new-guinea.png',
            'slug' => 'papua-new-guinea',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 201,
            'country_name' => 'Samoa',
            'capital' => 'Apia',
            'currency' => 'Samoan Tala (WST)',
            'flag' => 'twemoji_flag-samoa.png',
            'slug' => 'samoa',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 202,
            'country_name' => 'Solomon Islands',
            'capital' => 'Honiara',
            'currency' => 'Solomon Islands Dollar (SBD)',
            'flag' => 'twemoji_flag-solomon-islands.png',
            'slug' => 'solomon-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 203,
            'country_name' => 'Tonga',
            'capital' => 'Nukualofa',
            'currency' => 'Tongan Paanga (TOP)',
            'flag' => 'twemoji_flag-tonga.png',
            'slug' => 'tonga',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 204,
            'country_name' => 'Tuvalu',
            'capital' => 'Funafuti',
            'currency' => 'Dollar Tuvaluan (TVD)',
            'flag' => 'twemoji_flag-tuvalu.png',
            'slug' => 'tuvalu',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 205,
            'country_name' => 'Vanuatu',
            'capital' => 'Port Vila',
            'currency' => 'Vanuatu Vatu (VUV)',
            'flag' => 'twemoji_flag-vanuatu.png',
            'slug' => 'vanuatu',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 206,
            'country_name' => 'Hong Kong',
            'capital' => 'Victoria',
            'currency' => 'Hong Kong dollar (HKD)',
            'flag' => 'twemoji_flag-hong-kong.png',
            'slug' => 'hong-kong',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 207,
            'country_name' => 'Ireland',
            'capital' => 'Dublin',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-ireland.png',
            'slug' => 'ireland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 208,
            'country_name' => 'Montenegro',
            'capital' => 'Podgorica',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-montenegro.png',
            'slug' => 'montenegro',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 209,
            'country_name' => 'Serbia',
            'capital' => 'Belgrade',
            'currency' => 'Serbian Dinar (RSD)',
            'flag' => 'twemoji_flag-serbia.png',
            'slug' => 'serbia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 210,
            'country_name' => 'Puerto Rico',
            'capital' => 'San Juan',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-puerto-rico.png',
            'slug' => 'puerto-rico',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 211,
            'country_name' => 'British Virgin Islands',
            'capital' => 'Road Town',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-british-virgin-islands.png',
            'slug' => 'british-virgin-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 212,
            'country_name' => 'Bermuda',
            'capital' => 'Hamilton',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-bermuda.png',
            'slug' => 'bermuda',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 213,
            'country_name' => 'Gibraltar',
            'capital' => 'Gibraltar',
            'currency' => 'Gibraltar Pound (GIP)',
            'flag' => 'twemoji_flag-gibraltar.png',
            'slug' => 'gibraltar',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 214,
            'country_name' => 'Greenland',
            'capital' => 'Nuuk',
            'currency' => 'Danish Krone (DK)',
            'flag' => 'twemoji_flag-greenland.png',
            'slug' => 'greenland',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        Country::create([
            'id' => 215,
            'country_name' => 'Kosovo',
            'capital' => 'Pristina',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-kosovo.png',
            'slug' => 'kosovo',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 216,
            'country_name' => 'Cayman Islands',
            'capital' => 'George Town',
            'currency' => 'The United States Dollar (USD)',
            'flag' => 'twemoji_flag-cayman-islands.png',
            'slug' => 'cayman-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 217,
            'country_name' => 'Aland Islands',
            'capital' => 'Mariehamn',
            'currency' => 'the Euro (€)',
            'flag' => 'twemoji_flag-aland-islands.png',
            'slug' => 'aland-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 218,
            'country_name' => 'American Samoa',
            'capital' => 'Pago Pago',
            'currency' => 'United States dollar (USD)',
            'flag' => 'twemoji_flag-american-samoa.png',
            'slug' => 'american-samoa',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 219,
            'country_name' => 'Anguilla',
            'capital' => 'The Valley',
            'currency' => 'Eastern Caribbean dollar (XCD)',
            'flag' => 'twemoji_flag-anguilla.png',
            'slug' => 'anguilla',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 220,
            'country_name' => 'Aruba',
            'capital' => 'Oranjestad',
            'currency' => 'Aruban Florin (AWG)',
            'flag' => 'twemoji_flag-aruba.png',
            'slug' => 'aruba',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 221,
            'country_name' => 'Curacao',
            'capital' => 'Willemstad',
            'currency' => 'Antillean guilder (ANG)',
            'flag' => 'twemoji_flag-curacao.png',
            'slug' => 'curacao',
            'status' => 'active',
            'created_at' => '2023-03-03 12:11:44',
            'updated_at' => '2023-04-12 17:11:57'
        ]);


        Country::create([
            'id' => 222,
            'country_name' => 'Cook Islands',
            'capital' => 'Avarua',
            'currency' => 'New Zealand Dollar (NZD)',
            'flag' => 'twemoji_flag-cook-islands.png',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 223,
            'country_name' => 'Eswatini',
            'capital' => 'Mbabane',
            'currency' => 'Swazi Lilangeni (SZL)',
            'flag' => 'twemoji_flag-eswatini.png',
            'slug' => 'eswatini',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 224,
            'country_name' => 'Faroe Islands',
            'capital' => 'Torshavn',
            'currency' => 'Faroese Króna (DKK)',
            'flag' => 'twemoji_flag-faroe-islands.png',
            'slug' => 'faroe-islands',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 225,
            'country_name' => 'French Polynesia',
            'capital' => 'Papeete',
            'currency' => 'Franc (XPF)',
            'flag' => 'twemoji_flag-french-polynesia.png',
            'slug' => 'french-polynesia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 226,
            'country_name' => 'Guadeloupe',
            'capital' => 'Basse-Terre',
            'currency' => 'Euro (EUR)',
            'flag' => 'twemoji_flag-guadeloupe.png',
            'slug' => 'guadeloupe',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 227,
            'country_name' => 'Guam',
            'capital' => 'Hagatna',
            'currency' => 'United States Dollar (USD)',
            'flag' => 'twemoji_flag-guam.png',
            'slug' => 'guam',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 228,
            'country_name' => 'Montserrat',
            'capital' => 'Plymouth',
            'currency' => 'East Caribbean Dollar (XCD)',
            'flag' => 'twemoji_flag-montserrat.png',
            'slug' => 'montserrat',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 229,
            'country_name' => 'North Macedonia',
            'capital' => 'Skopje',
            'currency' => 'Macedonian Denar (MKD)',
            'flag' => 'twemoji_flag-north-macedonia.png',
            'slug' => 'north-macedonia',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 230,
            'country_name' => 'North Korea',
            'capital' => 'Pyongyang',
            'currency' => 'Korean Won (KPW)',
            'flag' => 'twemoji_flag-north-korea.png',
            'slug' => 'north-korea',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


        Country::create([
            'id' => 231,
            'country_name' => 'Taiwan',
            'capital' => 'Taipei',
            'currency' => 'New Taiwan dollar (TWD)',
            'flag' => 'twemoji_flag-taiwan.png',
            'slug' => 'taiwan',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);


    }
}
