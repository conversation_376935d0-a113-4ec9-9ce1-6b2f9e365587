<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Company;
use App\Models\Job;
use App\Models\Membership;
use Carbon\Carbon;

class EmployerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
       $employer = User::create([
            'name' => 'Jeni',
            'slug'=>'jeni',
            'email' => '<EMAIL>',
            'password' => bcrypt('12345678'),
            'role' => 'employer',
            'company_id' => '1',
            'is_approved' => '1',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

       $company =  Company::create([
            'user_id' => $employer->id,
            'company_name' => 'Binary data Pvt. Ltd.',
            'company_slug' => 'binary-data-pvt-ltd',
            'designation' => 'Human Resource Manager',
            'company_logo' => 'logo.jpg',
            'company_website' => 'https://www.lonewinat.co.uk',
            'company_location' => '2',
            'company_sector' =>  '17',
            'no_of_employees' => '51-100',
            'available_resume_count'=>'100',
            'company_description' => 'A company is a legal entity formed by individuals or a group of people to engage in business activities with the primary goal of generating profit. It serves as a platform for organizing resources, capital, and expertise to produce goods or provide services in a specific industry or market.',
            'status' => 'active',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()

         ]);


        // $membership =  Membership::create([
        //     'user_id' => $employer->id,
        //     'company_id' => $company->id,
        //     'plan_id' => 1,
        //     'expire_at' => Carbon::now()->addDay(),
        //     'purchase_at' => Carbon::now(),
        //     'status'=>'active',
        //     'created_at' => Carbon::now(),
        //     'updated_at' => Carbon::now()

        //  ]);

       $job = Job::create([
             'user_id' => $employer->id,
             'company_id' => $company->id,
             'sector_id' => 1,
             'job_title' => 'Laravel Developer',
             'job_slug' => 'azerbaijan/laravel-developer-1',
             'job_type' => 'fulltime',
             'job_country' => '3',
             'industry' =>  '1',
             'experience' => '0-2',
             'skills_required' => '1,2,3,4',
             'monthly_fixed_salary_currency'=> '',
             'monthly_fixed_salary_min' => '20000',
             'monthly_fixed_salary_min' => '30000',
             'available_vacancies' => '5',
             'job_status' => 'active',
             'created_at' => Carbon::now(),
             'updated_at' => Carbon::now()
        ]);
       $job = Job::create([
             'user_id' => $employer->id,
             'company_id' => $company->id,
             'sector_id' => 1,
             'job_title' => 'Graphics Designer',
              'job_slug' => 'andorra/graphics-designer-2',
             'job_type' => 'parttime',
             'job_country' => '1',
             'industry' =>  '1',
             'experience' => '2-3',
             'skills_required' => '1,2,3,4',
             'monthly_fixed_salary_currency'=> '',
             'monthly_fixed_salary_min' => '30000',
             'monthly_fixed_salary_min' => '50000',
             'available_vacancies' => '10',
             'job_status' => 'active',
             'created_at' => Carbon::now(),
             'updated_at' => Carbon::now()
        ]);


    }
}
