<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Industry;
use Carbon\Carbon;

class IndustriesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Industry::create( [
            'name'=>'Tech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'BioMed Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Creative Minds',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Data Analytics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Global Finance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'HealthTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Clean Energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Digital Marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Virtual Reality',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'MediaTech',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Precision Engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Agile Consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'AI Robotics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Cloud Services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Cyber Security',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Smart Mobility',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'SpaceTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Renewable Energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'GreenTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Design Studio',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Legal Tech',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'BioTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'FinTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'AdTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'EdTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Social Media',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'HealthCare Tech',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'E-commerce Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'FoodTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Gaming Industry',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'HR Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'PharmaTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'IoT Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'AgriTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'CleanTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Software Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'InsurTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'FinanceTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'AI Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Telecom Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'FashionTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Cloud Computing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'TravelTech Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Bioinformatics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'MedTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Blockchain Solutions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Robotics Industry',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Virtual Events',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'NanoTech Innovations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Industry::create( [
            'name'=>'Smart Cities',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );



    }
}
