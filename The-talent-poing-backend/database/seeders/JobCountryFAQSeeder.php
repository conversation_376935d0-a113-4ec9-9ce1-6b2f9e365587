<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JobCountryFAQSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the country ID for Lebanon
        $lebanonId = Country::where('country_name', 'Lebanon')->value('id');

        // Inserting FAQs for Lebanon
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $lebanonId,
                'location_name' => 'Lebanon',
                'question' => 'How can I find jobs in Lebanon that match my expertise?',
                'answer' => 'Finding a job in Lebanon can be straightforward if you follow these steps:<br>Step 1: Visit The TalentPoint and create an account.<br>Step 2: Upload your CV—ensure it highlights your skills and experience.<br>Step 3: Use the filters to search for jobs in Lebanon that match your preferences and expertise.<br>Step 4: Apply directly with a click.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $lebanonId,
                'location_name' => 'Lebanon',
                'question' => 'What sectors in Lebanon hire the most expatriates?',
                'answer' => 'Lebanon has sectors that actively seek expatriates due to their specialized skills and experience. In 2023, remittances from Lebanese expatriates played a significant role in the economy, amounting to $6.4 billion. The most expatriate-friendly sectors include:<br>● Healthcare<br>● IT and Tech<br>● Education<br>● Hospitality',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $lebanonId,
                'location_name' => 'Lebanon',
                'question' => 'Are there many available jobs in Lebanon right now?',
                'answer' => 'Yes, available jobs in Lebanon are firm in education, IT, and healthcare. Remote work is also rising, with businesses adapting to global trends and offering digital roles.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $lebanonId,
                'location_name' => 'Lebanon',
                'question' => 'How competitive is the hiring process for jobs at Lebanon companies?',
                'answer' => 'The hiring process for jobs in Lebanon can be competitive—especially in high-demand sectors like IT and healthcare. To stand out, it\'s important to tailor your CV to the Lebanese job market. Highlight your achievements, skills, and experiences that are relevant to the job you\'re applying for.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $lebanonId,
                'location_name' => 'Lebanon',
                'question' => 'Are there part-time or remote jobs in Lebanon?',
                'answer' => 'Yes, part-time and remote jobs in Lebanon are becoming more popular. These include roles in IT support, online teaching, and freelance writing. Companies are adapting to meet the demand for flexible work options.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Jordan
        $jordanId = Country::where('country_name', 'Jordan')->value('id');

        // Inserting FAQs for Jordan
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $jordanId,
                'location_name' => 'Jordan',
                'question' => 'How can I apply for jobs in Jordan?',
                'answer' => 'Applying for jobs in Jordan involves these steps:<br>● Update your CV with relevant skills and achievements.<br>● Search for openings in key sectors like tourism, IT, and healthcare.<br>● Use trusted platforms like The TalentPoint to find tailored opportunities.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jordanId,
                'location_name' => 'Jordan',
                'question' => 'What industries are hiring the most in Jordan?',
                'answer' => '● Tourism and Hospitality: Roles in hotels, travel agencies, and event management.<br>● IT and Digital Services: Software development and data analytics.<br>● Healthcare: Jobs for doctors, nurses, and lab technicians.<br>● Education: Teaching opportunities in schools and universities.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jordanId,
                'location_name' => 'Jordan',
                'question' => 'Are jobs in Jordan available for expats?',
                'answer' => 'Yes, expats in Jordan can find jobs in sectors like IT, education, and international development. Organizations often look for skilled professionals to support economic growth.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jordanId,
                'location_name' => 'Jordan',
                'question' => 'What are the average salaries in Jordan?',
                'answer' => '● IT Professionals: JOD 1,000–2,500 per month.<br>● Healthcare Workers: JOD 800–1,500 per month.<br>● Tourism Roles: JOD 600–1,200 per month.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jordanId,
                'location_name' => 'Jordan',
                'question' => 'What documents are required to work in Jordan?',
                'answer' => '● A valid work visa sponsored by an employer.<br>● A passport with at least six months validity.<br>● Certifications specific to the role.<br>● A formal job offer letter.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Iraq
        $iraqId = Country::where('country_name', 'Iraq')->value('id');

        // Inserting FAQs for Iraq
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $iraqId,
                'location_name' => 'Iraq',
                'question' => 'How can I find jobs in Iraq?',
                'answer' => 'Finding jobs in Iraq is simple if you follow these steps:<br>Step 1: Update your CV with relevant details.<br>Step 2: Visit The TalentPoint and create an account.<br>Step 3: Use filters to search for jobs in Iraq by industry.<br>Step 4: Explore roles in oil and gas, construction, and education.<br>Step 5: Apply directly to suitable positions.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $iraqId,
                'location_name' => 'Iraq',
                'question' => 'What are the top industries hiring in Iraq?',
                'answer' => '● Oil and Gas: Roles in exploration, engineering, and project management.<br>● Construction: Skilled labor, project supervisors, and civil engineers.<br>● Healthcare: Doctors, nurses, and medical technicians.<br>● Education: Teaching roles in international and local schools.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $iraqId,
                'location_name' => 'Iraq',
                'question' => 'Are there job opportunities for expats in Iraq?',
                'answer' => 'Yes, expats are in demand in oil and gas, logistics, and engineering. Companies often provide relocation benefits for skilled professionals.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $iraqId,
                'location_name' => 'Iraq',
                'question' => 'Are remote jobs available in Iraq?',
                'answer' => 'Remote jobs in Iraq are limited but growing, particularly in IT, content writing, and consulting roles.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $iraqId,
                'location_name' => 'Iraq',
                'question' => 'What are the challenges of working in Iraq?',
                'answer' => 'Job seekers should consider safety, cultural adjustments, and limited availability of leisure amenities. Employers often provide support for these challenges.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Bahrain
        $bahrainId = Country::where('country_name', 'Bahrain')->value('id');

        // Inserting FAQs for Bahrain
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $bahrainId,
                'location_name' => 'Bahrain',
                'question' => 'How can I find jobs in Bahrain?',
                'answer' => 'Finding jobs in Bahrain is easy if you follow these steps:<br>● Step 1: Visit The TalentPoint and create an account.<br>● Step 2: Upload your CV or use the resume builder.<br>● Step 3: Use filters to search for jobs in Bahrain by industry and role.<br>● Step 4: Explore opportunities in sectors like banking, hospitality, and healthcare.<br>● Step 5: Apply directly to positions that match your skills.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $bahrainId,
                'location_name' => 'Bahrain',
                'question' => 'What are the most in-demand industries in Bahrain?',
                'answer' => 'The most in-demand industries in Bahrain are:<br>● Banking and Finance: Roles in investment banking, compliance, and accounting.<br>● Healthcare: Opportunities for doctors, nurses, and technicians.<br>● Hospitality and Tourism: Jobs in hotels, restaurants, and event management.<br>● IT: Cybersecurity, software development, and IT support.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $bahrainId,
                'location_name' => 'Bahrain',
                'question' => 'Are there jobs available in Manama for expats?',
                'answer' => 'Yes, Manama, Bahrain\'s capital, offers expats opportunities in finance, retail, and education. Many multinational companies in Manama actively seek international talent.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $bahrainId,
                'location_name' => 'Bahrain',
                'question' => 'What benefits can I expect while working in Bahrain?',
                'answer' => '● Tax-free salaries.<br>● Housing and transportation allowances.<br>● Comprehensive health insurance.<br>● Work-life balance with modern amenities.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $bahrainId,
                'location_name' => 'Bahrain',
                'question' => 'What documents do I need to apply for jobs in Bahrain?',
                'answer' => 'The essential documents you need to apply for a job in Bahrain include the following:<br>● A valid work visa sponsored by an employer.<br>● A passport with at least six months validity.<br>● Certifications or licenses specific to your job.<br>● A formal job offer letter.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Abu Dhabi
        $abuDhabiId = Country::where('country_name', 'Abu Dhabi')->value('id');

        // Inserting FAQs for Abu Dhabi
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'What industries are hiring in Abu Dhabi, and how can The TalentPoint help me find opportunities?',
                'answer' => 'Abu Dhabi is booming in sectors like oil and gas, finance, healthcare, and education. The TalentPoint platform lists top employers, connecting you with opportunities in these sectors and helping you find the right match.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'How much should I expect to earn in Abu Dhabi, and can The TalentPoint provide salary insights?',
                'answer' => 'Salaries vary by industry, with skilled professionals earning between AED 15,000–20,000 per month. The TalentPoint provides industry-specific salary insights so you can gauge your earning potential.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'What’s the best way to apply for jobs in Abu Dhabi through The TalentPoint?',
                'answer' => 'To apply for jobs in Abu Dhabi through The TalentPoint, follow these steps:<br>1. Create an account on The TalentPoint platform.<br>2. Build your CV using our resume builder to enhance your profile.<br>3. Apply filters to find job listings that match your skills and preferences.<br>4. Apply directly to positions that interest you.<br>5. Enable personalized job alerts to stay updated on new opportunities.<br>This method helps you connect efficiently with top employers in Abu Dhabi.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'What skills are in demand for jobs in Abu Dhabi?',
                'answer' => 'Project management, IT, healthcare, financial analysis, and bilingual abilities (English and Arabic) are highly valued. Developing these skills can boost your chances of landing a role.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'Is it expensive to live in Abu Dhabi?',
                'answer' => 'Living costs, particularly for housing, can be high. However, salaries in Abu Dhabi often reflect the cost of living, and options are available to fit a range of budgets.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'Do I need a work visa to work in Abu Dhabi?',
                'answer' => 'Yes, a work visa is required for expats, and your employer typically handles the visa process after you accept a job offer.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'What are the working hours in Abu Dhabi?',
                'answer' => 'Standard working hours are around 8 hours a day, 5 days a week, with Fridays and Saturdays as the weekend. Some companies may also offer shorter hours during Ramadan.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'Can I find part-time jobs in Abu Dhabi using The TalentPoint?',
                'answer' => 'Yes, while full-time roles are more common, The Talent Point lists part-time opportunities as well. Use our filters to explore flexible job options in Abu Dhabi.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'What’s the job market competition like in Abu Dhabi, and how can The TalentPoint help me stand out?',
                'answer' => 'Competition is high in sectors like finance and engineering. The TalentPoint offers a resume builder, personalized job alerts, and application guidance to help your profile stand out and improve your chances of success in Abu Dhabi.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $abuDhabiId,
                'location_name' => 'Abu Dhabi',
                'question' => 'Do companies in Abu Dhabi offer housing or transportation allowances?',
                'answer' => 'Many companies provide allowances, especially for senior positions. It’s best to check for benefits details directly in the job offer, as policies vary across employers.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Ajman
        $ajmanId = Country::where('country_name', 'Ajman')->value('id');

        // Inserting FAQs for Ajman
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $ajmanId,
                'location_name' => 'Ajman',
                'question' => 'Where can I find jobs in Ajman, UAE?',
                'answer' => 'Ajman offers many job opportunities. You can find roles in education, healthcare, logistics, and retail. At TalentPoint, we are committed to providing job listings that cater to different skill levels, from entry-level to experienced professionals. Use our filters to navigate this sea of opportunities and find the right role for you.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ajmanId,
                'location_name' => 'Ajman',
                'question' => 'Are there vacancies in Ajman schools or Ajman University?',
                'answer' => 'Yes, Ajman schools and Ajman University frequently have openings for teaching, administrative, and research positions. Schools often hire teachers for primary, secondary, and subject-specific roles, while the university looks for faculty, student services, and operations staff. Find these opportunities on TalentPoint or their official websites.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ajmanId,
                'location_name' => 'Ajman',
                'question' => 'Can I find part-time jobs in Ajman?',
                'answer' => 'Rest assured, part-time jobs are not just a possibility, but a thriving reality in Ajman. They are available in hospitality, retail, teaching, and freelance work. Many of these roles, such as tutoring or flexible retail shifts, are designed to cater to individuals seeking work-life balance. You can easily discover these positions through TalentPoint’s dedicated part-time job section.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ajmanId,
                'location_name' => 'Ajman',
                'question' => 'Are there jobs for females in Ajman, including urgent openings?',
                'answer' => 'Ajman is a place where everyone\'s skills and talents are valued, and this is reflected in the job market. Many jobs in Ajman cater to women, especially in education, healthcare, customer service, and office administration. Urgent openings for females are common in schools, clinics, and small businesses. You can find these jobs listed on TalentPoint, filtered for your specific needs.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ajmanId,
                'location_name' => 'Ajman',
                'question' => 'What jobs are available in the Ajman Free Zone?',
                'answer' => 'Ajman Free Zone is a growing business hub with job opportunities in logistics, IT, manufacturing, and business support. Companies in the Free Zone often look for specialists in export documentation, supply chain, and office administration. At TalentPoint, we provide the latest updates on Free Zone vacancies to keep you informed.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Al Ain
        $alAinId = Country::where('country_name', 'Al Ain')->value('id');

        // Inserting FAQs for Al Ain
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $alAinId,
                'location_name' => 'Al Ain',
                'question' => 'What job opportunities are available in Al Ain, UAE?',
                'answer' => 'Al Ain, a city that offers a diverse range of roles in healthcare, education, agriculture, and tourism. As a cultural hub, it\'s not just about the job, but the experience. It’s home to many opportunities in museums, heritage sites, and hospitality, making it an exciting place to work.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alAinId,
                'location_name' => 'Al Ain',
                'question' => 'How can I find vacancies in Al Ain quickly?',
                'answer' => 'It\'s easier than you think. Regularly check job portals and local classifieds. You can also explore opportunities listed on trusted platforms like The TalentPoint to stay updated. The process is straightforward and the opportunities are abundant.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alAinId,
                'location_name' => 'Al Ain',
                'question' => 'Are part-time jobs available in Al Ain?',
                'answer' => 'Yes, part-time roles are not just available, but also quite common in tutoring, retail, and hospitality in Al Ain. Many businesses also hire part-time staff for event management and freelance work, providing a flexible job market.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alAinId,
                'location_name' => 'Al Ain',
                'question' => 'What are the most in-demand jobs in Al Ain?',
                'answer' => 'Al Ain frequently hires professionals in healthcare, teaching, and tourism due to its growing industries. Other in-demand roles include administrative and customer service positions.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alAinId,
                'location_name' => 'Al Ain',
                'question' => 'How can I apply for urgent jobs in Al Ain?',
                'answer' => 'Prepare an updated CV and focus on industries with immediate hiring needs, such as hospitality or healthcare. Urgent job listings are often found on reliable portals like The TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Al Madinah
        $alMadinahId = Country::where('country_name', 'Al Madinah')->value('id');

        // Inserting FAQs for Al Madinah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $alMadinahId,
                'location_name' => 'Al Madinah',
                'question' => 'How can I find jobs in Al Madinah?',
                'answer' => 'Finding jobs in Al Madinah is easy if you follow these steps:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile with accurate details.<br>• Step 3: Upload your updated CV showcasing your skills and experience.<br>• Step 4: Use filters to search for jobs in Al Madinah by industry or expertise.<br>• Step 5: Apply directly to roles that match your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alMadinahId,
                'location_name' => 'Al Madinah',
                'question' => 'Are there part-time jobs available in Al Madinah?',
                'answer' => 'Yes, part-time jobs in Al Madinah are common in sectors like education, retail, and hospitality. These roles are ideal for students or individuals looking for flexible schedules.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alMadinahId,
                'location_name' => 'Al Madinah',
                'question' => 'Can females find jobs in Al Madinah?',
                'answer' => 'Yes, females can work in education, healthcare, and administrative positions. Many companies in Al Madinah provide work environments tailored to cultural considerations.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alMadinahId,
                'location_name' => 'Al Madinah',
                'question' => 'What sectors are hiring the most in Al Madinah?',
                'answer' => 'Sectors like healthcare, education, and tourism frequently hire, supporting the city’s growing population and religious visitors.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $alMadinahId,
                'location_name' => 'Al Madinah',
                'question' => 'What documents are required to work in Al Madinah?',
                'answer' => '• A valid work visa.<br>• A passport with at least six months of validity.<br>• Certifications or licenses specific to your job role.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Dammam
        $dammamId = Country::where('country_name', 'Dammam')->value('id');

        // Inserting FAQs for Dammam
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $dammamId,
                'location_name' => 'Dammam',
                'question' => 'How do I find jobs in Dammam?',
                'answer' => 'Finding jobs in Dammam is simple if you follow these steps:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile.<br>• Step 3: Upload your updated CV with accurate details.<br>• Step 4: Use filters to search for jobs in Dammam by industry, role, or expertise.<br>• Step 5: Apply directly to job openings that match your skills and interests.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dammamId,
                'location_name' => 'Dammam',
                'question' => 'Are there high-demand sectors for jobs in Dammam?',
                'answer' => 'Sectors like oil and gas, logistics, healthcare, and IT are in high demand. Other key industries include construction, retail, and hospitality.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dammamId,
                'location_name' => 'Dammam',
                'question' => 'What types of jobs are available in Dammam for job seekers?',
                'answer' => 'Dammam offers opportunities in the following:<br>• Oil and gas: Engineering and field operations.<br>• Logistics: Supply chain and warehousing roles.<br>• Healthcare: Doctors, nurses, and technicians.<br>• IT: Software development and network management.<br>• Retail: Sales and store management jobs.<br>• Construction: Project supervision and skilled trades.<br>• Hospitality: Hotel and restaurant positions.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dammamId,
                'location_name' => 'Dammam',
                'question' => 'Can females find jobs in Dammam?',
                'answer' => 'Yes, females can find jobs in education, healthcare, administration, retail, and hospitality sectors. Many companies also offer flexible or part-time roles tailored for women.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dammamId,
                'location_name' => 'Dammam',
                'question' => 'What should expatriates know about working in Dammam?',
                'answer' => 'Dammam is a hub for expatriates, especially in oil and gas, engineering, and healthcare. Companies often provide visa sponsorship, accommodation, and other benefits for international candidates.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Doha
        $dohaId = Country::where('country_name', 'Doha')->value('id');

        // Inserting FAQs for Doha
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $dohaId,
                'location_name' => 'Doha',
                'question' => 'How can I find jobs in Doha as an expat?',
                'answer' => 'To find jobs in Doha, create a detailed CV and search for opportunities in industries like oil and gas, education, and hospitality. Use trusted platforms like The TalentPoint to explore roles tailored to your expertise.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dohaId,
                'location_name' => 'Doha',
                'question' => 'What are the most in-demand industries for jobs in Doha?',
                'answer' => 'Doha offers diverse job opportunities in:<br>• Oil and Gas: Engineering, project management, and field operations.<br>• Education: Teaching positions in international schools and universities.<br>• Hospitality and Tourism: Hotel management, customer service, and event coordination.<br>• IT: Software development and network administration.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dohaId,
                'location_name' => 'Doha',
                'question' => 'Are online jobs available in Doha?',
                'answer' => 'Yes, online jobs in Doha are available in fields such as IT, graphic design, content writing, and digital marketing. These roles cater to professionals seeking remote or freelance work options.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dohaId,
                'location_name' => 'Doha',
                'question' => 'What is the salary range for jobs in Doha?',
                'answer' => 'The salary range varies based on the industry:<br>• Oil and Gas: QAR 15,000–25,000 per month.<br>• Education: QAR 8,000–12,000 per month.<br>• Hospitality: QAR 6,000–10,000 per month.<br>Most salaries include benefits such as housing, transport, and health insurance.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dohaId,
                'location_name' => 'Doha',
                'question' => 'What documents are required to apply for jobs in Doha?',
                'answer' => 'To work in Doha, you’ll need:<br>• A valid work visa and employer sponsorship.<br>• A passport with at least six months validity.<br>• Job-specific certifications or licenses.<br>• A formal job offer letter.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Dubai
        $dubaiId = Country::where('country_name', 'Dubai')->value('id');

        // Inserting FAQs for Dubai
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $dubaiId,
                'location_name' => 'Dubai',
                'question' => 'How can I find jobs in Dubai?',
                'answer' => 'Discover the power of The TalentPoint, your gateway to thousands of job opportunities across various industries and experience levels in Dubai.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dubaiId,
                'location_name' => 'Dubai',
                'question' => 'How do I get jobs in Dubai as a fresher or experienced professional?',
                'answer' => 'Get the support you need in your job search by creating an account on TalentPoint, uploading your CV, and applying for positions that align with your skills. Remember, networking can also be a valuable tool.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dubaiId,
                'location_name' => 'Dubai',
                'question' => 'Where can I find jobs in demand in Dubai?',
                'answer' => 'Check The TalentPoint for trending jobs in IT, healthcare, finance, tourism, and construction—industries currently booming in Dubai.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dubaiId,
                'location_name' => 'Dubai',
                'question' => 'Is it difficult to find a job in Dubai?',
                'answer' => 'Thanks to platforms like TalentPoint, the once daunting task of job hunting in Dubai is now simplified with filtered searches and tailored recommendations.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $dubaiId,
                'location_name' => 'Dubai',
                'question' => 'What’s the best way to apply for jobs in Dubai?',
                'answer' => 'Prepare a professional CV, sign up on The TalentPoint, and use advanced filters to find and apply for jobs that meet your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Sohar
        $soharId = Country::where('country_name', 'Sohar')->value('id');

        // Inserting FAQs for Sohar
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $soharId,
                'location_name' => 'Sohar',
                'question' => 'How can I find jobs in Sohar?',
                'answer' => 'Finding jobs in Sohar is straightforward if you follow these steps:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile with accurate details.<br>• Step 3: Upload your CV highlighting your skills and achievements.<br>• Step 4: Use filters to search for jobs in Sohar by industry or expertise.<br>• Step 5: Apply directly to roles that match your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $soharId,
                'location_name' => 'Sohar',
                'question' => 'What types of jobs are available for expats in Sohar?',
                'answer' => 'Expats in Sohar can explore opportunities in logistics, manufacturing, and education. The city’s industrial zones and port attract roles in supply chain management, engineering, and technical operations. Many employers hiring expats list their vacancies on The TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $soharId,
                'location_name' => 'Sohar',
                'question' => 'Are part-time jobs available in Sohar?',
                'answer' => 'Yes, part-time jobs in Sohar are common in tutoring, retail, and hospitality. Freelance opportunities in IT, design, and writing are also available.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $soharId,
                'location_name' => 'Sohar',
                'question' => 'What documents are required to apply for jobs in Sohar?',
                'answer' => 'To work in Sohar, you’ll need:<br>• A valid work visa and employer sponsorship.<br>• A passport with at least six months validity.<br>• Certifications specific to your job role.<br>• A job offer from an employer in Sohar.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $soharId,
                'location_name' => 'Sohar',
                'question' => 'What industries dominate the job market in Sohar?',
                'answer' => 'The Sohar job market is dominated by:<br>• Logistics: Warehousing, transportation, and supply chain roles.<br>• Manufacturing: Industrial production and factory management.<br>• Education: Teaching positions in schools and training institutions.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Fujairah
        $fujairahId = Country::where('country_name', 'Fujairah')->value('id');

        // Inserting FAQs for Fujairah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $fujairahId,
                'location_name' => 'Fujairah',
                'question' => 'Where can I find jobs in Fujairah, UAE?',
                'answer' => 'Fujairah is a diverse job market, offering roles in a variety of industries such as tourism, logistics, and education. At The TalentPoint, we provide a comprehensive range of job listings, ensuring that you can find a position that aligns with your expertise, whether you\'re a fresh graduate or an experienced professional.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $fujairahId,
                'location_name' => 'Fujairah',
                'question' => 'Are there vacancies in Fujairah Airport or Dibba Al Fujairah?',
                'answer' => 'Yes, Fujairah Airport often hires for customer service, operations, and ground handling roles, while Dibba Al Fujairah has opportunities in tourism, retail, and hospitality. You can explore these positions through The TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $fujairahId,
                'location_name' => 'Fujairah',
                'question' => 'Can I find part-time jobs in Fujairah, including urgent openings?',
                'answer' => 'Part-time and urgent job openings are readily available in Fujairah, particularly in sectors like retail, teaching, and freelance work. The TalentPoint’s listings are a valuable resource for finding these flexible job options across various industries.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $fujairahId,
                'location_name' => 'Fujairah',
                'question' => 'What government jobs are available in Fujairah?',
                'answer' => 'Fujairah offers government roles in public services, education, and healthcare. These jobs require applicants to meet specific qualifications and are often listed on official government portals and The TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $fujairahId,
                'location_name' => 'Fujairah',
                'question' => 'Are there female-specific jobs in Fujairah?',
                'answer' => 'For female job seekers, Fujairah offers a range of female-friendly roles in teaching, administration, and customer service. Many companies in Fujairah are actively hiring for positions tailored to women’s skills, and The TalentPoint\'s listings are regularly updated with these opportunities.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Jeddah
        $jeddahId = Country::where('country_name', 'Jeddah')->value('id');

        // Inserting FAQs for Jeddah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $jeddahId,
                'location_name' => 'Jeddah',
                'question' => 'How can I find jobs in Jeddah?',
                'answer' => 'Finding jobs in Jeddah is simple if you follow these steps:<br>Step 1: Visit The TalentPoint website.<br>Step 2: Create an account and complete your profile with accurate details.<br>Step 3: Upload your updated CV to showcase your skills and experience.<br>Step 4: Use filters to search for jobs in Jeddah by industry, role, or expertise.<br>Step 5: Apply directly to roles that match your skills and interests.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jeddahId,
                'location_name' => 'Jeddah',
                'question' => 'What documents are required to work in Jeddah?',
                'answer' => 'To work in Jeddah, you will need:<br>• A valid work visa.<br>• A passport with at least six months validity.<br>• A job offer letter from a Saudi employer.<br>• Specific certifications or licenses (if required by the role).',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jeddahId,
                'location_name' => 'Jeddah',
                'question' => 'Are there jobs for women in Jeddah?',
                'answer' => 'Yes, women can find job opportunities in various sectors, including:<br>• Healthcare: Nursing, lab technicians, and administrative roles.<br>• Education: Teaching and academic support positions.<br>• Administration: Office management and HR roles.<br>• Retail and Hospitality: Customer service and management jobs with flexible schedules.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jeddahId,
                'location_name' => 'Jeddah',
                'question' => 'Are there part-time jobs in Jeddah for expats or students?',
                'answer' => 'Part-time jobs in Jeddah are available in sectors like retail, tutoring, and freelancing. These roles are ideal for students or expats seeking flexible work schedules.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jeddahId,
                'location_name' => 'Jeddah',
                'question' => 'What types of jobs are available in Jeddah’s industrial cities?',
                'answer' => 'Jeddah’s 2nd and 3rd Industrial Cities offer roles in:<br>• Manufacturing: Machinery operation and quality control.<br>• Logistics: Warehouse management and supply chain coordination.<br>• Engineering: Maintenance, project supervision, and technical support.<br>• Chemical and Food Industries: Production and safety management roles.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Ahmadi
        $ahmadiId = Country::where('country_name', 'Ahmadi')->value('id');

        // Inserting FAQs for Ahmadi
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $ahmadiId,
                'location_name' => 'Ahmadi',
                'question' => 'How can I find jobs in Ahmadi?',
                'answer' => 'Finding jobs in Ahmadi is simple if you follow these steps:<br>• Step 1: Visit The TalentPoint and create an account.<br>• Step 2: Upload your updated CV or use the resume builder.<br>• Step 3: Use filters to search for jobs in Ahmadi by industry and experience.<br>• Step 4: Explore opportunities in key sectors like oil and gas, healthcare, and logistics.<br>• Step 5: Apply directly to positions that align with your skills and career goals.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ahmadiId,
                'location_name' => 'Ahmadi',
                'question' => 'Why is Ahmadi a top destination for job seekers in Kuwait?',
                'answer' => 'Ahmadi is Kuwait\'s oil and gas hub, making it a prime location for high-demand industries. It also offers expat-friendly policies, which attract professionals from all over the world.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ahmadiId,
                'location_name' => 'Ahmadi',
                'question' => 'Are there high-paying jobs available in Ahmadi?',
                'answer' => 'Yes, industries like oil and gas, construction, and healthcare offer well-paying roles. Many jobs include benefits like housing allowances, transportation perks, and healthcare coverage.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ahmadiId,
                'location_name' => 'Ahmadi',
                'question' => 'Can I find jobs in Ahmadi as a fresher?',
                'answer' => 'Yes, freshers can find entry-level roles in administration, healthcare support, and technical assistance. Internships and trainee programs in the oil and gas sector are also common.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ahmadiId,
                'location_name' => 'Ahmadi',
                'question' => 'What lifestyle benefits can I expect when working in Ahmadi?',
                'answer' => 'Ahmadi offers tax-free salaries, housing benefits, and high-quality healthcare. Its proximity to Kuwait City provides access to modern amenities and leisure options.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Eastern Province
        $easternProvinceId = Country::where('country_name', 'Eastern Province')->value('id');

        // Inserting FAQs for Eastern Province
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $easternProvinceId,
                'location_name' => 'Eastern Province',
                'question' => 'How can I search for jobs in the Eastern Province?',
                'answer' => 'To search for jobs:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and fill in your profile.<br>• Step 3: Upload your CV.<br>• Step 4: Use filters to find jobs in the Eastern Province.<br>• Step 5: Apply for suitable roles.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $easternProvinceId,
                'location_name' => 'Eastern Province',
                'question' => 'Are there jobs for expatriates in the Eastern Province?',
                'answer' => 'Yes, expatriates are hired for roles in oil and gas, logistics, and engineering.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $easternProvinceId,
                'location_name' => 'Eastern Province',
                'question' => 'Are there job opportunities for women in the Eastern Province?',
                'answer' => 'Yes, women can find jobs in healthcare, education, and retail.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $easternProvinceId,
                'location_name' => 'Eastern Province',
                'question' => 'What industries hire the most in the Eastern Province?',
                'answer' => 'Oil and gas, logistics, and education sectors have the highest demand for skilled workers.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $easternProvinceId,
                'location_name' => 'Eastern Province',
                'question' => 'What documents are needed to work in the Eastern Province?',
                'answer' => 'A valid work visa, a passport, and industry-specific certifications are required.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Jubail
        $jubailId = Country::where('country_name', 'Jubail')->value('id');

        // Inserting FAQs for Jubail
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $jubailId,
                'location_name' => 'Jubail',
                'question' => 'How can I find jobs in Jubail?',
                'answer' => 'Finding jobs in Jubail is straightforward:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Sign up and complete your profile.<br>• Step 3: Upload your CV with relevant details.<br>• Step 4: Search for jobs in Jubail using filters by industry or expertise.<br>• Step 5: Apply directly to roles.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jubailId,
                'location_name' => 'Jubail',
                'question' => 'Are there urgent job openings in Jubail?',
                'answer' => 'Yes, urgent jobs in Jubail are often available in manufacturing, engineering, and logistics. Employers frequently post immediate openings to meet project deadlines.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jubailId,
                'location_name' => 'Jubail',
                'question' => 'Can females find job opportunities in Jubail?',
                'answer' => 'Yes, females can find jobs in healthcare, education, and office administration. Many employers in Jubail offer roles tailored for women.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jubailId,
                'location_name' => 'Jubail',
                'question' => 'What industries hire the most in Jubail?',
                'answer' => 'The petrochemical, construction, and logistics sectors dominate Jubail’s job market, providing roles for both locals and expatriates.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $jubailId,
                'location_name' => 'Jubail',
                'question' => 'What documents do I need to work in Jubail?',
                'answer' => 'Documents necessary to work in Jubail:<br>• A valid work visa.<br>• A job offer from a local employer.<br>• Certifications or licenses required for the specific role.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Kuwait
        $kuwaitId = Country::where('country_name', 'Kuwait')->value('id');

        // Inserting FAQs for Kuwait
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $kuwaitId,
                'location_name' => 'Kuwait',
                'question' => 'How can I find jobs in Kuwait as a foreigner?',
                'answer' => 'Step 1: Visit The TalentPoint and create an account.<br>Step 2: Upload your CV or use the resume builder.<br>Step 3: Use filters to search for jobs in Kuwait by industry and expertise.<br>Step 4: Explore opportunities in sectors like oil and gas, healthcare, and education.<br>Step 5: Apply directly with just a few clicks.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $kuwaitId,
                'location_name' => 'Kuwait',
                'question' => 'What is the average salary in Kuwait?',
                'answer' => 'The average salary in Kuwait varies by industry:<br>• Oil and Gas: KWD 1,500–2,500 per month.<br>• Healthcare: KWD 800–1,500 per month.<br>• Education: KWD 600–1,000 per month for teachers.<br>Salaries often include benefits like housing, transport, and healthcare.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $kuwaitId,
                'location_name' => 'Kuwait',
                'question' => 'What types of jobs are in high demand in Kuwait?',
                'answer' => 'Kuwait has a strong demand for professionals in:<br>• Construction: Project managers and skilled labor.<br>• IT: Software developers and network engineers.<br>• Education: International school teachers.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $kuwaitId,
                'location_name' => 'Kuwait',
                'question' => 'What are the key benefits of working in Kuwait as a foreigner?',
                'answer' => 'Working in Kuwait offers a range of attractive benefits for expatriates:<br>• Tax-Free Salaries: Higher take-home pay without deductions.<br>• Housing Allowances: Subsidized accommodation costs.<br>• Transportation Perks: Travel allowances or company vehicles.<br>• Healthcare Benefits: Comprehensive medical coverage.<br>• Annual Flight Tickets: Covered travel for vacations or family visits.<br>• Family Sponsorship: Relocate dependents with ease.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $kuwaitId,
                'location_name' => 'Kuwait',
                'question' => 'What documents are required to work in Kuwait as a foreigner?',
                'answer' => 'To work in Kuwait, you will need:<br>• A valid work visa sponsored by your employer.<br>• A passport with at least six months validity.<br>• Medical clearance and police verification from your home country.<br>• Job-specific certifications or licenses, if applicable.<br>• A formal job offer letter from a registered company in Kuwait.<br>Employers often assist with the visa and documentation process.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Makkah
        $makkahId = Country::where('country_name', 'Makkah')->value('id');

        // Inserting FAQs for Makkah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $makkahId,
                'location_name' => 'Makkah',
                'question' => 'How can I find jobs in Makkah, Saudi Arabia?',
                'answer' => 'Finding jobs in Makkah is easy if you follow these steps:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile with accurate details.<br>• Step 3: Upload your updated CV.<br>• Step 4: Use filters to search for jobs in Makkah by industry, role, or expertise.<br>• Step 5: Apply directly to job openings that match your skills.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $makkahId,
                'location_name' => 'Makkah',
                'question' => 'What types of jobs are available for expatriates in Makkah?',
                'answer' => 'Expatriates in Makkah can find roles in:<br>• Healthcare: Doctors, nurses, and technicians are always in demand.<br>• Construction: Opportunities in project management and engineering.<br>• Hospitality: Jobs in hotels and restaurants catering to pilgrims and tourists.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $makkahId,
                'location_name' => 'Makkah',
                'question' => 'Are there jobs for females in Makkah?',
                'answer' => 'Yes, females can find job opportunities in sectors like:<br>• Education: Teaching and academic roles.<br>• Healthcare: Nursing, lab technicians, and administrative positions.<br>• Retail and Hospitality: Sales and customer service jobs.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $makkahId,
                'location_name' => 'Makkah',
                'question' => 'What documents are required to apply for a job in Makkah?',
                'answer' => 'To apply for jobs in Makkah, you will need:<br>• A valid work visa.<br>• A passport with at least six months validity.<br>• A job offer letter from an employer in Makkah.<br>• Certifications or licenses if required for the specific role.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $makkahId,
                'location_name' => 'Makkah',
                'question' => 'What industries are hiring for jobs in Makkah?',
                'answer' => 'Makkah has strong hiring demand in:<br>• Tourism and Hospitality: Hotels, restaurants, and travel services.<br>• Healthcare: Clinics, hospitals, and medical centers.<br>• Retail: Shops and malls serving the local and pilgrim population.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Muscat
        $muscatId = Country::where('country_name', 'Muscat')->value('id');

        // Inserting FAQs for Muscat
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $muscatId,
                'location_name' => 'Muscat',
                'question' => 'How can I find finance jobs in Muscat?',
                'answer' => 'Finding jobs in Muscat is easy if you follow these steps:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile with accurate details.<br>• Step 3: Upload your updated CV.<br>• Step 4: Use filters to search for jobs in Muscat by industry, role, or expertise.<br>• Step 5: Apply directly to job openings that match your skills.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $muscatId,
                'location_name' => 'Muscat',
                'question' => 'Are freelance opportunities common in Muscat?',
                'answer' => '• Yes, freelancers are in demand for IT, design, and content creation projects.<br>• Freelance tutoring and consulting roles are also popular among professionals.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $muscatId,
                'location_name' => 'Muscat',
                'question' => 'What sectors hire the most female professionals in Muscat?',
                'answer' => '• Education: Teaching roles in schools and universities.<br>• Healthcare: Positions for nurses, lab technicians, and administrative staff.<br>• Retail: Customer service and store management jobs.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $muscatId,
                'location_name' => 'Muscat',
                'question' => 'What are the benefits of working in Muscat?',
                'answer' => '• Competitive salaries with tax-free income.<br>• Benefits like housing allowances, transportation, and health insurance.<br>• A multicultural work environment with a high standard of living.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $muscatId,
                'location_name' => 'Muscat',
                'question' => 'How can I find part-time or flexible jobs in Muscat?',
                'answer' => 'Search for roles in retail, hospitality, or tutoring. Platforms like The TalentPoint often list part-time and freelance options tailored to Muscat’s job market.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Oman
        $omanId = Country::where('country_name', 'Oman')->value('id');

        // Inserting FAQs for Oman
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $omanId,
                'location_name' => 'Oman',
                'question' => 'How can I apply for jobs in Oman as an expatriate?',
                'answer' => 'Finding jobs in Oman is straightforward:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile with updated details.<br>• Step 3: Upload your CV showcasing your skills and achievements.<br>• Step 4: Use filters to explore jobs in Oman by industry or expertise.<br>• Step 5: Apply directly to roles that match your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $omanId,
                'location_name' => 'Oman',
                'question' => 'What industries are best for expats working in Oman?',
                'answer' => 'Oman offers diverse opportunities for expats across its key industries:<br>• Oil and Gas: Engineering, project management, and technical operations.<br>• Healthcare: Doctors, nurses, and medical technicians.<br>• Education: Teaching roles in international schools and training centers.<br>• Tourism: Hospitality, tour management, and luxury services.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $omanId,
                'location_name' => 'Oman',
                'question' => 'What should I know about work culture in Oman?',
                'answer' => '• The workweek typically runs from Sunday to Thursday.<br>• Respect for local customs and traditions is highly valued.<br>• Collaborative and family-oriented work environments are common.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $omanId,
                'location_name' => 'Oman',
                'question' => 'Are there high-paying jobs for expats in Oman?',
                'answer' => '• Yes, high-paying roles are available in oil and gas engineering, senior management, and IT.<br>• Expatriates with specialized skills often earn competitive salaries with added benefits like housing and healthcare.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $omanId,
                'location_name' => 'Oman',
                'question' => 'What documents are needed to work in Oman?',
                'answer' => '• A valid work visa issued by an Omani employer.<br>• A passport with at least six months validity.<br>• Industry-specific certifications or licenses, such as teaching credentials or healthcare qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Qatar
        $qatarId = Country::where('country_name', 'Qatar')->value('id');

        // Inserting FAQs for Qatar
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $qatarId,
                'location_name' => 'Qatar',
                'question' => 'How can I apply for jobs in Qatar as an expat?',
                'answer' => 'To apply for jobs in Qatar, create a professional CV and search for openings in industries like construction, healthcare, and IT. Use trusted platforms like The TalentPoint to find roles tailored to your expertise.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $qatarId,
                'location_name' => 'Qatar',
                'question' => 'Are there online jobs in Qatar for expats?',
                'answer' => 'Yes, online jobs in Qatar are available for expats in fields like IT, content writing, digital marketing, and remote customer service. These roles offer flexibility and are becoming increasingly popular.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $qatarId,
                'location_name' => 'Qatar',
                'question' => 'What is the salary range for jobs in Qatar?',
                'answer' => 'The salary range depends on the industry:<br>• Construction: QAR 8,000–12,000 per month.<br>• Healthcare: QAR 10,000–18,000 per month.<br>• IT: QAR 12,000–20,000 per month.<br>Most roles include benefits like housing and health insurance.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $qatarId,
                'location_name' => 'Qatar',
                'question' => 'What are the top benefits of working in Qatar as a foreigner?',
                'answer' => '• Tax-free salaries, allowing for greater savings.<br>• Housing and transportation allowances.<br>• Comprehensive health insurance.<br>• Access to world-class facilities and a high standard of living.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $qatarId,
                'location_name' => 'Qatar',
                'question' => 'What industries hire the most foreigners in Qatar?',
                'answer' => 'Key industries hiring foreigners include construction, IT, education, and tourism. With Qatar’s continued development and global events, these sectors offer consistent opportunities for expatriates.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Ras Al Khaimah
        $rasAlKhaimahId = Country::where('country_name', 'Ras Al Khaimah')->value('id');

        // Inserting FAQs for Ras Al Khaimah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $rasAlKhaimahId,
                'location_name' => 'Ras Al Khaimah',
                'question' => 'How can I find jobs in Ras Al Khaimah, UAE?',
                'answer' => 'Ras Al Khaimah is a diverse job market, offering opportunities in tourism, manufacturing, and education. At TalentPoint, we provide a comprehensive range of job listings to cater to your specific skills and interests.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $rasAlKhaimahId,
                'location_name' => 'Ras Al Khaimah',
                'question' => 'Are there job vacancies at Ras Al Khaimah Airport?',
                'answer' => 'Yes, Ras Al Khaimah Airport frequently has openings in logistics, customer service, and operations. We list these roles to make your job search easier.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $rasAlKhaimahId,
                'location_name' => 'Ras Al Khaimah',
                'question' => 'Can I find part-time jobs in Ras Al Khaimah?',
                'answer' => 'Without a doubt! Ras Al Khaimah is a place where part-time job opportunities, such as teaching, freelancing, and retail, abound. At TalentPoint, we simplify the process of discovering these flexible job options, giving you the freedom to balance work and personal life.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $rasAlKhaimahId,
                'location_name' => 'Ras Al Khaimah',
                'question' => 'What jobs are available in Ras Al Khaimah Free Zone?',
                'answer' => 'The Free Zone is known for its roles in IT, logistics, and business management. We at The TalentPoint help you access these Free Zone opportunities effortlessly.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $rasAlKhaimahId,
                'location_name' => 'Ras Al Khaimah',
                'question' => 'How do I apply for government jobs in Ras Al Khaimah?',
                'answer' => 'Government jobs in administration, public services, and education are in high demand in Ras Al Khaimah. You can explore these roles through official portals or find curated listings with us at The TalentPoint, making the application process straightforward.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Riyadh
        $riyadhId = Country::where('country_name', 'Riyadh')->value('id');

        // Inserting FAQs for Riyadh
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $riyadhId,
                'location_name' => 'Riyadh',
                'question' => 'How can I find job openings in Riyadh?',
                'answer' => 'Finding job openings in Riyadh is simple if you follow these steps:<br>Step 1: Visit The TalentPoint.<br>Step 2: Create an account and complete your profile with accurate details.<br>Step 3: Upload your updated CV to showcase your skills and experience.<br>Step 4: Use filters to search for job openings in Riyadh by industry and expertise.<br>Step 5: Apply directly to roles that match your skills with just a click.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $riyadhId,
                'location_name' => 'Riyadh',
                'question' => 'What is the process to apply for jobs in Riyadh as an expat?',
                'answer' => 'To apply for jobs in Riyadh as an expat, ensure you have a valid work visa or sponsorship. Research industries like IT, healthcare, and education, which often hire expats. Update your CV to highlight your international experience and use platforms like The TalentPoint to explore available jobs.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $riyadhId,
                'location_name' => 'Riyadh',
                'question' => 'What is a good salary in Riyadh?',
                'answer' => 'IT and engineering jobs typically pay SAR 10,000 to SAR 20,000. Administrative roles range between SAR 7,000 and SAR 12,000.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $riyadhId,
                'location_name' => 'Riyadh',
                'question' => 'Are there jobs in Riyadh for females?',
                'answer' => 'Yes, females can find jobs in teaching, healthcare, and administration. Flexible and online roles are also becoming more common.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $riyadhId,
                'location_name' => 'Riyadh',
                'question' => 'What documents are required to work in Riyadh?',
                'answer' => 'To work in Riyadh, you will need a valid work visa and a passport with at least six months validity. A job offer letter from a Saudi employer is also required. Some industries may additionally need specific certifications or licenses.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Salalah
        $salalahId = Country::where('country_name', 'Salalah')->value('id');

        // Inserting FAQs for Salalah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $salalahId,
                'location_name' => 'Salalah',
                'question' => 'How can I find jobs in Salalah?',
                'answer' => 'Finding jobs in Salalah is simple if you follow these steps:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile.<br>• Step 3: Upload your CV with updated details.<br>• Step 4: Use filters to search for jobs in Salalah by industry or expertise.<br>• Step 5: Apply directly to roles that match your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $salalahId,
                'location_name' => 'Salalah',
                'question' => 'What types of jobs are available for expats in Salalah?',
                'answer' => 'Expats in Salalah can find opportunities in:<br>• Tourism: Hotel management, travel services, and event coordination.<br>• Logistics: Warehousing and supply chain management.<br>• Agriculture: Roles in farm operations and export logistics.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $salalahId,
                'location_name' => 'Salalah',
                'question' => 'Are there part-time job opportunities in Salalah?',
                'answer' => 'Yes, part-time roles are available in:<br>• Retail and hospitality sectors.<br>• Freelance roles like content writing and graphic design.<br>• Tutoring and academic coaching for students.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $salalahId,
                'location_name' => 'Salalah',
                'question' => 'What documents are needed to apply for jobs in Salalah?',
                'answer' => 'Expats applying for jobs in Salalah need a valid work visa, an updated passport, and job-specific certifications or licenses. Employers often assist in obtaining these documents, especially for skilled professionals in tourism and logistics.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $salalahId,
                'location_name' => 'Salalah',
                'question' => 'What benefits do expats receive when working in Salalah?',
                'answer' => 'Expats often enjoy benefits like:<br>• Housing and transportation allowances.<br>• Access to healthcare and insurance.<br>• A relaxed and family-friendly lifestyle.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Saudi Arabia
        $saudiArabiaId = Country::where('country_name', 'Saudi Arabia')->value('id');

        // Inserting FAQs for Saudi Arabia
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $saudiArabiaId,
                'location_name' => 'Saudi Arabia',
                'question' => 'What types of jobs are available in Saudi Arabia?',
                'answer' => 'Saudi Arabia offers a range of roles in industries like healthcare, education, and construction. Jobs in Saudi Arabia are also expanding in renewable energy, tourism, and entertainment under Vision 2030.<br><br>It\'s important to note that Saudi Arabia has its own unique cultural and social norms, which may influence the work environment and expectations.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $saudiArabiaId,
                'location_name' => 'Saudi Arabia',
                'question' => 'Are there jobs in Saudi Arabia for expats?',
                'answer' => 'Yes, expats are not just welcome, but in high demand for key roles in engineering, healthcare, IT, and education. Many companies provide visa sponsorships and competitive benefits for international workers. This makes Saudi Arabia an attractive destination for those seeking new career challenges.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $saudiArabiaId,
                'location_name' => 'Saudi Arabia',
                'question' => 'Which companies in Saudi Arabia are hiring?',
                'answer' => 'Companies, such as energy, manufacturing, and technology, frequently hire. Explore jobs in Saudi Arabian companies by browsing listings on platforms like The TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $saudiArabiaId,
                'location_name' => 'Saudi Arabia',
                'question' => 'Can I find online jobs in Saudi Arabia?',
                'answer' => 'Absolutely! Online jobs in Saudi Arabia are available in fields like IT, digital marketing, teaching, and consulting. These roles are ideal for those seeking flexibility.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $saudiArabiaId,
                'location_name' => 'Saudi Arabia',
                'question' => 'How can I find jobs in Saudi Arabia easily?',
                'answer' => 'With an updated CV and the right platform, finding a job in Saudi Arabia is easier than you might think. Platforms like The TalentPoint offer a wealth of opportunities tailored to your skills. By focusing on roles that align with your expertise, you can increase your chances of success and find the perfect job for you.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Sharjah
        $sharjahId = Country::where('country_name', 'Sharjah')->value('id');

        // Inserting FAQs for Sharjah
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $sharjahId,
                'location_name' => 'Sharjah',
                'question' => 'Where can I find jobs in Sharjah with current vacancies?',
                'answer' => 'Sharjah is a diverse job market, offering opportunities in a range of sectors including education, healthcare, logistics, and retail. You can explore these roles on The TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $sharjahId,
                'location_name' => 'Sharjah',
                'question' => 'How can I apply for jobs in Sharjah Airport or Municipality?',
                'answer' => 'Sharjah Airport frequently hires for roles in operations and customer service, while the Municipality offers positions in administration and urban planning. Check their official websites or find these jobs on TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $sharjahId,
                'location_name' => 'Sharjah',
                'question' => 'Are there part-time jobs in Sharjah, including 4-hour shifts?',
                'answer' => 'Yes, Sharjah\'s job market is flexible, with part-time options available in retail, freelancing, and hospitality. You can find these jobs on TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $sharjahId,
                'location_name' => 'Sharjah',
                'question' => 'What are the best jobs in Sharjah for females, including urgent roles?',
                'answer' => 'For females seeking immediate job opportunities, popular roles in teaching, nursing, administrative positions, and customer service are available. Visit The TalentPoint for urgent openings.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $sharjahId,
                'location_name' => 'Sharjah',
                'question' => 'How can freshers find jobs in Sharjah?',
                'answer' => 'Freshers can start with internships or entry-level roles in customer service, sales, or education. Many of these are listed on TalentPoint.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for UAE
        $uaeId = Country::where('country_name', 'UAE')->value('id');

        // Inserting FAQs for UAE
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $uaeId,
                'location_name' => 'UAE',
                'question' => 'How can I find jobs in the UAE easily?',
                'answer' => 'Visit trusted platforms like TalentPoint, the #1 job portal in the UAE with over 1 million job listings. Filter jobs by industry and apply instantly.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $uaeId,
                'location_name' => 'UAE',
                'question' => 'What’s the process to apply for government jobs in the UAE?',
                'answer' => 'Visit the UAE Careers portal, create an account, upload your documents, and apply for roles matching your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $uaeId,
                'location_name' => 'UAE',
                'question' => 'How can freshers secure a job in the UAE?',
                'answer' => 'Freshers can explore 50,000+ entry-level positions available on The TalentPoint, highlighting their skills and internships.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $uaeId,
                'location_name' => 'UAE',
                'question' => 'Can I get a job in the UAE without experience?',
                'answer' => 'Yes, many industries offer over 20,000 skills-based roles listed on The TalentPoint, perfect for candidates starting out.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $uaeId,
                'location_name' => 'UAE',
                'question' => 'How do I use The TalentPoint to find a job in UAE?',
                'answer' => 'Step 1: Visit thetalentpoint.com.<br>Step 2: Sign up with your email and complete your profile details.<br>Step 3: Upload a professional and updated CV.<br>Step 4: Browse through over 1 million active job listings across various industries, locations, and experience levels.<br>Step 5: Use the filters to refine your search and apply for jobs that match your preferences.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Umm Al Qaiwain
        $ummAlQaiwainId = Country::where('country_name', 'Umm Al Qaiwain')->value('id');

        // Inserting FAQs for Umm Al Qaiwain
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $ummAlQaiwainId,
                'location_name' => 'Umm Al Qaiwain',
                'question' => 'What industries offer jobs in Umm Al Quwain?',
                'answer' => 'Umm Al Quwain is a land of diverse opportunities, with job openings in burgeoning sectors like healthcare, education, retail, and hospitality. It\'s not just these, even specialized industries such as fisheries and small-scale manufacturing are significant employers in the region, offering a wide array of career paths.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ummAlQaiwainId,
                'location_name' => 'Umm Al Qaiwain',
                'question' => 'Are there job vacancies in Umm Al Quwain specifically for females?',
                'answer' => 'Yes, many female-friendly roles are available in education, healthcare, and administrative fields. For instance, in education, there are opportunities for teaching assistants and administrative staff. In healthcare, roles such as nurses and administrative assistants are in demand. And in the administrative field, positions like receptionists and office assistants are frequently available. Schools, clinics, and local businesses often hire for these positions suited to women’s skill sets.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ummAlQaiwainId,
                'location_name' => 'Umm Al Qaiwain',
                'question' => 'How can I find part-time jobs in Umm Al Quwain?',
                'answer' => 'Part-time jobs are available in various sectors in Umm Al Quwain. In education, there are opportunities for tutoring or teaching assistant roles. Retail offers part-time positions in sales or customer service. Customer service roles are also available in sectors like hospitality and logistics. Freelancing opportunities are common, especially for skilled professionals. Flexible hours make these jobs a popular choice for students and families.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ummAlQaiwainId,
                'location_name' => 'Umm Al Qaiwain',
                'question' => 'Are urgent job vacancies common in Umm Al Quwain?',
                'answer' => 'Urgent hiring happens in roles like customer service, logistics, and hospitality, especially during peak seasons or for quick project deadlines.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $ummAlQaiwainId,
                'location_name' => 'Umm Al Qaiwain',
                'question' => 'How do I apply for job vacancies in Umm Al Quwain?',
                'answer' => 'Prepare an updated CV and research roles in your preferred industry. For ease, you can use The TalentPoint, a job portal specifically for Umm Al Quwain, to find listings that match your qualifications. Simply create an account, fill in your profile, and start browsing for jobs. When you find a suitable listing, you can apply directly through the portal.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Yanbu
        $yanbuId = Country::where('country_name', 'Yanbu')->value('id');

        // Inserting FAQs for Yanbu
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $yanbuId,
                'location_name' => 'Yanbu',
                'question' => 'How can I find jobs in Yanbu?',
                'answer' => 'Follow these steps to find jobs in Yanbu:<br>• Step 1: Visit The TalentPoint website.<br>• Step 2: Create an account and complete your profile.<br>• Step 3: Upload your CV with updated details.<br>• Step 4: Use filters to explore jobs in Yanbu based on your expertise.<br>• Step 5: Apply to roles that align with your qualifications.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $yanbuId,
                'location_name' => 'Yanbu',
                'question' => 'What types of jobs are available for expatriates in Yanbu?',
                'answer' => 'Expatriates often work in industrial sectors, including oil, gas, and logistics, with opportunities in engineering and project management.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $yanbuId,
                'location_name' => 'Yanbu',
                'question' => 'Are part-time jobs available in Yanbu?',
                'answer' => 'Yes, part-time jobs are available in freelance roles, tutoring, and customer service.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $yanbuId,
                'location_name' => 'Yanbu',
                'question' => 'What industries are hiring the most in Yanbu?',
                'answer' => 'Industries like petrochemicals, manufacturing, and logistics are prominent employers in Yanbu.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $yanbuId,
                'location_name' => 'Yanbu',
                'question' => 'What are the key requirements to work in Yanbu?',
                'answer' => '• A work visa and sponsorship from a local employer.<br>• A valid passport with at least six months of validity.<br>• Certifications or licenses for specific job roles.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Fetch the country ID for Zufar
        $zufarId = Country::where('country_name', 'Zufar')->value('id');

        // Inserting FAQs for Zufar
        DB::table('job_country_faqs')->insert([
            [
                'location_id' => $zufarId,
                'location_name' => 'Zufar',
                'question' => 'What are the best ways to search for jobs in Zufar?',
                'answer' => 'To find jobs in Zufar, update your CV and use trusted platforms like The TalentPoint. Search by industry, role, and experience to discover roles in agriculture, tourism, and logistics.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $zufarId,
                'location_name' => 'Zufar',
                'question' => 'What jobs are available in Zufar’s agriculture sector?',
                'answer' => 'Zufar offers roles in farm operations, quality control, and export logistics. The region’s agricultural focus creates consistent demand for skilled professionals.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $zufarId,
                'location_name' => 'Zufar',
                'question' => 'What industries hire women in Zufar?',
                'answer' => 'Women can find opportunities in education, healthcare, and hospitality. Tourism also offers roles like guest services, tour guiding, and event management.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'location_id' => $zufarId,
                'location_name' => 'Zufar',
                'question' => 'What are the most common logistics jobs in Zufar?',
                'answer' => 'The logistics sector hires for roles in supply chain management, warehousing, and transportation. Salalah Port drives demand for skilled professionals in these areas.',
                'more_about_location' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
