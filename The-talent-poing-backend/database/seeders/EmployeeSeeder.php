<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;
use Carbon\Carbon;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = [
            [
                'name' => '<PERSON> Doe',
                'email' => '<EMAIL>',
                'isShowEmail' => '1',
                'password' => bcrypt('12345678'),
                'role' => 'employee',
                'company_id' => null,
                'available_resume_count' => 5,
                'profile_image' => '',
                'where_job_search' => '2',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
                'where_currently_based' => '2',
                'current_position' => 'Software Developer',
                'profile_complete_percentage' => 40,
                'unlock_instant_apply' => 0,
                'linked_id' => null,
                'google_id' => null,
                'showcontact_no' => 1,
                'date_of_birth' => '1990-01-01',
                'gender' => 'Male',
                'years_of_experience' => "0-1",
                'current_salary' => 7000,
                'desired_salary' => 9000,
                'bio' => 'Experienced software developer with expertise in web development.',
                'facebook_link' => 'https://www.facebook.com/johndoe',
                'twitter_link' => 'https://www.twitter.com/johndoe',
                'instagram_link' => 'https://www.instagram.com/johndoe',
                'website_url' => 'https://www.johndoe.com',
                'linkedin_link' => 'https://www.linkedin.com/in/johndoe',
                'email_verified_at' => Carbon::now(),
                'contact_no' => '1234567890',
                'login_count' => 5,
                'first_login' => 1,
                'slug' => 'john-doe',
                'is_approved' => 1,
                'status' => 'active',
                'remember_token' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
                        [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'isShowEmail' => true,
                'password' => bcrypt('12345678'),
                'role' => 'employee',
                'company_id' => null,
                'available_resume_count' => 5,
                'profile_image' => '',
                'where_job_search' => '2',
                'job_type' => 'parttime',
                'job_status' => 'ready_to_interview',
                'where_currently_based' => '2',
                'current_position' => 'Software Developer',
                'profile_complete_percentage' => 40,
                'unlock_instant_apply' => false,
                'linked_id' => null,
                'google_id' => null,
                'showcontact_no' => true,
                'date_of_birth' => '1990-02-15',
                'gender' => 'Female',
                'years_of_experience' => 7,
                'current_salary' => 8000,
                'desired_salary' => 10000,
                'bio' => 'Experienced software developer with a focus on front-end development.',
                'facebook_link' => 'https://www.facebook.com/janesmith',
                'twitter_link' => 'https://www.twitter.com/janesmith',
                'instagram_link' => 'https://www.instagram.com/janesmith',
                'website_url' => 'https://www.janesmith.com',
                'linkedin_link' => 'https://www.linkedin.com/in/janesmith',
                'email_verified_at' => Carbon::now(),
                'contact_no' => '9876543210',
                'login_count' => 8,
                'first_login' => true,
                'slug' => 'jane-smith',
                'is_approved' => true,
                'status' => 'active',
                'remember_token' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            // Add more user records here
        ];

        foreach ($users as $user) {
            DB::table('users')->insert($user);
        }
    
    }
}
