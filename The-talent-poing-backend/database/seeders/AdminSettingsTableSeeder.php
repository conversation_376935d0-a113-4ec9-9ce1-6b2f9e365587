<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AdminSettings;
use Carbon\Carbon;

class AdminSettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        AdminSettings::create( [
            
            'user_id'=>'1',
            'logo'=>'logo.png',
            'favicon'=>'favicon.png',
            'payment_gateway'=>'stripe',
            'payment_mode'=>'testing',
            'stripe_test_secret_key'=>'sk_test_8yTMfGjWta7zVzyhB6S3N2ws',
            'stripe_test_publish_key'=>'pk_test_FQu4ActGupRmMrkmBpwU26js',
            'stripe_live_secret_key'=>null,
            'stripe_live_publish_key'=>null,
            'homepage_meta_title'=>null,
            'homepage_meta_description'=>null,
            'jobs_meta_title'=>null,
            'jobs_meta_description'=>null,
            'carrer_meta_title'=>null,
            'carrer_meta_description'=>null,
            'about_meta_title'=>null,
            'about_meta_description'=>null,
            'employer_meta_title'=>null,
            'employer_meta_description'=>null,
            'employee_meta_title'=>null,
            'employee_meta_description'=>null,
            'pricing_meta_title'=>null,
            'pricing_meta_description'=>null,
            'blog_listing_meta_title'=>null,
            'blog_listing_meta_description'=>null,
            'linkedin_link'=>null,
            'twitter_link'=>null,
            'instagram_link'=>null,
            'facebook_link'=>null,
            'website_url'=>'https://env-dev.thetalentpoint.com/',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
    }
}