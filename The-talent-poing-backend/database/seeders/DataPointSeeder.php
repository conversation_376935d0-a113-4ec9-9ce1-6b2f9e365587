<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DataPointSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Sample data to be inserted into the data_points table
        $dataPoints = [

            //---- Employee specific data-points ----//

            [
                'data_point_name' => 'name',
                'relation_name' => null,
                'column_name' => 'name', // Correct column in User table
                'user_type' => '1,2', // 1=employee, 2=employer
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'profile-image',
                'relation_name' => 'profile',
                'column_name' => 'profile_image', // Correct foreign key in User table
                'user_type' => '1,2', // 1=employee, 2=employer
                'data_type' => 'Document',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'contact-number',
                'relation_name' => null,
                'column_name' => 'contact_no', // Correct column in User table
                'user_type' => '1,2', // 1=employee, 2=employer
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'resume',
                'relation_name' => 'resumes',
                'column_name' => 'resume_pdf_path', // Assumes `name` exists in Resumes table
                'user_type' => '1', // 1=employee
                'data_type' => 'Document',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'location',
                'relation_name' => 'country',
                'column_name' => 'country_name', // Assuming Country model has a `name` column
                'user_type' => '1,2', // 1=employee, 2=employer
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'gender',
                'relation_name' => null,
                'column_name' => 'gender', // Correct column in User table
                'user_type' => '1', // 1=employee
                'data_type' => 'Boolean',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'social-linkedin',
                'relation_name' => null,
                'column_name' => 'linkedin_link', // Correct column for LinkedIn link
                'user_type' => '1,2', // 1=employee, 2=employer
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'skills',
                'relation_name' => 'skills',
                'column_name' => 'skill_id', // Assuming `skills` exists in User table
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'profile-completion-percentage',
                'relation_name' => null,
                'column_name' => 'profile_complete_percentage',
                'user_type' => '1', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'experience-level',
                'relation_name' => null,
                'column_name' => 'years_of_experience',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'current-job-location',
                'relation_name' => null,
                'column_name' => 'countries',
                'user_type' => '1', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'current-job-role',
                'relation_name' => null,
                'column_name' => 'current_position',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'expected-salary-range',
                'relation_name' => null,
                'column_name' => 'desired_salary',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'data_point_name' => 'certifications',
            //     'relation_name' => null,
            //     'column_name' => 'desired_salary', 
            //     'user_type' => '1', // 1=employee
            //     'data_type' => 'Text',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            [
                'data_point_name' => 'languages-known',
                'relation_name' => 'languages',
                'column_name' => 'language',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'last-login-date',
                'relation_name' => null,
                'column_name' => 'last_login',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'data_point_name' => 'number-of-jobs-applied',
            //     'relation_name' => 'application',
            //     'column_name' => 'updated_at',
            //     'user_type' => '1', // 1=employee
            //     'data_type' => 'Integer',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            [
                'data_point_name' => 'last-application-date',
                'relation_name' => 'application',
                'column_name' => 'updated_at',
                'user_type' => '1', // 1=employee
                'data_type' => 'Date',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'application-status',
                'relation_name' => 'application',
                'column_name' => 'apply_status',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'job-saved',
                'relation_name' => 'savedjobs',
                'column_name' => 'status',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'job-viewed',
                'relation_name' => 'jobsview',
                'column_name' => 'status',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'notification-read',
                'relation_name' => 'userTo',
                'column_name' => 'is_read',
                'user_type' => '1', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'activity-status',
                'relation_name' => null,
                'column_name' => 'status',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'registered-date',
                'relation_name' => null,
                'column_name' => 'created_at',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],

            //---- Employer specific data-points ----//
            [
                'data_point_name' => 'job-title',
                'relation_name' => 'jobpost',
                'column_name' => 'job_title',
                'user_type' => '2', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'job-location',
                'relation_name' => 'jobpost',
                'column_name' => 'job_country',
                'user_type' => '1', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'job-type',
                'relation_name' => 'jobpost',
                'column_name' => 'job_type',
                'user_type' => '2', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'salary-range-max',
                'relation_name' => 'jobpost',
                'column_name' => 'monthly_fixed_salary_max',
                'user_type' => '2', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'experience-level-required',
                'relation_name' => 'jobpost',
                'column_name' => 'experience',
                'user_type' => '2', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'job-deadline',
                'relation_name' => 'jobpost',
                'column_name' => 'deadline',
                'user_type' => '2', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'data_point_name' => 'number-of-application',
            //     'relation_name' => 'jobpost.',
            //     'column_name' => 'deadline', 
            //     'user_type' => '2', // 1=employee
            //     'data_type' => 'Text',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // [
            //     'data_point_name' => 'number-of-views',
            //     'relation_name' => 'jobpost',
            //     'column_name' => 'deadline', 
            //     'user_type' => '2', // 1=employee
            //     'data_type' => 'Text',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // [
            //     'data_point_name' => 'number-of-shortlisted-candidates',
            //     'relation_name' => 'jobpost',
            //     'column_name' => 'deadline', 
            //     'user_type' => '2', // 1=employee
            //     'data_type' => 'Text',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            [
                'data_point_name' => 'job-status',
                'relation_name' => 'jobpost',
                'column_name' => 'job_status',
                'user_type' => '2', // 1=employee
                'data_type' => 'Text',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],

            [
                'data_point_name' => 'account-type',
                'relation_name' => 'membership',
                'column_name' => 'plan_id',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'subscription-expiry',
                'relation_name' => 'membership',
                'column_name' => 'expire_at',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'industry-type',
                'relation_name' => null,
                'column_name' => 'industry',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'company-size',
                'relation_name' => 'ownCompany',
                'column_name' => 'no_of_employees',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'company-location',
                'relation_name' => 'ownCompany',
                'column_name' => 'company_location',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'jobs-posted',
                'relation_name' => 'jobpost',
                'column_name' => 'created_at',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'data_point_name' => 'jobs-expired',
                'relation_name' => 'jobpost',
                'column_name' => 'deadline',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'data_point_name' => 'number-of-candidate-shortlisted',
            //     'relation_name' => 'jobpost',
            //     'column_name' => 'company_location',
            //     'user_type' => '2', // 1=employee
            //     'data_type' => 'Integer',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // [
            //     'data_point_name' => 'number-of-candidate-hired',
            //     'relation_name' => 'jobpost',
            //     'column_name' => 'company_location',
            //     'user_type' => '2', // 1=employee
            //     'data_type' => 'Integer',
            //     'status' => '1', // 1=active
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            [
                'data_point_name' => 'last-login-date',
                'relation_name' => null,
                'column_name' => 'last_login',
                'user_type' => '2', // 1=employee
                'data_type' => 'Integer',
                'status' => '1', // 1=active
                'created_at' => now(),
                'updated_at' => now(),
            ],

        ];


        // Insert the sample data into the data_points table
        DB::table('data_points')->insert($dataPoints);
    }
}
