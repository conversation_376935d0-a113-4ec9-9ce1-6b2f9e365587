<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // User::create([
        //     'name' => 'Super Admin',
        //     'email' => '<EMAIL>',
        //     'password' => bcrypt('123456789'),
        //     'role' => 'superadmin'
        // ]);
        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('123456789'),
            'role' => 'admin',
            'profile_image' => ''
        ]);
    }
}
