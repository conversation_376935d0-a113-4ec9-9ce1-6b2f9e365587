FROM --platform=linux/amd64 php:8.2-fpm
USER root
RUN apt-get update && apt-get install -y \
        libpng-dev \
        libjpeg-dev \
        libjpeg62-turbo-dev \
        libfreetype6-dev \
        libmcrypt-dev \
        libgd-dev \
        jpegoptim optipng pngquant gifsicle \
        zlib1g-dev \
        libxml2-dev \
        libzip-dev \
        libonig-dev \
        libpq-dev \
        zip \
        curl \
        unzip \
    && docker-php-ext-configure gd \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-install pdo_mysql \
    && docker-php-ext-install mysqli \
    && docker-php-ext-install zip \
    && docker-php-ext-install exif \
    && docker-php-ext-install pdo \
    && docker-php-ext-install pgsql \
    && docker-php-ext-install pdo_mysql \
    && docker-php-source delete \

# Install Composer
#RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN docker-php-ext-configure intl \
   && docker-php-ext-configure gettext \
   && docker-php-ext-install \
   intl \
   gettext

EXPOSE 9000

WORKDIR /var/www/html

COPY . /var/www/html
COPY ./.env.example /var/www/html/.env

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install Cron job
#RUN apt-get install -y cron
#ADD schedule/crontab /etc/cron.d/cron
#RUN chmod 0644 /etc/cron.d/cron
#RUN touch /var/log/cron.log

RUN composer install
RUN php artisan key:generate
RUN php artisan storage:link
RUN chmod -R 777 ./storage/*

#CMD printenv > /etc/environment && echo "cron starting…" && (cron) && : > /var/log/cron.log && tail -f /var/log/cron.log
#CMD ["php-fpm", "printenv > /etc/environment && echo 'cron starting…' && (cron) && : > /var/log/cron.log && tail -f /var/log/cron.log"]
CMD ["php-fpm"]
