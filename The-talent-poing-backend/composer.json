{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "drewm/mailchimp-api": "^2.5", "f9webltd/laravel-api-response-helpers": "^1.5", "fruitcake/laravel-cors": "^2.0.5", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "laravel/framework": "^9.0", "laravel/sanctum": "^2.14", "laravel/telescope": "^4.15", "laravel/tinker": "^2.7", "laravel/ui": "^4.2", "maatwebsite/excel": "^3.1", "mailjet/mailjet-apiv3-php": "^1.6", "spatie/laravel-newsletter": "^5.1", "spatie/mailcoach-sdk-php": "^1.1", "stripe/stripe-php": "^10.16"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "knuckleswtf/scribe": "^4.29", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Src\\": "src/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}