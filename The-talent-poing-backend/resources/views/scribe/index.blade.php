<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>The Talent Point Documentation</title>

    <link href="https://fonts.googleapis.com/css?family=Open+Sans&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="{{ asset("/vendor/scribe/css/theme-default.style.css") }}" media="screen">
    <link rel="stylesheet" href="{{ asset("/vendor/scribe/css/theme-default.print.css") }}" media="print">

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js"></script>

    <link rel="stylesheet"
          href="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/styles/obsidian.min.css">
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/highlight.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jets/0.14.1/jets.min.js"></script>

    <style id="language-style">
        /* starts out as display none and is replaced with js later  */
                    body .content .bash-example code { display: none; }
                    body .content .javascript-example code { display: none; }
            </style>

    <script>
        var tryItOutBaseUrl = "https://api.thetalentpoint.com";
        var useCsrf = Boolean();
        var csrfUrl = "/sanctum/csrf-cookie";
    </script>
    <script src="{{ asset("/vendor/scribe/js/tryitout-4.29.0.js") }}"></script>

    <script src="{{ asset("/vendor/scribe/js/theme-default-4.29.0.js") }}"></script>

</head>

<body data-languages="[&quot;bash&quot;,&quot;javascript&quot;]">

<a href="#" id="nav-button">
    <span>
        MENU
        <img src="{{ asset("/vendor/scribe/images/navbar.png") }}" alt="navbar-image"/>
    </span>
</a>
<div class="tocify-wrapper">
    
            <div class="lang-selector">
                                            <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                            <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                    </div>
    
    <div class="search">
        <input type="text" class="search" id="input-search" placeholder="Search">
    </div>

    <div id="toc">
                    <ul id="tocify-header-introduction" class="tocify-header">
                <li class="tocify-item level-1" data-unique="introduction">
                    <a href="#introduction">Introduction</a>
                </li>
                            </ul>
                    <ul id="tocify-header-authenticating-requests" class="tocify-header">
                <li class="tocify-item level-1" data-unique="authenticating-requests">
                    <a href="#authenticating-requests">Authenticating requests</a>
                </li>
                            </ul>
                    <ul id="tocify-header-authentication" class="tocify-header">
                <li class="tocify-item level-1" data-unique="authentication">
                    <a href="#authentication">Authentication</a>
                </li>
                                    <ul id="tocify-subheader-authentication" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="authentication-GETapi-authentication-session">
                                <a href="#authentication-GETapi-authentication-session">Get session</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="authentication-GETapi-authentication-logout">
                                <a href="#authentication-GETapi-authentication-logout">Logout</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="authentication-POSTapi-authenticate">
                                <a href="#authentication-POSTapi-authenticate">POST api/authenticate</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-countries" class="tocify-header">
                <li class="tocify-item level-1" data-unique="countries">
                    <a href="#countries">Countries</a>
                </li>
                                    <ul id="tocify-subheader-countries" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="countries-GETapi-countries-countries">
                                <a href="#countries-GETapi-countries-countries">GET api/countries/countries</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-GETapi-countries-getcountries">
                                <a href="#countries-GETapi-countries-getcountries">GET api/countries/getcountries</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-POSTapi-countries-editandsavecountry">
                                <a href="#countries-POSTapi-countries-editandsavecountry">POST api/countries/editandsavecountry</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-DELETEapi-countries-country">
                                <a href="#countries-DELETEapi-countries-country">DELETE api/countries/country</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-GETapi-countries-getallcountriesforadmin">
                                <a href="#countries-GETapi-countries-getallcountriesforadmin">GET api/countries/getallcountriesforadmin</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-GETapi-countries-getcountriesselectedids">
                                <a href="#countries-GETapi-countries-getcountriesselectedids">GET api/countries/getcountriesselectedids</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-PUTapi-countries-countrystatus">
                                <a href="#countries-PUTapi-countries-countrystatus">PUT api/countries/countrystatus</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="countries-GETapi-countries-single-country-by-name">
                                <a href="#countries-GETapi-countries-single-country-by-name">GET api/countries/single-country-by-name</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-endpoints" class="tocify-header">
                <li class="tocify-item level-1" data-unique="endpoints">
                    <a href="#endpoints">Endpoints</a>
                </li>
                                    <ul id="tocify-subheader-endpoints" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="endpoints-GETapi-file-management-files">
                                <a href="#endpoints-GETapi-file-management-files">Display a list of files store by the authenticated user</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-POSTapi-file-management-files">
                                <a href="#endpoints-POSTapi-file-management-files">Save a new file</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-file-management-files--id-">
                                <a href="#endpoints-GETapi-file-management-files--id-">Display the specified file.</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-PUTapi-file-management-files--uuid-">
                                <a href="#endpoints-PUTapi-file-management-files--uuid-">Update the specified resource in storage.</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-DELETEapi-file-management-files--id-">
                                <a href="#endpoints-DELETEapi-file-management-files--id-">Remove the specified resource from storage.</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-industries" class="tocify-header">
                <li class="tocify-item level-1" data-unique="industries">
                    <a href="#industries">Industries</a>
                </li>
                                    <ul id="tocify-subheader-industries" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="industries-GETapi-industry-getallindustries">
                                <a href="#industries-GETapi-industry-getallindustries">GET api/industry/getallindustries</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="industries-GETapi-industry-getallindustriesforadmin">
                                <a href="#industries-GETapi-industry-getallindustriesforadmin">GET api/industry/getallindustriesforadmin</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="industries-POSTapi-industry-editandsaveindustries">
                                <a href="#industries-POSTapi-industry-editandsaveindustries">POST api/industry/editandsaveindustries</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="industries-DELETEapi-industry-industries">
                                <a href="#industries-DELETEapi-industry-industries">DELETE api/industry/industries</a>
                            </li>
                                                                        </ul>
                            </ul>
                    <ul id="tocify-header-salaries" class="tocify-header">
                <li class="tocify-item level-1" data-unique="salaries">
                    <a href="#salaries">Salaries</a>
                </li>
                                    <ul id="tocify-subheader-salaries" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="salaries-GETapi-salaries-insights">
                                <a href="#salaries-GETapi-salaries-insights">Get salary insights</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="salaries-GETapi-salaries-career-paths">
                                <a href="#salaries-GETapi-salaries-career-paths">Get salary similar career paths</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="salaries-GETapi-salaries-latest-openings">
                                <a href="#salaries-GETapi-salaries-latest-openings">Get latest job openings</a>
                            </li>
                                                                        </ul>
                            </ul>
            </div>

    <ul class="toc-footer" id="toc-footer">
                    <li style="padding-bottom: 5px;"><a href="{{ route("scribe.postman") }}">View Postman collection</a></li>
                            <li style="padding-bottom: 5px;"><a href="{{ route("scribe.openapi") }}">View OpenAPI spec</a></li>
                <li><a href="http://github.com/knuckleswtf/scribe">Documentation powered by Scribe ✍</a></li>
    </ul>

    <ul class="toc-footer" id="last-updated">
        <li>Last updated: January 22, 2024</li>
    </ul>
</div>

<div class="page-wrapper">
    <div class="dark-box"></div>
    <div class="content">
        <h1 id="introduction">Introduction</h1>
<p>Documentation for internal integration</p>
<aside>
    <strong>Base URL</strong>: <code>https://api.thetalentpoint.com/</code>
</aside>
<p>This documentation aims to provide all the information you need to work with our API.</p>
<aside>As you scroll, you'll see code examples for working with the API in different programming languages in the dark area to the right (or as part of the content on mobile).
You can switch the language used with the tabs at the top right (or from the nav menu at the top left on mobile).</aside>

        <h1 id="authenticating-requests">Authenticating requests</h1>
<p>To authenticate requests, include an <strong><code>Authorization</code></strong> header with the value <strong><code>"Bearer {YOUR_AUTH_KEY}"</code></strong>.</p>
<p>All authenticated endpoints are marked with a <code>requires authentication</code> badge in the documentation below.</p>
<p>You can retrieve your token by visiting your dashboard and clicking <b>Generate API token</b>.</p>

        <h1 id="authentication">Authentication</h1>

    

                                <h2 id="authentication-GETapi-authentication-session">Get session</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>

<p>Returns the current session</p>

<span id="example-requests-GETapi-authentication-session">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/authentication/session" \
    --header "Authorization: Bearer {YOUR_AUTH_KEY}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/authentication/session"
);

const headers = {
    "Authorization": "Bearer {YOUR_AUTH_KEY}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-authentication-session">
            <blockquote>
            <p>Example response (401):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Unauthenticated.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-authentication-session" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-authentication-session"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-authentication-session"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-authentication-session" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-authentication-session">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-authentication-session" data-method="GET"
      data-path="api/authentication/session"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-authentication-session', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-authentication-session"
                    onclick="tryItOut('GETapi-authentication-session');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-authentication-session"
                    onclick="cancelTryOut('GETapi-authentication-session');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-authentication-session"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/authentication/session</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-authentication-session"
               value="Bearer {YOUR_AUTH_KEY}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {YOUR_AUTH_KEY}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-authentication-session"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-authentication-session"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="authentication-GETapi-authentication-logout">Logout</h2>

<p>
<small class="badge badge-darkred">requires authentication</small>
</p>



<span id="example-requests-GETapi-authentication-logout">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/authentication/logout" \
    --header "Authorization: Bearer {YOUR_AUTH_KEY}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/authentication/logout"
);

const headers = {
    "Authorization": "Bearer {YOUR_AUTH_KEY}",
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-authentication-logout">
            <blockquote>
            <p>Example response (401):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Unauthenticated.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-authentication-logout" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-authentication-logout"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-authentication-logout"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-authentication-logout" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-authentication-logout">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-authentication-logout" data-method="GET"
      data-path="api/authentication/logout"
      data-authed="1"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-authentication-logout', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-authentication-logout"
                    onclick="tryItOut('GETapi-authentication-logout');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-authentication-logout"
                    onclick="cancelTryOut('GETapi-authentication-logout');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-authentication-logout"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/authentication/logout</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Authorization</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Authorization" class="auth-value"               data-endpoint="GETapi-authentication-logout"
               value="Bearer {YOUR_AUTH_KEY}"
               data-component="header">
    <br>
<p>Example: <code>Bearer {YOUR_AUTH_KEY}</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-authentication-logout"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-authentication-logout"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="authentication-POSTapi-authenticate">POST api/authenticate</h2>

<p>
</p>



<span id="example-requests-POSTapi-authenticate">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://api.thetalentpoint.com/api/authenticate" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"email\": \"<EMAIL>\",
    \"password\": \"occaecati\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/authenticate"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "email": "<EMAIL>",
    "password": "occaecati"
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-POSTapi-authenticate">
</span>
<span id="execution-results-POSTapi-authenticate" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-authenticate"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-authenticate"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-authenticate" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-authenticate">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-POSTapi-authenticate" data-method="POST"
      data-path="api/authenticate"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-authenticate', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-POSTapi-authenticate"
                    onclick="tryItOut('POSTapi-authenticate');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-POSTapi-authenticate"
                    onclick="cancelTryOut('POSTapi-authenticate');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-POSTapi-authenticate"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/authenticate</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="POSTapi-authenticate"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="POSTapi-authenticate"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>email</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="email"                data-endpoint="POSTapi-authenticate"
               value="<EMAIL>"
               data-component="body">
    <br>
<p>Must be a valid email address. Example: <code><EMAIL></code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>password</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="password"                data-endpoint="POSTapi-authenticate"
               value="occaecati"
               data-component="body">
    <br>
<p>Example: <code>occaecati</code></p>
        </div>
        </form>

                <h1 id="countries">Countries</h1>

    

                                <h2 id="countries-GETapi-countries-countries">GET api/countries/countries</h2>

<p>
</p>



<span id="example-requests-GETapi-countries-countries">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/countries/countries" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/countries"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-countries-countries">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1999
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">[
    {
        &quot;id&quot;: 4,
        &quot;country_name&quot;: &quot;Bahrain&quot;,
        &quot;slug&quot;: &quot;bahrain&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bahrain.png&quot;,
        &quot;currency&quot;: &quot;Bahraini Dinar (BHD)&quot;,
        &quot;capital&quot;: &quot;Manama&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 10,
        &quot;country_name&quot;: &quot;Cyprus&quot;,
        &quot;slug&quot;: &quot;cyprus&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cyprus.png&quot;,
        &quot;currency&quot;: &quot;Euros (EUR)&quot;,
        &quot;capital&quot;: &quot;Nicosia&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 64,
        &quot;country_name&quot;: &quot;Egypt&quot;,
        &quot;slug&quot;: &quot;egypt&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-egypt.png&quot;,
        &quot;currency&quot;: &quot;Egyptian Pound (EGP)&quot;,
        &quot;capital&quot;: &quot;Cairo&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 12,
        &quot;country_name&quot;: &quot;India&quot;,
        &quot;slug&quot;: &quot;india&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-india.png&quot;,
        &quot;currency&quot;: &quot;Indian Rupee (INR)&quot;,
        &quot;capital&quot;: &quot;New Delhi&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 14,
        &quot;country_name&quot;: &quot;Iran&quot;,
        &quot;slug&quot;: &quot;iran&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-iran.png&quot;,
        &quot;currency&quot;: &quot;Iranian Rial (IRR)&quot;,
        &quot;capital&quot;: &quot;Tehran&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 15,
        &quot;country_name&quot;: &quot;Iraq&quot;,
        &quot;slug&quot;: &quot;iraq&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-iraq.png&quot;,
        &quot;currency&quot;: &quot;Iraqi Dinar (IQD)&quot;,
        &quot;capital&quot;: &quot;Baghdad&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 16,
        &quot;country_name&quot;: &quot;Israel&quot;,
        &quot;slug&quot;: &quot;israel&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-israel.png&quot;,
        &quot;currency&quot;: &quot;Israeli Shekel (ILS)&quot;,
        &quot;capital&quot;: &quot;Jerusalem&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 18,
        &quot;country_name&quot;: &quot;Jordan&quot;,
        &quot;slug&quot;: &quot;jordan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-jordan.png&quot;,
        &quot;currency&quot;: &quot;Jordanian Dinar (JOD)&quot;,
        &quot;capital&quot;: &quot;Amman&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 21,
        &quot;country_name&quot;: &quot;Kuwait&quot;,
        &quot;slug&quot;: &quot;kuwait&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kuwait.png&quot;,
        &quot;currency&quot;: &quot;Kuwaiti Dinar (KWD)&quot;,
        &quot;capital&quot;: &quot;Kuwait City&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 30,
        &quot;country_name&quot;: &quot;Oman&quot;,
        &quot;slug&quot;: &quot;oman&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-oman.png&quot;,
        &quot;currency&quot;: &quot;Omani Rial (OMR)&quot;,
        &quot;capital&quot;: &quot;Muscat&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 31,
        &quot;country_name&quot;: &quot;Pakistan&quot;,
        &quot;slug&quot;: &quot;pakistan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-pakistan.png&quot;,
        &quot;currency&quot;: &quot;Pakistan Rupee (PKR)&quot;,
        &quot;capital&quot;: &quot;Islamabad&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 32,
        &quot;country_name&quot;: &quot;Palestine&quot;,
        &quot;slug&quot;: &quot;palestine&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-palestinian-territories.png&quot;,
        &quot;currency&quot;: &quot;Israeli new shekel (ILS)&quot;,
        &quot;capital&quot;: &quot;Jerusalem (claimed)&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 34,
        &quot;country_name&quot;: &quot;Qatar&quot;,
        &quot;slug&quot;: &quot;qatar&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-qatar.png&quot;,
        &quot;currency&quot;: &quot;Qatari Riyal (QAR)&quot;,
        &quot;capital&quot;: &quot;Doha&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 36,
        &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
        &quot;slug&quot;: &quot;saudi-arabia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-saudi-arabia.png&quot;,
        &quot;currency&quot;: &quot;Saudi Riyal (SAR)&quot;,
        &quot;capital&quot;: &quot;Riyadh&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 39,
        &quot;country_name&quot;: &quot;Syria&quot;,
        &quot;slug&quot;: &quot;syria&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-syria.png&quot;,
        &quot;currency&quot;: &quot;Syrian Pound (SYP)&quot;,
        &quot;capital&quot;: &quot;Damascus&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 231,
        &quot;country_name&quot;: &quot;Taiwan&quot;,
        &quot;slug&quot;: &quot;taiwan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-taiwan.png&quot;,
        &quot;currency&quot;: &quot;New Taiwan dollar (TWD)&quot;,
        &quot;capital&quot;: &quot;Taipei&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-11-07T23:43:07.000000Z&quot;
    },
    {
        &quot;id&quot;: 43,
        &quot;country_name&quot;: &quot;Turkey&quot;,
        &quot;slug&quot;: &quot;turkey&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-turkey.png&quot;,
        &quot;currency&quot;: &quot;Turkish Lira (TRY)&quot;,
        &quot;capital&quot;: &quot;Ankara&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 45,
        &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
        &quot;slug&quot;: &quot;united-arab-emirates&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
        &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
        &quot;capital&quot;: &quot;Abu Dhabi&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 48,
        &quot;country_name&quot;: &quot;Yemen&quot;,
        &quot;slug&quot;: &quot;yemen&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-yemen.png&quot;,
        &quot;currency&quot;: &quot;Yemeni Rial (YER)&quot;,
        &quot;capital&quot;: &quot;Sanaa&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    }
]</code>
 </pre>
    </span>
<span id="execution-results-GETapi-countries-countries" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-countries-countries"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-countries-countries"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-countries-countries" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-countries-countries">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-countries-countries" data-method="GET"
      data-path="api/countries/countries"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-countries-countries', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-countries-countries"
                    onclick="tryItOut('GETapi-countries-countries');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-countries-countries"
                    onclick="cancelTryOut('GETapi-countries-countries');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-countries-countries"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/countries/countries</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-countries-countries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-countries-countries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-GETapi-countries-getcountries">GET api/countries/getcountries</h2>

<p>
</p>



<span id="example-requests-GETapi-countries-getcountries">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/countries/getcountries" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/getcountries"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-countries-getcountries">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1998
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: true,
    &quot;data&quot;: [
        {
            &quot;id&quot;: 4,
            &quot;country_name&quot;: &quot;Bahrain&quot;,
            &quot;slug&quot;: &quot;bahrain&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-bahrain.png&quot;,
            &quot;currency&quot;: &quot;Bahraini Dinar (BHD)&quot;,
            &quot;capital&quot;: &quot;Manama&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 16,
            &quot;country_name&quot;: &quot;Israel&quot;,
            &quot;slug&quot;: &quot;israel&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-israel.png&quot;,
            &quot;currency&quot;: &quot;Israeli Shekel (ILS)&quot;,
            &quot;capital&quot;: &quot;Jerusalem&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 18,
            &quot;country_name&quot;: &quot;Jordan&quot;,
            &quot;slug&quot;: &quot;jordan&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-jordan.png&quot;,
            &quot;currency&quot;: &quot;Jordanian Dinar (JOD)&quot;,
            &quot;capital&quot;: &quot;Amman&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 21,
            &quot;country_name&quot;: &quot;Kuwait&quot;,
            &quot;slug&quot;: &quot;kuwait&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-kuwait.png&quot;,
            &quot;currency&quot;: &quot;Kuwaiti Dinar (KWD)&quot;,
            &quot;capital&quot;: &quot;Kuwait City&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 30,
            &quot;country_name&quot;: &quot;Oman&quot;,
            &quot;slug&quot;: &quot;oman&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-oman.png&quot;,
            &quot;currency&quot;: &quot;Omani Rial (OMR)&quot;,
            &quot;capital&quot;: &quot;Muscat&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 34,
            &quot;country_name&quot;: &quot;Qatar&quot;,
            &quot;slug&quot;: &quot;qatar&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-qatar.png&quot;,
            &quot;currency&quot;: &quot;Qatari Riyal (QAR)&quot;,
            &quot;capital&quot;: &quot;Doha&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 36,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;slug&quot;: &quot;saudi-arabia&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-saudi-arabia.png&quot;,
            &quot;currency&quot;: &quot;Saudi Riyal (SAR)&quot;,
            &quot;capital&quot;: &quot;Riyadh&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 45,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;slug&quot;: &quot;united-arab-emirates&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
            &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
            &quot;capital&quot;: &quot;Abu Dhabi&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        {
            &quot;id&quot;: 64,
            &quot;country_name&quot;: &quot;Egypt&quot;,
            &quot;slug&quot;: &quot;egypt&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-egypt.png&quot;,
            &quot;currency&quot;: &quot;Egyptian Pound (EGP)&quot;,
            &quot;capital&quot;: &quot;Cairo&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        }
    ],
    &quot;jobs&quot;: {
        &quot;18&quot;: 42,
        &quot;21&quot;: 42,
        &quot;30&quot;: 54,
        &quot;34&quot;: 55,
        &quot;36&quot;: 119,
        &quot;4&quot;: 65,
        &quot;45&quot;: 296,
        &quot;64&quot;: 95
    },
    &quot;message&quot;: &quot;countries listing successfully!&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-countries-getcountries" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-countries-getcountries"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-countries-getcountries"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-countries-getcountries" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-countries-getcountries">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-countries-getcountries" data-method="GET"
      data-path="api/countries/getcountries"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-countries-getcountries', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-countries-getcountries"
                    onclick="tryItOut('GETapi-countries-getcountries');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-countries-getcountries"
                    onclick="cancelTryOut('GETapi-countries-getcountries');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-countries-getcountries"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/countries/getcountries</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-countries-getcountries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-countries-getcountries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-POSTapi-countries-editandsavecountry">POST api/countries/editandsavecountry</h2>

<p>
</p>



<span id="example-requests-POSTapi-countries-editandsavecountry">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://api.thetalentpoint.com/api/countries/editandsavecountry" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/editandsavecountry"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-POSTapi-countries-editandsavecountry">
</span>
<span id="execution-results-POSTapi-countries-editandsavecountry" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-countries-editandsavecountry"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-countries-editandsavecountry"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-countries-editandsavecountry" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-countries-editandsavecountry">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-POSTapi-countries-editandsavecountry" data-method="POST"
      data-path="api/countries/editandsavecountry"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-countries-editandsavecountry', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-POSTapi-countries-editandsavecountry"
                    onclick="tryItOut('POSTapi-countries-editandsavecountry');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-POSTapi-countries-editandsavecountry"
                    onclick="cancelTryOut('POSTapi-countries-editandsavecountry');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-POSTapi-countries-editandsavecountry"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/countries/editandsavecountry</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="POSTapi-countries-editandsavecountry"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="POSTapi-countries-editandsavecountry"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-DELETEapi-countries-country">DELETE api/countries/country</h2>

<p>
</p>



<span id="example-requests-DELETEapi-countries-country">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request DELETE \
    "https://api.thetalentpoint.com/api/countries/country" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/country"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-DELETEapi-countries-country">
</span>
<span id="execution-results-DELETEapi-countries-country" hidden>
    <blockquote>Received response<span
                id="execution-response-status-DELETEapi-countries-country"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-DELETEapi-countries-country"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-DELETEapi-countries-country" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-DELETEapi-countries-country">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-DELETEapi-countries-country" data-method="DELETE"
      data-path="api/countries/country"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('DELETEapi-countries-country', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-DELETEapi-countries-country"
                    onclick="tryItOut('DELETEapi-countries-country');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-DELETEapi-countries-country"
                    onclick="cancelTryOut('DELETEapi-countries-country');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-DELETEapi-countries-country"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-red">DELETE</small>
            <b><code>api/countries/country</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="DELETEapi-countries-country"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="DELETEapi-countries-country"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-GETapi-countries-getallcountriesforadmin">GET api/countries/getallcountriesforadmin</h2>

<p>
</p>



<span id="example-requests-GETapi-countries-getallcountriesforadmin">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/countries/getallcountriesforadmin" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/getallcountriesforadmin"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-countries-getallcountriesforadmin">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1997
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">[
    {
        &quot;id&quot;: 231,
        &quot;country_name&quot;: &quot;Taiwan&quot;,
        &quot;slug&quot;: &quot;taiwan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-taiwan.png&quot;,
        &quot;currency&quot;: &quot;New Taiwan dollar (TWD)&quot;,
        &quot;capital&quot;: &quot;Taipei&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-11-07T23:43:07.000000Z&quot;
    },
    {
        &quot;id&quot;: 230,
        &quot;country_name&quot;: &quot;North Korea&quot;,
        &quot;slug&quot;: &quot;north-korea&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-north-korea.png&quot;,
        &quot;currency&quot;: &quot;Korean Won (KPW)&quot;,
        &quot;capital&quot;: &quot;Pyongyang&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 229,
        &quot;country_name&quot;: &quot;North Macedonia&quot;,
        &quot;slug&quot;: &quot;north-macedonia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-north-macedonia.png&quot;,
        &quot;currency&quot;: &quot;Macedonian Denar (MKD)&quot;,
        &quot;capital&quot;: &quot;Skopje&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 228,
        &quot;country_name&quot;: &quot;Montserrat&quot;,
        &quot;slug&quot;: &quot;montserrat&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-montserrat.png&quot;,
        &quot;currency&quot;: &quot;East Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;Plymouth&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 227,
        &quot;country_name&quot;: &quot;Guam&quot;,
        &quot;slug&quot;: &quot;guam&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-guam.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Hagatna&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 226,
        &quot;country_name&quot;: &quot;Guadeloupe&quot;,
        &quot;slug&quot;: &quot;guadeloupe&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-guadeloupe.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Basse-Terre&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 225,
        &quot;country_name&quot;: &quot;French Polynesia&quot;,
        &quot;slug&quot;: &quot;french-polynesia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-french-polynesia.png&quot;,
        &quot;currency&quot;: &quot;Franc (XPF)&quot;,
        &quot;capital&quot;: &quot;Papeete&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 224,
        &quot;country_name&quot;: &quot;Faroe Islands&quot;,
        &quot;slug&quot;: &quot;faroe-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-faroe-islands.png&quot;,
        &quot;currency&quot;: &quot;Faroese Kr&oacute;na (DKK)&quot;,
        &quot;capital&quot;: &quot;Torshavn&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 223,
        &quot;country_name&quot;: &quot;Eswatini&quot;,
        &quot;slug&quot;: &quot;eswatini&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-eswatini.png&quot;,
        &quot;currency&quot;: &quot;Swazi Lilangeni (SZL)&quot;,
        &quot;capital&quot;: &quot;Mbabane&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 222,
        &quot;country_name&quot;: &quot;Cook Islands&quot;,
        &quot;slug&quot;: null,
        &quot;flag&quot;: &quot;twemoji_flag-cook-islands.png&quot;,
        &quot;currency&quot;: &quot;New Zealand Dollar (NZD)&quot;,
        &quot;capital&quot;: &quot;Avarua&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 221,
        &quot;country_name&quot;: &quot;Curacao&quot;,
        &quot;slug&quot;: &quot;curacao&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-curacao.png&quot;,
        &quot;currency&quot;: &quot;Antillean guilder (ANG)&quot;,
        &quot;capital&quot;: &quot;Willemstad&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-03-03T07:11:44.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-04-12T12:11:57.000000Z&quot;
    },
    {
        &quot;id&quot;: 220,
        &quot;country_name&quot;: &quot;Aruba&quot;,
        &quot;slug&quot;: &quot;aruba&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-aruba.png&quot;,
        &quot;currency&quot;: &quot;Aruban Florin (AWG)&quot;,
        &quot;capital&quot;: &quot;Oranjestad&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 219,
        &quot;country_name&quot;: &quot;Anguilla&quot;,
        &quot;slug&quot;: &quot;anguilla&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-anguilla.png&quot;,
        &quot;currency&quot;: &quot;Eastern Caribbean dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;The Valley&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 218,
        &quot;country_name&quot;: &quot;American Samoa&quot;,
        &quot;slug&quot;: &quot;american-samoa&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-american-samoa.png&quot;,
        &quot;currency&quot;: &quot;United States dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Pago Pago&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 217,
        &quot;country_name&quot;: &quot;Aland Islands&quot;,
        &quot;slug&quot;: &quot;aland-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-aland-islands.png&quot;,
        &quot;currency&quot;: &quot;the Euro (&euro;)&quot;,
        &quot;capital&quot;: &quot;Mariehamn&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 216,
        &quot;country_name&quot;: &quot;Cayman Islands&quot;,
        &quot;slug&quot;: &quot;cayman-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cayman-islands.png&quot;,
        &quot;currency&quot;: &quot;The United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;George Town&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 215,
        &quot;country_name&quot;: &quot;Kosovo&quot;,
        &quot;slug&quot;: &quot;kosovo&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kosovo.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Pristina&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 214,
        &quot;country_name&quot;: &quot;Greenland&quot;,
        &quot;slug&quot;: &quot;greenland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-greenland.png&quot;,
        &quot;currency&quot;: &quot;Danish Krone (DK)&quot;,
        &quot;capital&quot;: &quot;Nuuk&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 213,
        &quot;country_name&quot;: &quot;Gibraltar&quot;,
        &quot;slug&quot;: &quot;gibraltar&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-gibraltar.png&quot;,
        &quot;currency&quot;: &quot;Gibraltar Pound (GIP)&quot;,
        &quot;capital&quot;: &quot;Gibraltar&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 212,
        &quot;country_name&quot;: &quot;Bermuda&quot;,
        &quot;slug&quot;: &quot;bermuda&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bermuda.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Hamilton&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 211,
        &quot;country_name&quot;: &quot;British Virgin Islands&quot;,
        &quot;slug&quot;: &quot;british-virgin-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-british-virgin-islands.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Road Town&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 210,
        &quot;country_name&quot;: &quot;Puerto Rico&quot;,
        &quot;slug&quot;: &quot;puerto-rico&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-puerto-rico.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;San Juan&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 209,
        &quot;country_name&quot;: &quot;Serbia&quot;,
        &quot;slug&quot;: &quot;serbia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-serbia.png&quot;,
        &quot;currency&quot;: &quot;Serbian Dinar (RSD)&quot;,
        &quot;capital&quot;: &quot;Belgrade&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 208,
        &quot;country_name&quot;: &quot;Montenegro&quot;,
        &quot;slug&quot;: &quot;montenegro&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-montenegro.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Podgorica&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 207,
        &quot;country_name&quot;: &quot;Ireland&quot;,
        &quot;slug&quot;: &quot;ireland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-ireland.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Dublin&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 206,
        &quot;country_name&quot;: &quot;Hong Kong&quot;,
        &quot;slug&quot;: &quot;hong-kong&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-hong-kong.png&quot;,
        &quot;currency&quot;: &quot;Hong Kong dollar (HKD)&quot;,
        &quot;capital&quot;: &quot;Victoria&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 205,
        &quot;country_name&quot;: &quot;Vanuatu&quot;,
        &quot;slug&quot;: &quot;vanuatu&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-vanuatu.png&quot;,
        &quot;currency&quot;: &quot;Vanuatu Vatu (VUV)&quot;,
        &quot;capital&quot;: &quot;Port Vila&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 204,
        &quot;country_name&quot;: &quot;Tuvalu&quot;,
        &quot;slug&quot;: &quot;tuvalu&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-tuvalu.png&quot;,
        &quot;currency&quot;: &quot;Dollar Tuvaluan (TVD)&quot;,
        &quot;capital&quot;: &quot;Funafuti&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 203,
        &quot;country_name&quot;: &quot;Tonga&quot;,
        &quot;slug&quot;: &quot;tonga&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-tonga.png&quot;,
        &quot;currency&quot;: &quot;Tongan Paanga (TOP)&quot;,
        &quot;capital&quot;: &quot;Nukualofa&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 202,
        &quot;country_name&quot;: &quot;Solomon Islands&quot;,
        &quot;slug&quot;: &quot;solomon-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-solomon-islands.png&quot;,
        &quot;currency&quot;: &quot;Solomon Islands Dollar (SBD)&quot;,
        &quot;capital&quot;: &quot;Honiara&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 201,
        &quot;country_name&quot;: &quot;Samoa&quot;,
        &quot;slug&quot;: &quot;samoa&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-samoa.png&quot;,
        &quot;currency&quot;: &quot;Samoan Tala (WST)&quot;,
        &quot;capital&quot;: &quot;Apia&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 200,
        &quot;country_name&quot;: &quot;Papua New Guinea&quot;,
        &quot;slug&quot;: &quot;papua-new-guinea&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-papua-new-guinea.png&quot;,
        &quot;currency&quot;: &quot;Kina (PGK)&quot;,
        &quot;capital&quot;: &quot;Port Moresby&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 199,
        &quot;country_name&quot;: &quot;Palau&quot;,
        &quot;slug&quot;: &quot;palau&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-palau.png&quot;,
        &quot;currency&quot;: &quot;Fortnightly (USD)&quot;,
        &quot;capital&quot;: &quot;Koror&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 198,
        &quot;country_name&quot;: &quot;New Zealand&quot;,
        &quot;slug&quot;: &quot;new-zealand&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-new-zealand.png&quot;,
        &quot;currency&quot;: &quot;New Zealand Dollar (NZD)&quot;,
        &quot;capital&quot;: &quot;Wellington&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 197,
        &quot;country_name&quot;: &quot;Nauru&quot;,
        &quot;slug&quot;: &quot;nauru&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-nauru.png&quot;,
        &quot;currency&quot;: &quot;Australian Dollar (AUD)&quot;,
        &quot;capital&quot;: &quot;Yaren&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 196,
        &quot;country_name&quot;: &quot;Micronesia&quot;,
        &quot;slug&quot;: &quot;micronesia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-micronesia.png&quot;,
        &quot;currency&quot;: &quot;United States dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Palikir&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 195,
        &quot;country_name&quot;: &quot;Marshall Islands&quot;,
        &quot;slug&quot;: &quot;marshall-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-marshall-islands.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Majuro&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 194,
        &quot;country_name&quot;: &quot;Kiribati&quot;,
        &quot;slug&quot;: &quot;kiribati&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kiribati.png&quot;,
        &quot;currency&quot;: &quot;Australian Dollar (AUD)&quot;,
        &quot;capital&quot;: &quot;Tarawa&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 193,
        &quot;country_name&quot;: &quot;Fiji&quot;,
        &quot;slug&quot;: &quot;fiji&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-fiji.png&quot;,
        &quot;currency&quot;: &quot;Fijian Dollar (FJD)&quot;,
        &quot;capital&quot;: &quot;Suva&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 192,
        &quot;country_name&quot;: &quot;Australia&quot;,
        &quot;slug&quot;: &quot;australia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-australia.png&quot;,
        &quot;currency&quot;: &quot;Australian Dollar (AUD)&quot;,
        &quot;capital&quot;: &quot;Canberra&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 191,
        &quot;country_name&quot;: &quot;Yugoslavia&quot;,
        &quot;slug&quot;: &quot;yugoslavia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-syria.png&quot;,
        &quot;currency&quot;: &quot;Yugoslav dinar (YUD)&quot;,
        &quot;capital&quot;: &quot;Belgrade&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 190,
        &quot;country_name&quot;: &quot;Vatican City&quot;,
        &quot;slug&quot;: &quot;vatican-city&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-vatican-city.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Vatican City&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 188,
        &quot;country_name&quot;: &quot;United Kingdom&quot;,
        &quot;slug&quot;: &quot;united-kingdom&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-united-kingdom.png&quot;,
        &quot;currency&quot;: &quot;Pound Sterling (GBP)&quot;,
        &quot;capital&quot;: &quot;London&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 187,
        &quot;country_name&quot;: &quot;Ukraine&quot;,
        &quot;slug&quot;: &quot;ukraine&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-ukraine.png&quot;,
        &quot;currency&quot;: &quot;Ukrainian Hryvnia (UAH)&quot;,
        &quot;capital&quot;: &quot;Kiev&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 185,
        &quot;country_name&quot;: &quot;Switzerland&quot;,
        &quot;slug&quot;: &quot;switzerland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-switzerland.png&quot;,
        &quot;currency&quot;: &quot;Swiss Franc (CHF)&quot;,
        &quot;capital&quot;: &quot;Berne&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 184,
        &quot;country_name&quot;: &quot;Sweden&quot;,
        &quot;slug&quot;: &quot;sweden&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-sweden.png&quot;,
        &quot;currency&quot;: &quot;Swedish Krona (SEK)&quot;,
        &quot;capital&quot;: &quot;Stockholm&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 183,
        &quot;country_name&quot;: &quot;Spain&quot;,
        &quot;slug&quot;: &quot;spain&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-spain.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Madrid&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    {
        &quot;id&quot;: 182,
        &quot;country_name&quot;: &quot;Slovenia&quot;,
        &quot;slug&quot;: &quot;slovenia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-slovenia.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Ljubljana&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 181,
        &quot;country_name&quot;: &quot;Slovakia&quot;,
        &quot;slug&quot;: &quot;slovakia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-slovakia.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Bratislava&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 180,
        &quot;country_name&quot;: &quot;Scotland&quot;,
        &quot;slug&quot;: &quot;scotland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-scotland.png&quot;,
        &quot;currency&quot;: &quot;Pound Sterling (GBP)&quot;,
        &quot;capital&quot;: &quot;Edinburgh&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 179,
        &quot;country_name&quot;: &quot;San Marino&quot;,
        &quot;slug&quot;: &quot;san-marino&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-san-marino.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;San Marino&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 177,
        &quot;country_name&quot;: &quot;Romania&quot;,
        &quot;slug&quot;: &quot;romania&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-romania.png&quot;,
        &quot;currency&quot;: &quot;Romanian Leu (RON)&quot;,
        &quot;capital&quot;: &quot;Bucharest&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 176,
        &quot;country_name&quot;: &quot;Portugal&quot;,
        &quot;slug&quot;: &quot;portugal&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-portugal.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Lisbon&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 175,
        &quot;country_name&quot;: &quot;Poland&quot;,
        &quot;slug&quot;: &quot;poland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-poland.png&quot;,
        &quot;currency&quot;: &quot;Polish Zloty (PLN)&quot;,
        &quot;capital&quot;: &quot;Warsaw&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 174,
        &quot;country_name&quot;: &quot;Northern Ireland&quot;,
        &quot;slug&quot;: &quot;northern-ireland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-united-kingdom.png&quot;,
        &quot;currency&quot;: &quot;British Pound (GBP)&quot;,
        &quot;capital&quot;: &quot;Belfast&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 173,
        &quot;country_name&quot;: &quot;Norway&quot;,
        &quot;slug&quot;: &quot;norway&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-norway.png&quot;,
        &quot;currency&quot;: &quot;Norwegian Krone (NOK)&quot;,
        &quot;capital&quot;: &quot;Oslo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 172,
        &quot;country_name&quot;: &quot;Netherlands&quot;,
        &quot;slug&quot;: &quot;netherlands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-netherlands.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Amsterdam&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 171,
        &quot;country_name&quot;: &quot;Monaco&quot;,
        &quot;slug&quot;: &quot;monaco&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-monaco.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Monaco&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 170,
        &quot;country_name&quot;: &quot;Moldova&quot;,
        &quot;slug&quot;: &quot;moldova&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-moldova.png&quot;,
        &quot;currency&quot;: &quot;Moldovan leu (MDL)&quot;,
        &quot;capital&quot;: &quot;Kishinev&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 169,
        &quot;country_name&quot;: &quot;Martinique&quot;,
        &quot;slug&quot;: &quot;martinique&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-martinique.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Fort-de-France&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 168,
        &quot;country_name&quot;: &quot;Malta&quot;,
        &quot;slug&quot;: &quot;malta&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-malta.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Valletta&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 167,
        &quot;country_name&quot;: &quot;Macedonia&quot;,
        &quot;slug&quot;: &quot;macedonia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-north-macedonia.png&quot;,
        &quot;currency&quot;: &quot;Macedonian Denar (MKD)&quot;,
        &quot;capital&quot;: &quot;Skopje&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 166,
        &quot;country_name&quot;: &quot;Luxembourg&quot;,
        &quot;slug&quot;: &quot;luxembourg&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-luxembourg.png&quot;,
        &quot;currency&quot;: &quot;Euros (EUR)&quot;,
        &quot;capital&quot;: &quot;Luxembourg&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 165,
        &quot;country_name&quot;: &quot;Lithuania&quot;,
        &quot;slug&quot;: &quot;lithuania&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-lithuania.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Vilnius&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 164,
        &quot;country_name&quot;: &quot;Liechtenstein&quot;,
        &quot;slug&quot;: &quot;liechtenstein&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-liechtenstein.png&quot;,
        &quot;currency&quot;: &quot;Swiss franc (CHF)&quot;,
        &quot;capital&quot;: &quot;Vaduz&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 163,
        &quot;country_name&quot;: &quot;Latvia&quot;,
        &quot;slug&quot;: &quot;latvia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-latvia.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Riga&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 162,
        &quot;country_name&quot;: &quot;Italy&quot;,
        &quot;slug&quot;: &quot;italy&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-italy.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Rome&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 161,
        &quot;country_name&quot;: &quot;Iceland&quot;,
        &quot;slug&quot;: &quot;iceland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-iceland.png&quot;,
        &quot;currency&quot;: &quot;Icelandic Kr&oacute;na (ISK)&quot;,
        &quot;capital&quot;: &quot;Reykjavik&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 160,
        &quot;country_name&quot;: &quot;Hungary&quot;,
        &quot;slug&quot;: &quot;hungary&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-hungary.png&quot;,
        &quot;currency&quot;: &quot;Florin (HUF)&quot;,
        &quot;capital&quot;: &quot;Budapest&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 159,
        &quot;country_name&quot;: &quot;Greece&quot;,
        &quot;slug&quot;: &quot;greece&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-greece.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Athens&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 158,
        &quot;country_name&quot;: &quot;Germany&quot;,
        &quot;slug&quot;: &quot;germany&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-germany.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Berlin&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 155,
        &quot;country_name&quot;: &quot;France&quot;,
        &quot;slug&quot;: &quot;france&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-france.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Paris&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 154,
        &quot;country_name&quot;: &quot;Finland&quot;,
        &quot;slug&quot;: &quot;finland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-finland.png&quot;,
        &quot;currency&quot;: &quot;Euros (EUR)&quot;,
        &quot;capital&quot;: &quot;Helsinki&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 153,
        &quot;country_name&quot;: &quot;Estonia&quot;,
        &quot;slug&quot;: &quot;estonia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-estonia.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Tallinn&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 152,
        &quot;country_name&quot;: &quot;Denmark&quot;,
        &quot;slug&quot;: &quot;denmark&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-denmark.png&quot;,
        &quot;currency&quot;: &quot;Danish Krone (DKK)&quot;,
        &quot;capital&quot;: &quot;Copenhagen&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 151,
        &quot;country_name&quot;: &quot;Czech Republic&quot;,
        &quot;slug&quot;: &quot;czech-republic&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-czechia.png&quot;,
        &quot;currency&quot;: &quot;Crown - Czech (CZK)&quot;,
        &quot;capital&quot;: &quot;Prague&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 149,
        &quot;country_name&quot;: &quot;Croatia&quot;,
        &quot;slug&quot;: &quot;croatia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-croatia.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Zagreb&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 148,
        &quot;country_name&quot;: &quot;Bulgaria&quot;,
        &quot;slug&quot;: &quot;bulgaria&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bulgaria.png&quot;,
        &quot;currency&quot;: &quot;Bulgarian Lev (BGN)&quot;,
        &quot;capital&quot;: &quot;Sofia&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 147,
        &quot;country_name&quot;: &quot;Bosnia &amp; Herzegovina&quot;,
        &quot;slug&quot;: &quot;bosnia-herzegovina&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bosnia-and-herzegovina.png&quot;,
        &quot;currency&quot;: &quot;Bosnian Herzegovinian Convertible Mark (BAM)&quot;,
        &quot;capital&quot;: &quot;Sarajevo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 146,
        &quot;country_name&quot;: &quot;Belgium&quot;,
        &quot;slug&quot;: &quot;belgium&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-belgium.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Brussels&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 145,
        &quot;country_name&quot;: &quot;Belarus&quot;,
        &quot;slug&quot;: &quot;belarus&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-belarus.png&quot;,
        &quot;currency&quot;: &quot;Belarusian ruble (BYN)&quot;,
        &quot;capital&quot;: &quot;Minsk&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 143,
        &quot;country_name&quot;: &quot;Austria&quot;,
        &quot;slug&quot;: &quot;austria&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-austria.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Vienna&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 141,
        &quot;country_name&quot;: &quot;Andorra&quot;,
        &quot;slug&quot;: &quot;andorra&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-andorra.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Andorra-la-vella&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 140,
        &quot;country_name&quot;: &quot;Albania&quot;,
        &quot;slug&quot;: &quot;albania&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-albania.png&quot;,
        &quot;currency&quot;: &quot;Albanian Lek (ALL)&quot;,
        &quot;capital&quot;: &quot;Tirane&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 139,
        &quot;country_name&quot;: &quot;Falkland Islands&quot;,
        &quot;slug&quot;: &quot;falkland-islands&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-falkland-islands.png&quot;,
        &quot;currency&quot;: &quot;Falkland Islands Pound (FKP)&quot;,
        &quot;capital&quot;: &quot;Stanley&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 138,
        &quot;country_name&quot;: &quot;French Guiana&quot;,
        &quot;slug&quot;: &quot;french-guiana&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-french-guiana.png&quot;,
        &quot;currency&quot;: &quot;Euro (EUR)&quot;,
        &quot;capital&quot;: &quot;Cayenne&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 137,
        &quot;country_name&quot;: &quot;Venezuela&quot;,
        &quot;slug&quot;: &quot;venezuela&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-venezuela.png&quot;,
        &quot;currency&quot;: &quot;Venezuelan Bolivar (VES)&quot;,
        &quot;capital&quot;: &quot;Caracas&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 136,
        &quot;country_name&quot;: &quot;Uruguay&quot;,
        &quot;slug&quot;: &quot;uruguay&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-uruguay.png&quot;,
        &quot;currency&quot;: &quot;Uruguayan Peso (UYU)&quot;,
        &quot;capital&quot;: &quot;Montevideo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 135,
        &quot;country_name&quot;: &quot;Suriname&quot;,
        &quot;slug&quot;: &quot;suriname&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-suriname.png&quot;,
        &quot;currency&quot;: &quot;Surinamese Dollar (SRD)&quot;,
        &quot;capital&quot;: &quot;Paramaribo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 134,
        &quot;country_name&quot;: &quot;Peru&quot;,
        &quot;slug&quot;: &quot;peru&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-peru.png&quot;,
        &quot;currency&quot;: &quot;Peruvian Sol (PEN)&quot;,
        &quot;capital&quot;: &quot;Lima&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 133,
        &quot;country_name&quot;: &quot;Paraguay&quot;,
        &quot;slug&quot;: &quot;paraguay&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-paraguay.png&quot;,
        &quot;currency&quot;: &quot;Paraguayan Guaran&iacute; (PYG)&quot;,
        &quot;capital&quot;: &quot;Asuncion&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 132,
        &quot;country_name&quot;: &quot;Guyana&quot;,
        &quot;slug&quot;: &quot;guyana&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-guyana.png&quot;,
        &quot;currency&quot;: &quot;Guyanese Dollar (GYD)&quot;,
        &quot;capital&quot;: &quot;Georgetown&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 131,
        &quot;country_name&quot;: &quot;Ecuador&quot;,
        &quot;slug&quot;: &quot;ecuador&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-ecuador.png&quot;,
        &quot;currency&quot;: &quot;United State Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Quito&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 130,
        &quot;country_name&quot;: &quot;Colombia&quot;,
        &quot;slug&quot;: &quot;colombia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-colombia.png&quot;,
        &quot;currency&quot;: &quot;Colombian Peso (COP)&quot;,
        &quot;capital&quot;: &quot;Bogota&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 129,
        &quot;country_name&quot;: &quot;Chile&quot;,
        &quot;slug&quot;: &quot;chile&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-chile.png&quot;,
        &quot;currency&quot;: &quot;Chilean Peso (CLP)&quot;,
        &quot;capital&quot;: &quot;Santiago&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 128,
        &quot;country_name&quot;: &quot;Brazil&quot;,
        &quot;slug&quot;: &quot;brazil&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-brazil.png&quot;,
        &quot;currency&quot;: &quot;Brazilian Real (BRL)&quot;,
        &quot;capital&quot;: &quot;Brasilia&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 127,
        &quot;country_name&quot;: &quot;Bolivia&quot;,
        &quot;slug&quot;: &quot;bolivia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bolivia.png&quot;,
        &quot;currency&quot;: &quot;Boliviano (BOB)&quot;,
        &quot;capital&quot;: &quot;La Paz&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 126,
        &quot;country_name&quot;: &quot;Argentina&quot;,
        &quot;slug&quot;: &quot;argentina&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-argentina.png&quot;,
        &quot;currency&quot;: &quot;Argentine Peso (ARS)&quot;,
        &quot;capital&quot;: &quot;Buenos Aires&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 125,
        &quot;country_name&quot;: &quot;United States of America&quot;,
        &quot;slug&quot;: &quot;united-states-of-america&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-united-states.png&quot;,
        &quot;currency&quot;: &quot;United State Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Washington D.C.&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 124,
        &quot;country_name&quot;: &quot;Trinidad and Tobago&quot;,
        &quot;slug&quot;: &quot;trinidad-and-tobago&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-trinidad-and-tobago.png&quot;,
        &quot;currency&quot;: &quot;Trinidad and Tobago Dollar (TTD)&quot;,
        &quot;capital&quot;: &quot;Port of Spain&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 123,
        &quot;country_name&quot;: &quot;Saint Vincent and the Grenadines&quot;,
        &quot;slug&quot;: &quot;saint-vincent-and-the-grenadines&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-st-vincent-and-grenadines.png&quot;,
        &quot;currency&quot;: &quot;East Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;Kingstown&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 122,
        &quot;country_name&quot;: &quot;Saint Lucia&quot;,
        &quot;slug&quot;: &quot;saint-lucia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-st-lucia.png&quot;,
        &quot;currency&quot;: &quot;Eastern Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;Castries&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 121,
        &quot;country_name&quot;: &quot;Saint Kitts and Nevis&quot;,
        &quot;slug&quot;: &quot;saint-kitts-and-nevis&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-st-kitts-and-nevis.png&quot;,
        &quot;currency&quot;: &quot;East Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;Basseterre&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 120,
        &quot;country_name&quot;: &quot;Panama&quot;,
        &quot;slug&quot;: &quot;panama&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-panama.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Panama City&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 119,
        &quot;country_name&quot;: &quot;Nicaragua&quot;,
        &quot;slug&quot;: &quot;nicaragua&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-nicaragua.png&quot;,
        &quot;currency&quot;: &quot;Nicaraguan C&oacute;rdoba (NIO)&quot;,
        &quot;capital&quot;: &quot;Managua&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 118,
        &quot;country_name&quot;: &quot;Mexico&quot;,
        &quot;slug&quot;: &quot;mexico&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-mexico.png&quot;,
        &quot;currency&quot;: &quot;Mexican Peso (MXN)&quot;,
        &quot;capital&quot;: &quot;Mexico City&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 117,
        &quot;country_name&quot;: &quot;Jamaica&quot;,
        &quot;slug&quot;: &quot;jamaica&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-jamaica.png&quot;,
        &quot;currency&quot;: &quot;Jamaican Dollar (JMD)&quot;,
        &quot;capital&quot;: &quot;Kingston&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 116,
        &quot;country_name&quot;: &quot;Honduras&quot;,
        &quot;slug&quot;: &quot;honduras&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-honduras.png&quot;,
        &quot;currency&quot;: &quot;Lempira (HNL)&quot;,
        &quot;capital&quot;: &quot;Tegucigalpa&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 115,
        &quot;country_name&quot;: &quot;Haiti&quot;,
        &quot;slug&quot;: &quot;haiti&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-haiti.png&quot;,
        &quot;currency&quot;: &quot;Haitian gourde (HTG)&quot;,
        &quot;capital&quot;: &quot;Port au Prince&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 114,
        &quot;country_name&quot;: &quot;Guatemala&quot;,
        &quot;slug&quot;: &quot;guatemala&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-guatemala.png&quot;,
        &quot;currency&quot;: &quot;Quetzal (GTQ)&quot;,
        &quot;capital&quot;: &quot;Guatemala City&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 113,
        &quot;country_name&quot;: &quot;Grenada&quot;,
        &quot;slug&quot;: &quot;grenada&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-grenada.png&quot;,
        &quot;currency&quot;: &quot;Eastern Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;St. George s&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 112,
        &quot;country_name&quot;: &quot;El Salvador&quot;,
        &quot;slug&quot;: &quot;el-salvador&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-el-salvador.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;San Salvador&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 111,
        &quot;country_name&quot;: &quot;Dominican Republic&quot;,
        &quot;slug&quot;: &quot;dominican-republic&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-dominican-republic.png&quot;,
        &quot;currency&quot;: &quot;Dominican Peso (DOP)&quot;,
        &quot;capital&quot;: &quot;Santo Domingo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 110,
        &quot;country_name&quot;: &quot;Dominica&quot;,
        &quot;slug&quot;: &quot;dominica&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-dominica.png&quot;,
        &quot;currency&quot;: &quot;Eastern Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;Roseau&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 109,
        &quot;country_name&quot;: &quot;Cuba&quot;,
        &quot;slug&quot;: &quot;cuba&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cuba.png&quot;,
        &quot;currency&quot;: &quot;Cuban Peso (CUP)&quot;,
        &quot;capital&quot;: &quot;Havana&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 108,
        &quot;country_name&quot;: &quot;Costa Rica&quot;,
        &quot;slug&quot;: &quot;costa-rica&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-costa-rica.png&quot;,
        &quot;currency&quot;: &quot;Costa Rican Col&oacute;n (CRC)&quot;,
        &quot;capital&quot;: &quot;San Jos&eacute;&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 107,
        &quot;country_name&quot;: &quot;Canada&quot;,
        &quot;slug&quot;: &quot;canada&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-canada.png&quot;,
        &quot;currency&quot;: &quot;Canadian Dollar (CAD)&quot;,
        &quot;capital&quot;: &quot;Ottawa&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 106,
        &quot;country_name&quot;: &quot;Belize&quot;,
        &quot;slug&quot;: &quot;belize&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-belize.png&quot;,
        &quot;currency&quot;: &quot;Belize Dollar (BZD)&quot;,
        &quot;capital&quot;: &quot;Belmopan&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 105,
        &quot;country_name&quot;: &quot;Barbados&quot;,
        &quot;slug&quot;: &quot;barbados&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-barbados.png&quot;,
        &quot;currency&quot;: &quot;Barbados Dollar (BBD)&quot;,
        &quot;capital&quot;: &quot;Bridgetown&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 104,
        &quot;country_name&quot;: &quot;Bahamas&quot;,
        &quot;slug&quot;: &quot;bahamas&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bahamas.png&quot;,
        &quot;currency&quot;: &quot;Bahamian dollar (BSD)&quot;,
        &quot;capital&quot;: &quot;Nassau&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 103,
        &quot;country_name&quot;: &quot;Antigua and Barbuda&quot;,
        &quot;slug&quot;: &quot;antigua-and-barbuda&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-antigua-and-barbuda.png&quot;,
        &quot;currency&quot;: &quot;Eastern Caribbean Dollar (XCD)&quot;,
        &quot;capital&quot;: &quot;St. John s&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 102,
        &quot;country_name&quot;: &quot;Zimbabwe&quot;,
        &quot;slug&quot;: &quot;zimbabwe&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-zimbabwe.png&quot;,
        &quot;currency&quot;: &quot;Zimbabwe Dollar (ZWD)&quot;,
        &quot;capital&quot;: &quot;Harare&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 101,
        &quot;country_name&quot;: &quot;Zambia&quot;,
        &quot;slug&quot;: &quot;zambia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-zambia.png&quot;,
        &quot;currency&quot;: &quot;Zambian Kwacha (ZMW)&quot;,
        &quot;capital&quot;: &quot;Lusaka&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 100,
        &quot;country_name&quot;: &quot;Uganda&quot;,
        &quot;slug&quot;: &quot;uganda&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-uganda.png&quot;,
        &quot;currency&quot;: &quot;Ugandan Shilling (UGX)&quot;,
        &quot;capital&quot;: &quot;Kampala&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 99,
        &quot;country_name&quot;: &quot;Tunisia&quot;,
        &quot;slug&quot;: &quot;tunisia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-tunisia.png&quot;,
        &quot;currency&quot;: &quot;Tunisian Dinar (TND)&quot;,
        &quot;capital&quot;: &quot;Tunis&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 98,
        &quot;country_name&quot;: &quot;Togo&quot;,
        &quot;slug&quot;: &quot;togo&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-togo.png&quot;,
        &quot;currency&quot;: &quot;Togolese Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Lom&eacute;&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 97,
        &quot;country_name&quot;: &quot;Tanzania&quot;,
        &quot;slug&quot;: &quot;tanzania&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-tanzania.png&quot;,
        &quot;currency&quot;: &quot;Tanzanian Shilling (TZS)&quot;,
        &quot;capital&quot;: &quot;Dodoma&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 96,
        &quot;country_name&quot;: &quot;Swaziland&quot;,
        &quot;slug&quot;: &quot;swaziland&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-eswatini.png&quot;,
        &quot;currency&quot;: &quot;Lilangeni (SZL)&quot;,
        &quot;capital&quot;: &quot;Mbabane&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 95,
        &quot;country_name&quot;: &quot;Sudan&quot;,
        &quot;slug&quot;: &quot;sudan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-sudan.png&quot;,
        &quot;currency&quot;: &quot;Sudanese Pound (SDG)&quot;,
        &quot;capital&quot;: &quot;Khartoum&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 94,
        &quot;country_name&quot;: &quot;South Sudan&quot;,
        &quot;slug&quot;: &quot;south-sudan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-south-sudan.png&quot;,
        &quot;currency&quot;: &quot;South Sudanese pound (SS&pound;)&quot;,
        &quot;capital&quot;: &quot;Juba&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 93,
        &quot;country_name&quot;: &quot;South Africa&quot;,
        &quot;slug&quot;: &quot;south-africa&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-south-africa.png&quot;,
        &quot;currency&quot;: &quot;South African Rand (ZAR)&quot;,
        &quot;capital&quot;: &quot;Pretoria&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 92,
        &quot;country_name&quot;: &quot;Somalia&quot;,
        &quot;slug&quot;: &quot;somalia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-somalia.png&quot;,
        &quot;currency&quot;: &quot;Somali Shilling&quot;,
        &quot;capital&quot;: &quot;Mogadishu&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 91,
        &quot;country_name&quot;: &quot;Sierra Leone&quot;,
        &quot;slug&quot;: &quot;sierra-leone&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-sierra-leone.png&quot;,
        &quot;currency&quot;: &quot;Sierra Leonean Leone (SLL)&quot;,
        &quot;capital&quot;: &quot;Freetown&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 90,
        &quot;country_name&quot;: &quot;Seychelles&quot;,
        &quot;slug&quot;: &quot;seychelles&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-seychelles.png&quot;,
        &quot;currency&quot;: &quot;Seychellois Rupee (SCR)&quot;,
        &quot;capital&quot;: &quot;Victoria&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 89,
        &quot;country_name&quot;: &quot;Senegal&quot;,
        &quot;slug&quot;: &quot;senegal&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-senegal.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Dakar&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 88,
        &quot;country_name&quot;: &quot;Sao Tome &amp; Principe&quot;,
        &quot;slug&quot;: &quot;sao-tome-principe&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-sao-tome-and-principe.png&quot;,
        &quot;currency&quot;: &quot; Sao Tome and Principe Dobra (STN)&quot;,
        &quot;capital&quot;: &quot;Sao Tome&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 87,
        &quot;country_name&quot;: &quot;Rwanda&quot;,
        &quot;slug&quot;: &quot;rwanda&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-rwanda.png&quot;,
        &quot;currency&quot;: &quot;Rwandan Franc (RWF)&quot;,
        &quot;capital&quot;: &quot;Kigali&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 86,
        &quot;country_name&quot;: &quot;Nigeria&quot;,
        &quot;slug&quot;: &quot;nigeria&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-nigeria.png&quot;,
        &quot;currency&quot;: &quot;Nigerian Naira (NGN)&quot;,
        &quot;capital&quot;: &quot;Abuja&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 85,
        &quot;country_name&quot;: &quot;Niger&quot;,
        &quot;slug&quot;: &quot;niger&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-niger.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Niamey&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 84,
        &quot;country_name&quot;: &quot;Namibia&quot;,
        &quot;slug&quot;: &quot;namibia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-namibia.png&quot;,
        &quot;currency&quot;: &quot;Namibian Dollar (NAD)&quot;,
        &quot;capital&quot;: &quot;Windhoek&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 83,
        &quot;country_name&quot;: &quot;Mozambique&quot;,
        &quot;slug&quot;: &quot;mozambique&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-mozambique.png&quot;,
        &quot;currency&quot;: &quot;Mozambican Metical (MZN)&quot;,
        &quot;capital&quot;: &quot;Maputo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 82,
        &quot;country_name&quot;: &quot;Morocco&quot;,
        &quot;slug&quot;: &quot;morocco&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-morocco.png&quot;,
        &quot;currency&quot;: &quot;Moroccan Dirham (MAD)&quot;,
        &quot;capital&quot;: &quot;Rabat&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 81,
        &quot;country_name&quot;: &quot;Mauritius&quot;,
        &quot;slug&quot;: &quot;mauritius&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-mauritius.png&quot;,
        &quot;currency&quot;: &quot;Mauritian Rupee (MUR)&quot;,
        &quot;capital&quot;: &quot;Mauritius&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 80,
        &quot;country_name&quot;: &quot;Mauritania&quot;,
        &quot;slug&quot;: &quot;mauritania&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-mauritania.png&quot;,
        &quot;currency&quot;: &quot;Mauritanian Ouguiya (MRU)&quot;,
        &quot;capital&quot;: &quot;Nouakchott&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 79,
        &quot;country_name&quot;: &quot;Mali&quot;,
        &quot;slug&quot;: &quot;mali&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-mali.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Bamako&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 78,
        &quot;country_name&quot;: &quot;Malawi&quot;,
        &quot;slug&quot;: &quot;malawi&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-malawi.png&quot;,
        &quot;currency&quot;: &quot; Malawian Kwacha (MWK)&quot;,
        &quot;capital&quot;: &quot;Lilongwe&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 77,
        &quot;country_name&quot;: &quot;Madagascar&quot;,
        &quot;slug&quot;: &quot;madagascar&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-madagascar.png&quot;,
        &quot;currency&quot;: &quot;Malagasy Ariary (Ar/MGA)&quot;,
        &quot;capital&quot;: &quot;Antananarivo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 76,
        &quot;country_name&quot;: &quot;Libya&quot;,
        &quot;slug&quot;: &quot;libya&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-libya.png&quot;,
        &quot;currency&quot;: &quot;Libyan Dinar (LYD)&quot;,
        &quot;capital&quot;: &quot;Tripoli&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 75,
        &quot;country_name&quot;: &quot;Liberia&quot;,
        &quot;slug&quot;: &quot;liberia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-liberia.png&quot;,
        &quot;currency&quot;: &quot;Liberian Dollar (LRD)&quot;,
        &quot;capital&quot;: &quot;Monrovia&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 74,
        &quot;country_name&quot;: &quot;Lesotho&quot;,
        &quot;slug&quot;: &quot;lesotho&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-lesotho.png&quot;,
        &quot;currency&quot;: &quot;Lesotho Loti (LSL)&quot;,
        &quot;capital&quot;: &quot;Maseru&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 73,
        &quot;country_name&quot;: &quot;Kenya&quot;,
        &quot;slug&quot;: &quot;kenya&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kenya.png&quot;,
        &quot;currency&quot;: &quot;Kenyan Shilling (KES)&quot;,
        &quot;capital&quot;: &quot;Nairobi&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 72,
        &quot;country_name&quot;: &quot;Guinea-Bissau&quot;,
        &quot;slug&quot;: &quot;guinea-bissau&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-guinea-bissau.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Bissau&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 71,
        &quot;country_name&quot;: &quot;Guinea&quot;,
        &quot;slug&quot;: &quot;guinea&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-guinea.png&quot;,
        &quot;currency&quot;: &quot;Guinean Franc (GNF)&quot;,
        &quot;capital&quot;: &quot;Conakry&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 70,
        &quot;country_name&quot;: &quot;Ghana&quot;,
        &quot;slug&quot;: &quot;ghana&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-ghana.png&quot;,
        &quot;currency&quot;: &quot;Ghanaian Cedi (GHS)&quot;,
        &quot;capital&quot;: &quot;Accra&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 69,
        &quot;country_name&quot;: &quot;Gambia&quot;,
        &quot;slug&quot;: &quot;gambia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-gambia.png&quot;,
        &quot;currency&quot;: &quot;Gambian dalasi (GMD)&quot;,
        &quot;capital&quot;: &quot;Banjul&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 68,
        &quot;country_name&quot;: &quot;Gabon&quot;,
        &quot;slug&quot;: &quot;gabon&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-gabon.png&quot;,
        &quot;currency&quot;: &quot;Central African Franc (XFA)&quot;,
        &quot;capital&quot;: &quot;Libreville&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 67,
        &quot;country_name&quot;: &quot;Ethiopia&quot;,
        &quot;slug&quot;: &quot;ethiopia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-ethiopia.png&quot;,
        &quot;currency&quot;: &quot;Ethiopian Birr (ETB)&quot;,
        &quot;capital&quot;: &quot;Addis Ababa&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 66,
        &quot;country_name&quot;: &quot;Eritrea&quot;,
        &quot;slug&quot;: &quot;eritrea&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-eritrea.png&quot;,
        &quot;currency&quot;: &quot;Eritrean nakfa (ERN)&quot;,
        &quot;capital&quot;: &quot;Asmara&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 65,
        &quot;country_name&quot;: &quot;Equatorial Guinea&quot;,
        &quot;slug&quot;: &quot;equatorial-guinea&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-equatorial-guinea.png&quot;,
        &quot;currency&quot;: &quot;Central African CFA franc (CFA)&quot;,
        &quot;capital&quot;: &quot;Malabo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 64,
        &quot;country_name&quot;: &quot;Egypt&quot;,
        &quot;slug&quot;: &quot;egypt&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-egypt.png&quot;,
        &quot;currency&quot;: &quot;Egyptian Pound (EGP)&quot;,
        &quot;capital&quot;: &quot;Cairo&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 63,
        &quot;country_name&quot;: &quot;Djibouti&quot;,
        &quot;slug&quot;: &quot;djibouti&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-djibouti.png&quot;,
        &quot;currency&quot;: &quot;Djiboutian franc (DJF)&quot;,
        &quot;capital&quot;: &quot;Djibouti&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 62,
        &quot;country_name&quot;: &quot;Democratic Republic of Congo&quot;,
        &quot;slug&quot;: &quot;democratic-republic-of-congo&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-congo-kinshasa.png&quot;,
        &quot;currency&quot;: &quot;Congolese Franc (CDF)&quot;,
        &quot;capital&quot;: &quot;Kinshasa&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 61,
        &quot;country_name&quot;: &quot;C&ocirc;te d Ivoire&quot;,
        &quot;slug&quot;: &quot;cote-divoire&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cote-divoire.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Yamoussoukro&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 60,
        &quot;country_name&quot;: &quot;Congo&quot;,
        &quot;slug&quot;: &quot;congo&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-congo-brazzaville.png&quot;,
        &quot;currency&quot;: &quot;Congolese Franc (CDF)&quot;,
        &quot;capital&quot;: &quot;Brazzaville&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 59,
        &quot;country_name&quot;: &quot;Comoros&quot;,
        &quot;slug&quot;: &quot;comoros&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-comoros.png&quot;,
        &quot;currency&quot;: &quot;Comorian franc (KMF)&quot;,
        &quot;capital&quot;: &quot;Moroni&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 58,
        &quot;country_name&quot;: &quot;Chad&quot;,
        &quot;slug&quot;: &quot;chad&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-chad.png&quot;,
        &quot;currency&quot;: &quot;Central African CFA Franc (XAF)&quot;,
        &quot;capital&quot;: &quot;N Djamena&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 57,
        &quot;country_name&quot;: &quot;Central African Rep.&quot;,
        &quot;slug&quot;: &quot;central-african-rep&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-central-african-republic.png&quot;,
        &quot;currency&quot;: &quot;Central African CFA franc (XAF)&quot;,
        &quot;capital&quot;: &quot;Bangui&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 56,
        &quot;country_name&quot;: &quot;Cape Verde&quot;,
        &quot;slug&quot;: &quot;cape-verde&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cape-verde.png&quot;,
        &quot;currency&quot;: &quot;Cape Verdean Escudo (CVE)&quot;,
        &quot;capital&quot;: &quot;Praia&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 55,
        &quot;country_name&quot;: &quot;Cameroon&quot;,
        &quot;slug&quot;: &quot;cameroon&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cameroon.png&quot;,
        &quot;currency&quot;: &quot;Central African CFA Franc (XAF)&quot;,
        &quot;capital&quot;: &quot;Yaounde&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 54,
        &quot;country_name&quot;: &quot;Burundi&quot;,
        &quot;slug&quot;: &quot;burundi&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-burundi.png&quot;,
        &quot;currency&quot;: &quot;Burundian Franc (BIF)&quot;,
        &quot;capital&quot;: &quot;Bujumbura&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 53,
        &quot;country_name&quot;: &quot;Burkina Faso&quot;,
        &quot;slug&quot;: &quot;burkina-faso&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-burkina-faso.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Ouagadougou&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 52,
        &quot;country_name&quot;: &quot;Botswana&quot;,
        &quot;slug&quot;: &quot;botswana&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-botswana.png&quot;,
        &quot;currency&quot;: &quot;Botswana Pula (BWP)&quot;,
        &quot;capital&quot;: &quot;Gaborone&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 51,
        &quot;country_name&quot;: &quot;Benin&quot;,
        &quot;slug&quot;: &quot;benin&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-benin.png&quot;,
        &quot;currency&quot;: &quot;West African CFA Franc (XOF)&quot;,
        &quot;capital&quot;: &quot;Porto-Novo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 50,
        &quot;country_name&quot;: &quot;Angola&quot;,
        &quot;slug&quot;: &quot;angola&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-angola.png&quot;,
        &quot;currency&quot;: &quot;Angolan Kwanza (AOA)&quot;,
        &quot;capital&quot;: &quot;Luanda&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 49,
        &quot;country_name&quot;: &quot;Algeria&quot;,
        &quot;slug&quot;: &quot;algeria&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-algeria.png&quot;,
        &quot;currency&quot;: &quot;Algerian Dinar (DZD)&quot;,
        &quot;capital&quot;: &quot;Algiers&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 48,
        &quot;country_name&quot;: &quot;Yemen&quot;,
        &quot;slug&quot;: &quot;yemen&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-yemen.png&quot;,
        &quot;currency&quot;: &quot;Yemeni Rial (YER)&quot;,
        &quot;capital&quot;: &quot;Sanaa&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 47,
        &quot;country_name&quot;: &quot;Vietnam / Viet Nam&quot;,
        &quot;slug&quot;: &quot;vietnam-viet-nam&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-vietnam.png&quot;,
        &quot;currency&quot;: &quot;Vietnamese Dong (VND)&quot;,
        &quot;capital&quot;: &quot;Hanoi&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 46,
        &quot;country_name&quot;: &quot;Uzbekistan&quot;,
        &quot;slug&quot;: &quot;uzbekistan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-uzbekistan.png&quot;,
        &quot;currency&quot;: &quot;Uzbekistani Som (UZS)&quot;,
        &quot;capital&quot;: &quot;Tashkent&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 45,
        &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
        &quot;slug&quot;: &quot;united-arab-emirates&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
        &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
        &quot;capital&quot;: &quot;Abu Dhabi&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 44,
        &quot;country_name&quot;: &quot;Tur kmenistan&quot;,
        &quot;slug&quot;: &quot;tur-kmenistan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-tur-kmenistan.png&quot;,
        &quot;currency&quot;: &quot;Turkmenistan Manat (TMT)&quot;,
        &quot;capital&quot;: &quot;Ashgabat&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 43,
        &quot;country_name&quot;: &quot;Turkey&quot;,
        &quot;slug&quot;: &quot;turkey&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-turkey.png&quot;,
        &quot;currency&quot;: &quot;Turkish Lira (TRY)&quot;,
        &quot;capital&quot;: &quot;Ankara&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 42,
        &quot;country_name&quot;: &quot;Timor-Leste/East Timor&quot;,
        &quot;slug&quot;: &quot;timor-lesteeast-timor&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-timor-leste.png&quot;,
        &quot;currency&quot;: &quot;United States Dollar (USD)&quot;,
        &quot;capital&quot;: &quot;Dili&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 41,
        &quot;country_name&quot;: &quot;Thailand&quot;,
        &quot;slug&quot;: &quot;thailand&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-thailand.png&quot;,
        &quot;currency&quot;: &quot;Thai Bhat (THB)&quot;,
        &quot;capital&quot;: &quot;Bangkok&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 40,
        &quot;country_name&quot;: &quot;Tajikistan&quot;,
        &quot;slug&quot;: &quot;tajikistan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-tajikistan.png&quot;,
        &quot;currency&quot;: &quot;Tajikistani Somoni (TJS)&quot;,
        &quot;capital&quot;: &quot;Dushanbe&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 39,
        &quot;country_name&quot;: &quot;Syria&quot;,
        &quot;slug&quot;: &quot;syria&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-syria.png&quot;,
        &quot;currency&quot;: &quot;Syrian Pound (SYP)&quot;,
        &quot;capital&quot;: &quot;Damascus&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 38,
        &quot;country_name&quot;: &quot;Sri Lanka&quot;,
        &quot;slug&quot;: &quot;sri-lanka&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-sri-lanka.png&quot;,
        &quot;currency&quot;: &quot;Sri Lankan Rupee (LKR)&quot;,
        &quot;capital&quot;: &quot;Sri Jayawardenapura Kotte&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 37,
        &quot;country_name&quot;: &quot;Singapore&quot;,
        &quot;slug&quot;: &quot;singapore&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-singapore.png&quot;,
        &quot;currency&quot;: &quot;Singapore Dollar (SGD)&quot;,
        &quot;capital&quot;: &quot;Singapore&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 36,
        &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
        &quot;slug&quot;: &quot;saudi-arabia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-saudi-arabia.png&quot;,
        &quot;currency&quot;: &quot;Saudi Riyal (SAR)&quot;,
        &quot;capital&quot;: &quot;Riyadh&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 35,
        &quot;country_name&quot;: &quot;Russia&quot;,
        &quot;slug&quot;: &quot;russia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-russia.png&quot;,
        &quot;currency&quot;: &quot;Russian Ruble (RUB)&quot;,
        &quot;capital&quot;: &quot;Moscow&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 34,
        &quot;country_name&quot;: &quot;Qatar&quot;,
        &quot;slug&quot;: &quot;qatar&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-qatar.png&quot;,
        &quot;currency&quot;: &quot;Qatari Riyal (QAR)&quot;,
        &quot;capital&quot;: &quot;Doha&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 33,
        &quot;country_name&quot;: &quot;Philippines&quot;,
        &quot;slug&quot;: &quot;philippines&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-philippines.png&quot;,
        &quot;currency&quot;: &quot;Philippine Peso (PHP)&quot;,
        &quot;capital&quot;: &quot;Manila&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 32,
        &quot;country_name&quot;: &quot;Palestine&quot;,
        &quot;slug&quot;: &quot;palestine&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-palestinian-territories.png&quot;,
        &quot;currency&quot;: &quot;Israeli new shekel (ILS)&quot;,
        &quot;capital&quot;: &quot;Jerusalem (claimed)&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 31,
        &quot;country_name&quot;: &quot;Pakistan&quot;,
        &quot;slug&quot;: &quot;pakistan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-pakistan.png&quot;,
        &quot;currency&quot;: &quot;Pakistan Rupee (PKR)&quot;,
        &quot;capital&quot;: &quot;Islamabad&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 30,
        &quot;country_name&quot;: &quot;Oman&quot;,
        &quot;slug&quot;: &quot;oman&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-oman.png&quot;,
        &quot;currency&quot;: &quot;Omani Rial (OMR)&quot;,
        &quot;capital&quot;: &quot;Muscat&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 29,
        &quot;country_name&quot;: &quot;Nepal&quot;,
        &quot;slug&quot;: &quot;nepal&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-nepal.png&quot;,
        &quot;currency&quot;: &quot;Nepalese Rupee (Rs)&quot;,
        &quot;capital&quot;: &quot;Kathmandu&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 28,
        &quot;country_name&quot;: &quot;Myanmar&quot;,
        &quot;slug&quot;: &quot;myanmar&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-myanmar-burma.png&quot;,
        &quot;currency&quot;: &quot;Myanmar Kyat (MMK)&quot;,
        &quot;capital&quot;: &quot;Nay Pyi Taw&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 27,
        &quot;country_name&quot;: &quot;Mongolia&quot;,
        &quot;slug&quot;: &quot;mongolia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-mongolia.png&quot;,
        &quot;currency&quot;: &quot;Mongolian Togrog or Turgik (MNT)&quot;,
        &quot;capital&quot;: &quot;Ulaanbaatar&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 26,
        &quot;country_name&quot;: &quot;Maldives&quot;,
        &quot;slug&quot;: &quot;maldives&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-maldives.png&quot;,
        &quot;currency&quot;: &quot;Maldivian Rufiyaa (MVR)&quot;,
        &quot;capital&quot;: &quot;Male / Male&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 25,
        &quot;country_name&quot;: &quot;Malaysia&quot;,
        &quot;slug&quot;: &quot;malaysia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-malaysia.png&quot;,
        &quot;currency&quot;: &quot;Malaysian Ringgit (MYR)&quot;,
        &quot;capital&quot;: &quot;Kuala Lumpur&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 24,
        &quot;country_name&quot;: &quot;Lebanon Lebanese Republic&quot;,
        &quot;slug&quot;: &quot;lebanon-lebanese-republic&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-lebanon.png&quot;,
        &quot;currency&quot;: &quot;Lebanese Pound (LBP)&quot;,
        &quot;capital&quot;: &quot;Beirut&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 23,
        &quot;country_name&quot;: &quot;Laos&quot;,
        &quot;slug&quot;: &quot;laos&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-laos.png&quot;,
        &quot;currency&quot;: &quot;Laotian Kip (LAK)&quot;,
        &quot;capital&quot;: &quot;Vientiane&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 22,
        &quot;country_name&quot;: &quot;Kyrgyzstan&quot;,
        &quot;slug&quot;: &quot;kyrgyzstan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kyrgyzstan.png&quot;,
        &quot;currency&quot;: &quot;Kyrgyzstani Som (KGS)&quot;,
        &quot;capital&quot;: &quot;Bishkek&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 21,
        &quot;country_name&quot;: &quot;Kuwait&quot;,
        &quot;slug&quot;: &quot;kuwait&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kuwait.png&quot;,
        &quot;currency&quot;: &quot;Kuwaiti Dinar (KWD)&quot;,
        &quot;capital&quot;: &quot;Kuwait City&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 20,
        &quot;country_name&quot;: &quot;Korea&quot;,
        &quot;slug&quot;: &quot;korea&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-south-korea.png&quot;,
        &quot;currency&quot;: &quot;South Korean Won (KRW)&quot;,
        &quot;capital&quot;: &quot;Seoul&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 19,
        &quot;country_name&quot;: &quot;Kazakhstan&quot;,
        &quot;slug&quot;: &quot;kazakhstan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-kazakhstan.png&quot;,
        &quot;currency&quot;: &quot;Kazakhstani Tenge (KZT)&quot;,
        &quot;capital&quot;: &quot;Astana Kazakh&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 18,
        &quot;country_name&quot;: &quot;Jordan&quot;,
        &quot;slug&quot;: &quot;jordan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-jordan.png&quot;,
        &quot;currency&quot;: &quot;Jordanian Dinar (JOD)&quot;,
        &quot;capital&quot;: &quot;Amman&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 17,
        &quot;country_name&quot;: &quot;Japan&quot;,
        &quot;slug&quot;: &quot;japan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-japan.png&quot;,
        &quot;currency&quot;: &quot;Yen (JPY)&quot;,
        &quot;capital&quot;: &quot;Tokyo&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 16,
        &quot;country_name&quot;: &quot;Israel&quot;,
        &quot;slug&quot;: &quot;israel&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-israel.png&quot;,
        &quot;currency&quot;: &quot;Israeli Shekel (ILS)&quot;,
        &quot;capital&quot;: &quot;Jerusalem&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 15,
        &quot;country_name&quot;: &quot;Iraq&quot;,
        &quot;slug&quot;: &quot;iraq&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-iraq.png&quot;,
        &quot;currency&quot;: &quot;Iraqi Dinar (IQD)&quot;,
        &quot;capital&quot;: &quot;Baghdad&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 14,
        &quot;country_name&quot;: &quot;Iran&quot;,
        &quot;slug&quot;: &quot;iran&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-iran.png&quot;,
        &quot;currency&quot;: &quot;Iranian Rial (IRR)&quot;,
        &quot;capital&quot;: &quot;Tehran&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 13,
        &quot;country_name&quot;: &quot;Indonesia&quot;,
        &quot;slug&quot;: &quot;indonesia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-indonesia.png&quot;,
        &quot;currency&quot;: &quot;Indonesian Rupiah (IDR)&quot;,
        &quot;capital&quot;: &quot;Jakarta&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 12,
        &quot;country_name&quot;: &quot;India&quot;,
        &quot;slug&quot;: &quot;india&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-india.png&quot;,
        &quot;currency&quot;: &quot;Indian Rupee (INR)&quot;,
        &quot;capital&quot;: &quot;New Delhi&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 11,
        &quot;country_name&quot;: &quot;Georgia&quot;,
        &quot;slug&quot;: &quot;georgia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-georgia.png&quot;,
        &quot;currency&quot;: &quot;Georgian Lari (GEL)&quot;,
        &quot;capital&quot;: &quot;Tbilisi&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 10,
        &quot;country_name&quot;: &quot;Cyprus&quot;,
        &quot;slug&quot;: &quot;cyprus&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cyprus.png&quot;,
        &quot;currency&quot;: &quot;Euros (EUR)&quot;,
        &quot;capital&quot;: &quot;Nicosia&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 9,
        &quot;country_name&quot;: &quot;China&quot;,
        &quot;slug&quot;: &quot;china&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-china.png&quot;,
        &quot;currency&quot;: &quot;Chinese Yuan (CNY)&quot;,
        &quot;capital&quot;: &quot;Beijing&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 8,
        &quot;country_name&quot;: &quot;Cambodia&quot;,
        &quot;slug&quot;: &quot;cambodia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-cambodia.png&quot;,
        &quot;currency&quot;: &quot;Cambodian Riel (KHR)&quot;,
        &quot;capital&quot;: &quot;Phnom Penh&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 7,
        &quot;country_name&quot;: &quot;Brunei&quot;,
        &quot;slug&quot;: &quot;brunei&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-brunei.png&quot;,
        &quot;currency&quot;: &quot;Brunei dollar (BND)&quot;,
        &quot;capital&quot;: &quot;Bandar Seri Begawan&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 6,
        &quot;country_name&quot;: &quot;Bhutan&quot;,
        &quot;slug&quot;: &quot;bhutan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bhutan.png&quot;,
        &quot;currency&quot;: &quot;Ngultrum (BTN)&quot;,
        &quot;capital&quot;: &quot;Thimphu&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 5,
        &quot;country_name&quot;: &quot;Bangladesh&quot;,
        &quot;slug&quot;: &quot;bangladesh&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bangladesh.png&quot;,
        &quot;currency&quot;: &quot;Bangladeshi Taka (BDT)&quot;,
        &quot;capital&quot;: &quot;Dhaka&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 4,
        &quot;country_name&quot;: &quot;Bahrain&quot;,
        &quot;slug&quot;: &quot;bahrain&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-bahrain.png&quot;,
        &quot;currency&quot;: &quot;Bahraini Dinar (BHD)&quot;,
        &quot;capital&quot;: &quot;Manama&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 3,
        &quot;country_name&quot;: &quot;Azerbaijan&quot;,
        &quot;slug&quot;: &quot;azerbaijan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-azerbaijan.png&quot;,
        &quot;currency&quot;: &quot;Azerbaijani Manat (AZN)&quot;,
        &quot;capital&quot;: &quot;Baku&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 2,
        &quot;country_name&quot;: &quot;Armenia&quot;,
        &quot;slug&quot;: &quot;armenia&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-armenia.png&quot;,
        &quot;currency&quot;: &quot;Armenian Dram (AMD)&quot;,
        &quot;capital&quot;: &quot;Yerevan&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    {
        &quot;id&quot;: 1,
        &quot;country_name&quot;: &quot;Afghanistan&quot;,
        &quot;slug&quot;: &quot;afghanistan&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-afghanistan.png&quot;,
        &quot;currency&quot;: &quot;Afghani (AFN)&quot;,
        &quot;capital&quot;: &quot;Kabul&quot;,
        &quot;status&quot;: &quot;deactive&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    }
]</code>
 </pre>
    </span>
<span id="execution-results-GETapi-countries-getallcountriesforadmin" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-countries-getallcountriesforadmin"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-countries-getallcountriesforadmin"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-countries-getallcountriesforadmin" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-countries-getallcountriesforadmin">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-countries-getallcountriesforadmin" data-method="GET"
      data-path="api/countries/getallcountriesforadmin"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-countries-getallcountriesforadmin', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-countries-getallcountriesforadmin"
                    onclick="tryItOut('GETapi-countries-getallcountriesforadmin');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-countries-getallcountriesforadmin"
                    onclick="cancelTryOut('GETapi-countries-getallcountriesforadmin');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-countries-getallcountriesforadmin"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/countries/getallcountriesforadmin</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-countries-getallcountriesforadmin"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-countries-getallcountriesforadmin"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-GETapi-countries-getcountriesselectedids">GET api/countries/getcountriesselectedids</h2>

<p>
</p>



<span id="example-requests-GETapi-countries-getcountriesselectedids">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/countries/getcountriesselectedids" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/getcountriesselectedids"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-countries-getcountriesselectedids">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1996
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;status&quot;: true,
    &quot;data&quot;: [
        {
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;cities&quot;: [
                {
                    &quot;id&quot;: 462,
                    &quot;country_id&quot;: 45,
                    &quot;city_name&quot;: &quot;Abu Dhabi Island and Internal Islands City&quot;,
                    &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 464,
                    &quot;country_id&quot;: 45,
                    &quot;city_name&quot;: &quot;Al Ain City&quot;,
                    &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 474,
                    &quot;country_id&quot;: 45,
                    &quot;city_name&quot;: &quot;Ajman&quot;,
                    &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 478,
                    &quot;country_id&quot;: 45,
                    &quot;city_name&quot;: &quot;Dubai&quot;,
                    &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 479,
                    &quot;country_id&quot;: 45,
                    &quot;city_name&quot;: &quot;Al Fujairah City&quot;,
                    &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                }
            ]
        },
        {
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;cities&quot;: [
                {
                    &quot;id&quot;: 156,
                    &quot;country_id&quot;: 36,
                    &quot;city_name&quot;: &quot;Yanbu&quot;,
                    &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
                },
                {
                    &quot;id&quot;: 190,
                    &quot;country_id&quot;: 36,
                    &quot;city_name&quot;: &quot;Dammam&quot;,
                    &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
                },
                {
                    &quot;id&quot;: 320,
                    &quot;country_id&quot;: 36,
                    &quot;city_name&quot;: &quot;Al Madiq Makkah&quot;,
                    &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 394,
                    &quot;country_id&quot;: 36,
                    &quot;city_name&quot;: &quot;Jeddah&quot;,
                    &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 454,
                    &quot;country_id&quot;: 36,
                    &quot;city_name&quot;: &quot;Riyadh&quot;,
                    &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
                }
            ]
        },
        {
            &quot;country_name&quot;: &quot;Qatar&quot;,
            &quot;cities&quot;: [
                {
                    &quot;id&quot;: 73,
                    &quot;country_id&quot;: 34,
                    &quot;city_name&quot;: &quot;Doha&quot;,
                    &quot;country_name&quot;: &quot;Qatar&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
                }
            ]
        },
        {
            &quot;country_name&quot;: &quot;Oman&quot;,
            &quot;cities&quot;: [
                {
                    &quot;id&quot;: 48,
                    &quot;country_id&quot;: 30,
                    &quot;city_name&quot;: &quot;Sohar&quot;,
                    &quot;country_name&quot;: &quot;Oman&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
                },
                {
                    &quot;id&quot;: 61,
                    &quot;country_id&quot;: 30,
                    &quot;city_name&quot;: &quot;Muscat&quot;,
                    &quot;country_name&quot;: &quot;Oman&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
                },
                {
                    &quot;id&quot;: 153567,
                    &quot;country_id&quot;: 30,
                    &quot;city_name&quot;: &quot;Zufar&quot;,
                    &quot;country_name&quot;: &quot;Oman&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-11-06T08:26:06.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-11-06T08:26:06.000000Z&quot;
                },
                {
                    &quot;id&quot;: 153568,
                    &quot;country_id&quot;: 30,
                    &quot;city_name&quot;: &quot;Salalah&quot;,
                    &quot;country_name&quot;: &quot;Oman&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-11-06T08:26:52.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-11-06T08:26:52.000000Z&quot;
                }
            ]
        },
        {
            &quot;country_name&quot;: &quot;Kuwait&quot;,
            &quot;cities&quot;: [
                {
                    &quot;id&quot;: 153566,
                    &quot;country_id&quot;: 21,
                    &quot;city_name&quot;: &quot;Ahmadi&quot;,
                    &quot;country_name&quot;: &quot;Kuwait&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-11-06T08:24:56.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-11-06T08:24:56.000000Z&quot;
                }
            ]
        },
        {
            &quot;country_name&quot;: &quot;Bahrain&quot;,
            &quot;cities&quot;: [
                {
                    &quot;id&quot;: 2,
                    &quot;country_id&quot;: 4,
                    &quot;city_name&quot;: &quot;Manama&quot;,
                    &quot;country_name&quot;: &quot;Bahrain&quot;,
                    &quot;status&quot;: &quot;active&quot;,
                    &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
                    &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
                }
            ]
        }
    ],
    &quot;other_location_data&quot;: [
        {
            &quot;id&quot;: 179,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Muţayrifī&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 316,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Khulasah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 356,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;As Suwadah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 28,
            &quot;country_id&quot;: 21,
            &quot;city_name&quot;: &quot;Ḩawallī&quot;,
            &quot;country_name&quot;: &quot;Kuwait&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 415,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Raqiyah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 134,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Harthiyah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 199,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Qaisumah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 298,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Ghassalah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 302,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Hamimah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 476,
            &quot;country_id&quot;: 45,
            &quot;city_name&quot;: &quot;Manama&quot;,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 153569,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Jubail&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-06T08:28:27.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-06T08:28:27.000000Z&quot;
        },
        {
            &quot;id&quot;: 385,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Fayd&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 169,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Abqaiq&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 313,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Khamrah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 34,
            &quot;country_id&quot;: 21,
            &quot;city_name&quot;: &quot;Şabāḩ as Sālim&quot;,
            &quot;country_name&quot;: &quot;Kuwait&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 318,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Kura`&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 481,
            &quot;country_id&quot;: 45,
            &quot;city_name&quot;: &quot;Dibba Al Fujairah Municipality&quot;,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 225,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Jarādīyah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 425,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Turabah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 457,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Tumayr&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 198,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Nariyah&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 73,
            &quot;country_id&quot;: 34,
            &quot;city_name&quot;: &quot;Doha&quot;,
            &quot;country_name&quot;: &quot;Qatar&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 395,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Julayyil&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 152,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Suq Suwayq&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 242,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Al Mayasam&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 427,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Wadi al Jalil&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 500,
            &quot;country_id&quot;: 45,
            &quot;city_name&quot;: &quot;Umm Al Quwain City&quot;,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 388,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;Haddat ash Sham&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:06.000000Z&quot;
        },
        {
            &quot;id&quot;: 33,
            &quot;country_id&quot;: 21,
            &quot;city_name&quot;: &quot;Al-Masayel&quot;,
            &quot;country_name&quot;: &quot;Kuwait&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        },
        {
            &quot;id&quot;: 122,
            &quot;country_id&quot;: 36,
            &quot;city_name&quot;: &quot;As Sadayir&quot;,
            &quot;country_name&quot;: &quot;Saudi Arabia&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-09-20T19:54:05.000000Z&quot;
        }
    ],
    &quot;message&quot;: &quot;countries listing successfully!&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-countries-getcountriesselectedids" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-countries-getcountriesselectedids"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-countries-getcountriesselectedids"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-countries-getcountriesselectedids" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-countries-getcountriesselectedids">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-countries-getcountriesselectedids" data-method="GET"
      data-path="api/countries/getcountriesselectedids"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-countries-getcountriesselectedids', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-countries-getcountriesselectedids"
                    onclick="tryItOut('GETapi-countries-getcountriesselectedids');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-countries-getcountriesselectedids"
                    onclick="cancelTryOut('GETapi-countries-getcountriesselectedids');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-countries-getcountriesselectedids"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/countries/getcountriesselectedids</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-countries-getcountriesselectedids"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-countries-getcountriesselectedids"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-PUTapi-countries-countrystatus">PUT api/countries/countrystatus</h2>

<p>
</p>



<span id="example-requests-PUTapi-countries-countrystatus">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request PUT \
    "https://api.thetalentpoint.com/api/countries/countrystatus" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/countrystatus"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-PUTapi-countries-countrystatus">
</span>
<span id="execution-results-PUTapi-countries-countrystatus" hidden>
    <blockquote>Received response<span
                id="execution-response-status-PUTapi-countries-countrystatus"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-PUTapi-countries-countrystatus"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-PUTapi-countries-countrystatus" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-PUTapi-countries-countrystatus">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-PUTapi-countries-countrystatus" data-method="PUT"
      data-path="api/countries/countrystatus"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('PUTapi-countries-countrystatus', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-PUTapi-countries-countrystatus"
                    onclick="tryItOut('PUTapi-countries-countrystatus');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-PUTapi-countries-countrystatus"
                    onclick="cancelTryOut('PUTapi-countries-countrystatus');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-PUTapi-countries-countrystatus"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-darkblue">PUT</small>
            <b><code>api/countries/countrystatus</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="PUTapi-countries-countrystatus"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="PUTapi-countries-countrystatus"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="countries-GETapi-countries-single-country-by-name">GET api/countries/single-country-by-name</h2>

<p>
</p>



<span id="example-requests-GETapi-countries-single-country-by-name">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/countries/single-country-by-name" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/countries/single-country-by-name"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-countries-single-country-by-name">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1995
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;id&quot;: 4,
    &quot;country_name&quot;: &quot;Bahrain&quot;,
    &quot;slug&quot;: &quot;bahrain&quot;,
    &quot;flag&quot;: &quot;twemoji_flag-bahrain.png&quot;,
    &quot;currency&quot;: &quot;Bahraini Dinar (BHD)&quot;,
    &quot;capital&quot;: &quot;Manama&quot;,
    &quot;status&quot;: &quot;active&quot;,
    &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
    &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-countries-single-country-by-name" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-countries-single-country-by-name"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-countries-single-country-by-name"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-countries-single-country-by-name" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-countries-single-country-by-name">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-countries-single-country-by-name" data-method="GET"
      data-path="api/countries/single-country-by-name"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-countries-single-country-by-name', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-countries-single-country-by-name"
                    onclick="tryItOut('GETapi-countries-single-country-by-name');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-countries-single-country-by-name"
                    onclick="cancelTryOut('GETapi-countries-single-country-by-name');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-countries-single-country-by-name"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/countries/single-country-by-name</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-countries-single-country-by-name"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-countries-single-country-by-name"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                <h1 id="endpoints">Endpoints</h1>

    

                                <h2 id="endpoints-GETapi-file-management-files">Display a list of files store by the authenticated user</h2>

<p>
</p>

<p>If no user is authenticated will show and empty list</p>

<span id="example-requests-GETapi-file-management-files">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/file-management/files" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/file-management/files"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-file-management-files">
            <blockquote>
            <p>Example response (401):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Unauthenticated.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-file-management-files" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-file-management-files"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-file-management-files"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-file-management-files" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-file-management-files">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-file-management-files" data-method="GET"
      data-path="api/file-management/files"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-file-management-files', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-file-management-files"
                    onclick="tryItOut('GETapi-file-management-files');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-file-management-files"
                    onclick="cancelTryOut('GETapi-file-management-files');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-file-management-files"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/file-management/files</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-file-management-files"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-file-management-files"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="endpoints-POSTapi-file-management-files">Save a new file</h2>

<p>
</p>



<span id="example-requests-POSTapi-file-management-files">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://api.thetalentpoint.com/api/file-management/files" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"file\": \"non\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/file-management/files"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "file": "non"
};

fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-POSTapi-file-management-files">
</span>
<span id="execution-results-POSTapi-file-management-files" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-file-management-files"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-file-management-files"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-file-management-files" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-file-management-files">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-POSTapi-file-management-files" data-method="POST"
      data-path="api/file-management/files"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-file-management-files', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-POSTapi-file-management-files"
                    onclick="tryItOut('POSTapi-file-management-files');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-POSTapi-file-management-files"
                    onclick="cancelTryOut('POSTapi-file-management-files');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-POSTapi-file-management-files"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/file-management/files</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="POSTapi-file-management-files"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="POSTapi-file-management-files"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>file</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="file"                data-endpoint="POSTapi-file-management-files"
               value="non"
               data-component="body">
    <br>
<p>Example: <code>non</code></p>
        </div>
        </form>

                    <h2 id="endpoints-GETapi-file-management-files--id-">Display the specified file.</h2>

<p>
</p>



<span id="example-requests-GETapi-file-management-files--id-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/file-management/files/est" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/file-management/files/est"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-file-management-files--id-">
            <blockquote>
            <p>Example response (401):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;message&quot;: &quot;Unauthenticated.&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-file-management-files--id-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-file-management-files--id-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-file-management-files--id-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-file-management-files--id-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-file-management-files--id-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-file-management-files--id-" data-method="GET"
      data-path="api/file-management/files/{id}"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-file-management-files--id-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-file-management-files--id-"
                    onclick="tryItOut('GETapi-file-management-files--id-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-file-management-files--id-"
                    onclick="cancelTryOut('GETapi-file-management-files--id-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-file-management-files--id-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/file-management/files/{id}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-file-management-files--id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-file-management-files--id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="id"                data-endpoint="GETapi-file-management-files--id-"
               value="est"
               data-component="url">
    <br>
<p>The ID of the file. Example: <code>est</code></p>
            </div>
                    </form>

                    <h2 id="endpoints-PUTapi-file-management-files--uuid-">Update the specified resource in storage.</h2>

<p>
</p>



<span id="example-requests-PUTapi-file-management-files--uuid-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request PUT \
    "https://api.thetalentpoint.com/api/file-management/files/024dccc7-a776-4656-9f16-1ac31886f78d" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/file-management/files/024dccc7-a776-4656-9f16-1ac31886f78d"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "PUT",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-PUTapi-file-management-files--uuid-">
</span>
<span id="execution-results-PUTapi-file-management-files--uuid-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-PUTapi-file-management-files--uuid-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-PUTapi-file-management-files--uuid-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-PUTapi-file-management-files--uuid-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-PUTapi-file-management-files--uuid-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-PUTapi-file-management-files--uuid-" data-method="PUT"
      data-path="api/file-management/files/{uuid}"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('PUTapi-file-management-files--uuid-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-PUTapi-file-management-files--uuid-"
                    onclick="tryItOut('PUTapi-file-management-files--uuid-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-PUTapi-file-management-files--uuid-"
                    onclick="cancelTryOut('PUTapi-file-management-files--uuid-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-PUTapi-file-management-files--uuid-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-darkblue">PUT</small>
            <b><code>api/file-management/files/{uuid}</code></b>
        </p>
            <p>
            <small class="badge badge-purple">PATCH</small>
            <b><code>api/file-management/files/{uuid}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="PUTapi-file-management-files--uuid-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="PUTapi-file-management-files--uuid-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>uuid</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="uuid"                data-endpoint="PUTapi-file-management-files--uuid-"
               value="024dccc7-a776-4656-9f16-1ac31886f78d"
               data-component="url">
    <br>
<p>Example: <code>024dccc7-a776-4656-9f16-1ac31886f78d</code></p>
            </div>
                    </form>

                    <h2 id="endpoints-DELETEapi-file-management-files--id-">Remove the specified resource from storage.</h2>

<p>
</p>



<span id="example-requests-DELETEapi-file-management-files--id-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request DELETE \
    "https://api.thetalentpoint.com/api/file-management/files/qui" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/file-management/files/qui"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-DELETEapi-file-management-files--id-">
</span>
<span id="execution-results-DELETEapi-file-management-files--id-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-DELETEapi-file-management-files--id-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-DELETEapi-file-management-files--id-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-DELETEapi-file-management-files--id-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-DELETEapi-file-management-files--id-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-DELETEapi-file-management-files--id-" data-method="DELETE"
      data-path="api/file-management/files/{id}"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('DELETEapi-file-management-files--id-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-DELETEapi-file-management-files--id-"
                    onclick="tryItOut('DELETEapi-file-management-files--id-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-DELETEapi-file-management-files--id-"
                    onclick="cancelTryOut('DELETEapi-file-management-files--id-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-DELETEapi-file-management-files--id-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-red">DELETE</small>
            <b><code>api/file-management/files/{id}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="DELETEapi-file-management-files--id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="DELETEapi-file-management-files--id-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="id"                data-endpoint="DELETEapi-file-management-files--id-"
               value="qui"
               data-component="url">
    <br>
<p>The ID of the file. Example: <code>qui</code></p>
            </div>
                    </form>

                <h1 id="industries">Industries</h1>

    

                                <h2 id="industries-GETapi-industry-getallindustries">GET api/industry/getallindustries</h2>

<p>
</p>



<span id="example-requests-GETapi-industry-getallindustries">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/industry/getallindustries" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/industry/getallindustries"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-industry-getallindustries">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1994
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;success&quot;: true,
    &quot;data&quot;: [
        {
            &quot;id&quot;: 24,
            &quot;name&quot;: &quot;AdTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 12,
            &quot;name&quot;: &quot;Agile Consulting&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 34,
            &quot;name&quot;: &quot;AgriTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 13,
            &quot;name&quot;: &quot;AI Robotics&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 39,
            &quot;name&quot;: &quot;AI Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 44,
            &quot;name&quot;: &quot;Bioinformatics&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 2,
            &quot;name&quot;: &quot;BioMed Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 22,
            &quot;name&quot;: &quot;BioTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 46,
            &quot;name&quot;: &quot;Blockchain Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 51,
            &quot;name&quot;: &quot;Building services industry&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T01:49:28.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T01:49:28.000000Z&quot;
        },
        {
            &quot;id&quot;: 56,
            &quot;name&quot;: &quot;Chemical Process&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:52:16.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:52:16.000000Z&quot;
        },
        {
            &quot;id&quot;: 7,
            &quot;name&quot;: &quot;Clean Energy&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 35,
            &quot;name&quot;: &quot;CleanTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 42,
            &quot;name&quot;: &quot;Cloud Computing&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 14,
            &quot;name&quot;: &quot;Cloud Services&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 57,
            &quot;name&quot;: &quot;Construction&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-16T01:02:16.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-16T01:02:16.000000Z&quot;
        },
        {
            &quot;id&quot;: 3,
            &quot;name&quot;: &quot;Creative Minds&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 15,
            &quot;name&quot;: &quot;Cyber Security&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 4,
            &quot;name&quot;: &quot;Data Analytics&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 20,
            &quot;name&quot;: &quot;Design Studio&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 8,
            &quot;name&quot;: &quot;Digital Marketing&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 28,
            &quot;name&quot;: &quot;E-commerce Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 25,
            &quot;name&quot;: &quot;EdTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 41,
            &quot;name&quot;: &quot;FashionTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 38,
            &quot;name&quot;: &quot;FinanceTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 23,
            &quot;name&quot;: &quot;FinTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 52,
            &quot;name&quot;: &quot;Food &amp; Beverage&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:10.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:10.000000Z&quot;
        },
        {
            &quot;id&quot;: 29,
            &quot;name&quot;: &quot;FoodTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 30,
            &quot;name&quot;: &quot;Gaming Industry&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 5,
            &quot;name&quot;: &quot;Global Finance&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 19,
            &quot;name&quot;: &quot;GreenTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 27,
            &quot;name&quot;: &quot;HealthCare Tech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 6,
            &quot;name&quot;: &quot;HealthTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 31,
            &quot;name&quot;: &quot;HR Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 37,
            &quot;name&quot;: &quot;InsurTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 33,
            &quot;name&quot;: &quot;IoT Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 21,
            &quot;name&quot;: &quot;Legal Tech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 55,
            &quot;name&quot;: &quot;Life Science&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:57.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:57.000000Z&quot;
        },
        {
            &quot;id&quot;: 10,
            &quot;name&quot;: &quot;MediaTech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 45,
            &quot;name&quot;: &quot;MedTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 49,
            &quot;name&quot;: &quot;NanoTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 54,
            &quot;name&quot;: &quot;Oil &amp; Gas&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:44.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:44.000000Z&quot;
        },
        {
            &quot;id&quot;: 32,
            &quot;name&quot;: &quot;PharmaTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 53,
            &quot;name&quot;: &quot;Power&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:26.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:26.000000Z&quot;
        },
        {
            &quot;id&quot;: 11,
            &quot;name&quot;: &quot;Precision Engineering&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 18,
            &quot;name&quot;: &quot;Renewable Energy&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 47,
            &quot;name&quot;: &quot;Robotics Industry&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 50,
            &quot;name&quot;: &quot;Smart Cities&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 16,
            &quot;name&quot;: &quot;Smart Mobility&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 26,
            &quot;name&quot;: &quot;Social Media&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 36,
            &quot;name&quot;: &quot;Software Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 17,
            &quot;name&quot;: &quot;SpaceTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 1,
            &quot;name&quot;: &quot;Tech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 40,
            &quot;name&quot;: &quot;Telecom Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 43,
            &quot;name&quot;: &quot;TravelTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 48,
            &quot;name&quot;: &quot;Virtual Events&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 9,
            &quot;name&quot;: &quot;Virtual Reality&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-industry-getallindustries" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-industry-getallindustries"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-industry-getallindustries"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-industry-getallindustries" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-industry-getallindustries">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-industry-getallindustries" data-method="GET"
      data-path="api/industry/getallindustries"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-industry-getallindustries', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-industry-getallindustries"
                    onclick="tryItOut('GETapi-industry-getallindustries');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-industry-getallindustries"
                    onclick="cancelTryOut('GETapi-industry-getallindustries');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-industry-getallindustries"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/industry/getallindustries</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-industry-getallindustries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-industry-getallindustries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="industries-GETapi-industry-getallindustriesforadmin">GET api/industry/getallindustriesforadmin</h2>

<p>
</p>



<span id="example-requests-GETapi-industry-getallindustriesforadmin">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/industry/getallindustriesforadmin" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/industry/getallindustriesforadmin"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-industry-getallindustriesforadmin">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1993
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;success&quot;: true,
    &quot;data&quot;: [
        {
            &quot;id&quot;: 57,
            &quot;name&quot;: &quot;Construction&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-16T01:02:16.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-16T01:02:16.000000Z&quot;
        },
        {
            &quot;id&quot;: 56,
            &quot;name&quot;: &quot;Chemical Process&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:52:16.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:52:16.000000Z&quot;
        },
        {
            &quot;id&quot;: 55,
            &quot;name&quot;: &quot;Life Science&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:57.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:57.000000Z&quot;
        },
        {
            &quot;id&quot;: 54,
            &quot;name&quot;: &quot;Oil &amp; Gas&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:44.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:44.000000Z&quot;
        },
        {
            &quot;id&quot;: 53,
            &quot;name&quot;: &quot;Power&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:26.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:26.000000Z&quot;
        },
        {
            &quot;id&quot;: 52,
            &quot;name&quot;: &quot;Food &amp; Beverage&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T23:51:10.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T23:51:10.000000Z&quot;
        },
        {
            &quot;id&quot;: 51,
            &quot;name&quot;: &quot;Building services industry&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-11-13T01:49:28.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-11-13T01:49:28.000000Z&quot;
        },
        {
            &quot;id&quot;: 50,
            &quot;name&quot;: &quot;Smart Cities&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 49,
            &quot;name&quot;: &quot;NanoTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 48,
            &quot;name&quot;: &quot;Virtual Events&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 47,
            &quot;name&quot;: &quot;Robotics Industry&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 46,
            &quot;name&quot;: &quot;Blockchain Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 45,
            &quot;name&quot;: &quot;MedTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 44,
            &quot;name&quot;: &quot;Bioinformatics&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 43,
            &quot;name&quot;: &quot;TravelTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 42,
            &quot;name&quot;: &quot;Cloud Computing&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 41,
            &quot;name&quot;: &quot;FashionTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 40,
            &quot;name&quot;: &quot;Telecom Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 39,
            &quot;name&quot;: &quot;AI Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 38,
            &quot;name&quot;: &quot;FinanceTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 37,
            &quot;name&quot;: &quot;InsurTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 36,
            &quot;name&quot;: &quot;Software Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 35,
            &quot;name&quot;: &quot;CleanTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 34,
            &quot;name&quot;: &quot;AgriTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 33,
            &quot;name&quot;: &quot;IoT Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 32,
            &quot;name&quot;: &quot;PharmaTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 31,
            &quot;name&quot;: &quot;HR Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 30,
            &quot;name&quot;: &quot;Gaming Industry&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 29,
            &quot;name&quot;: &quot;FoodTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 28,
            &quot;name&quot;: &quot;E-commerce Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 27,
            &quot;name&quot;: &quot;HealthCare Tech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 26,
            &quot;name&quot;: &quot;Social Media&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 25,
            &quot;name&quot;: &quot;EdTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 24,
            &quot;name&quot;: &quot;AdTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 23,
            &quot;name&quot;: &quot;FinTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 22,
            &quot;name&quot;: &quot;BioTech Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 21,
            &quot;name&quot;: &quot;Legal Tech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 20,
            &quot;name&quot;: &quot;Design Studio&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 19,
            &quot;name&quot;: &quot;GreenTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 18,
            &quot;name&quot;: &quot;Renewable Energy&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 17,
            &quot;name&quot;: &quot;SpaceTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 16,
            &quot;name&quot;: &quot;Smart Mobility&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 15,
            &quot;name&quot;: &quot;Cyber Security&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 14,
            &quot;name&quot;: &quot;Cloud Services&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 13,
            &quot;name&quot;: &quot;AI Robotics&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 12,
            &quot;name&quot;: &quot;Agile Consulting&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 11,
            &quot;name&quot;: &quot;Precision Engineering&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 10,
            &quot;name&quot;: &quot;MediaTech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 9,
            &quot;name&quot;: &quot;Virtual Reality&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 8,
            &quot;name&quot;: &quot;Digital Marketing&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 7,
            &quot;name&quot;: &quot;Clean Energy&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 6,
            &quot;name&quot;: &quot;HealthTech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 5,
            &quot;name&quot;: &quot;Global Finance&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 4,
            &quot;name&quot;: &quot;Data Analytics&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 3,
            &quot;name&quot;: &quot;Creative Minds&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 2,
            &quot;name&quot;: &quot;BioMed Innovations&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        {
            &quot;id&quot;: 1,
            &quot;name&quot;: &quot;Tech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        }
    ]
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-industry-getallindustriesforadmin" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-industry-getallindustriesforadmin"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-industry-getallindustriesforadmin"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-industry-getallindustriesforadmin" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-industry-getallindustriesforadmin">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-industry-getallindustriesforadmin" data-method="GET"
      data-path="api/industry/getallindustriesforadmin"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-industry-getallindustriesforadmin', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-industry-getallindustriesforadmin"
                    onclick="tryItOut('GETapi-industry-getallindustriesforadmin');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-industry-getallindustriesforadmin"
                    onclick="cancelTryOut('GETapi-industry-getallindustriesforadmin');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-industry-getallindustriesforadmin"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/industry/getallindustriesforadmin</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-industry-getallindustriesforadmin"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-industry-getallindustriesforadmin"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="industries-POSTapi-industry-editandsaveindustries">POST api/industry/editandsaveindustries</h2>

<p>
</p>



<span id="example-requests-POSTapi-industry-editandsaveindustries">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "https://api.thetalentpoint.com/api/industry/editandsaveindustries" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/industry/editandsaveindustries"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "POST",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-POSTapi-industry-editandsaveindustries">
</span>
<span id="execution-results-POSTapi-industry-editandsaveindustries" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-industry-editandsaveindustries"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-industry-editandsaveindustries"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-industry-editandsaveindustries" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-industry-editandsaveindustries">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-POSTapi-industry-editandsaveindustries" data-method="POST"
      data-path="api/industry/editandsaveindustries"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-industry-editandsaveindustries', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-POSTapi-industry-editandsaveindustries"
                    onclick="tryItOut('POSTapi-industry-editandsaveindustries');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-POSTapi-industry-editandsaveindustries"
                    onclick="cancelTryOut('POSTapi-industry-editandsaveindustries');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-POSTapi-industry-editandsaveindustries"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/industry/editandsaveindustries</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="POSTapi-industry-editandsaveindustries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="POSTapi-industry-editandsaveindustries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="industries-DELETEapi-industry-industries">DELETE api/industry/industries</h2>

<p>
</p>



<span id="example-requests-DELETEapi-industry-industries">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request DELETE \
    "https://api.thetalentpoint.com/api/industry/industries" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/industry/industries"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-DELETEapi-industry-industries">
</span>
<span id="execution-results-DELETEapi-industry-industries" hidden>
    <blockquote>Received response<span
                id="execution-response-status-DELETEapi-industry-industries"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-DELETEapi-industry-industries"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-DELETEapi-industry-industries" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-DELETEapi-industry-industries">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-DELETEapi-industry-industries" data-method="DELETE"
      data-path="api/industry/industries"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('DELETEapi-industry-industries', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-DELETEapi-industry-industries"
                    onclick="tryItOut('DELETEapi-industry-industries');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-DELETEapi-industry-industries"
                    onclick="cancelTryOut('DELETEapi-industry-industries');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-DELETEapi-industry-industries"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-red">DELETE</small>
            <b><code>api/industry/industries</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="DELETEapi-industry-industries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="DELETEapi-industry-industries"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                <h1 id="salaries">Salaries</h1>

    

                                <h2 id="salaries-GETapi-salaries-insights">Get salary insights</h2>

<p>
</p>

<p>Return all information by industry and country</p>

<span id="example-requests-GETapi-salaries-insights">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/salaries/insights?industry_id=31&amp;country_id=45" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/salaries/insights"
);

const params = {
    "industry_id": "31",
    "country_id": "45",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-salaries-insights">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1992
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;industry&quot;: {
        &quot;id&quot;: 31,
        &quot;name&quot;: &quot;HR Solutions&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
    },
    &quot;country&quot;: {
        &quot;id&quot;: 45,
        &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
        &quot;slug&quot;: &quot;united-arab-emirates&quot;,
        &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
        &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
        &quot;capital&quot;: &quot;Abu Dhabi&quot;,
        &quot;status&quot;: &quot;active&quot;,
        &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
        &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
    },
    &quot;salary&quot;: {
        &quot;average&quot;: 6437.5,
        &quot;min&quot;: &quot;2000&quot;,
        &quot;max&quot;: &quot;8000&quot;,
        &quot;total&quot;: 8
    },
    &quot;based_experience&quot;: [
        {
            &quot;average&quot;: 2000,
            &quot;count&quot;: 1,
            &quot;time&quot;: 2
        },
        {
            &quot;average&quot;: 7000,
            &quot;count&quot;: 4,
            &quot;time&quot;: 3
        },
        {
            &quot;average&quot;: 7000,
            &quot;count&quot;: 1,
            &quot;time&quot;: &quot;any&quot;
        },
        {
            &quot;average&quot;: 8000,
            &quot;count&quot;: 1,
            &quot;time&quot;: 5
        },
        {
            &quot;average&quot;: 6500,
            &quot;count&quot;: 1,
            &quot;time&quot;: 4
        }
    ],
    &quot;based_company&quot;: [
        {
            &quot;average&quot;: 15000,
            &quot;company&quot;: {
                &quot;id&quot;: 36,
                &quot;background_banner_image&quot;: null,
                &quot;company_name&quot;: &quot;Connect Resources&quot;,
                &quot;company_slug&quot;: &quot;connect-resources&quot;,
                &quot;company_website&quot;: &quot;https://www.connectresources.ae&quot;
            }
        },
        {
            &quot;average&quot;: 3500,
            &quot;company&quot;: {
                &quot;id&quot;: 57,
                &quot;background_banner_image&quot;: null,
                &quot;company_name&quot;: &quot;Connectgroup&quot;,
                &quot;company_slug&quot;: &quot;connectgroup&quot;,
                &quot;company_website&quot;: &quot;https://connectgroup.co/&quot;
            }
        },
        {
            &quot;average&quot;: 4000,
            &quot;company&quot;: {
                &quot;id&quot;: 61,
                &quot;background_banner_image&quot;: null,
                &quot;company_name&quot;: &quot;Tafaseel Group&quot;,
                &quot;company_slug&quot;: &quot;HR&quot;,
                &quot;company_website&quot;: &quot;https://tafaseel.ae/about-us&quot;
            }
        },
        {
            &quot;average&quot;: 3250,
            &quot;company&quot;: {
                &quot;id&quot;: 63,
                &quot;background_banner_image&quot;: null,
                &quot;company_name&quot;: &quot;redford recruiters&quot;,
                &quot;company_slug&quot;: &quot;Admin&quot;,
                &quot;company_website&quot;: &quot;https://www.mediclinic.com/en/sustainable-development.html&quot;
            }
        }
    ]
}</code>
 </pre>
            <blockquote>
            <p>Example response (400, Industry not exists):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;error&quot;: &quot;The industry selected not exists&quot;
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-salaries-insights" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-salaries-insights"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-salaries-insights"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-salaries-insights" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-salaries-insights">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-salaries-insights" data-method="GET"
      data-path="api/salaries/insights"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-salaries-insights', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-salaries-insights"
                    onclick="tryItOut('GETapi-salaries-insights');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-salaries-insights"
                    onclick="cancelTryOut('GETapi-salaries-insights');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-salaries-insights"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/salaries/insights</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-salaries-insights"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-salaries-insights"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>industry_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="industry_id"                data-endpoint="GETapi-salaries-insights"
               value="31"
               data-component="query">
    <br>
<p>Example: <code>31</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>country_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="country_id"                data-endpoint="GETapi-salaries-insights"
               value="45"
               data-component="query">
    <br>
<p>Example: <code>45</code></p>
            </div>
                </form>

                    <h2 id="salaries-GETapi-salaries-career-paths">Get salary similar career paths</h2>

<p>
</p>

<p>Returns a list of other similar industries with the average salary based on published jobs offers</p>

<span id="example-requests-GETapi-salaries-career-paths">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/salaries/career-paths?industry_id=1&amp;country_id=1&amp;num_items=3" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"industry_id\": \"ut\",
    \"country_id\": \"sapiente\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/salaries/career-paths"
);

const params = {
    "industry_id": "1",
    "country_id": "1",
    "num_items": "3",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "industry_id": "ut",
    "country_id": "sapiente"
};

fetch(url, {
    method: "GET",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-salaries-career-paths">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1991
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">[
    {
        &quot;industry&quot;: {
            &quot;id&quot;: 1,
            &quot;name&quot;: &quot;Tech Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        &quot;salary_average&quot;: 50000,
        &quot;total&quot;: 1
    },
    {
        &quot;industry&quot;: {
            &quot;id&quot;: 21,
            &quot;name&quot;: &quot;Legal Tech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        &quot;salary_average&quot;: 0,
        &quot;total&quot;: 0
    },
    {
        &quot;industry&quot;: {
            &quot;id&quot;: 27,
            &quot;name&quot;: &quot;HealthCare Tech&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:36.000000Z&quot;
        },
        &quot;salary_average&quot;: 0,
        &quot;total&quot;: 0
    }
]</code>
 </pre>
            <blockquote>
            <p>Example response (400, Industry not exists):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
    &quot;error&quot;: &quot;The industry selected not exists&quot;
}</code>
 </pre>
            <blockquote>
            <p>Example response (422, Missing arguments):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
  &quot;message&quot;: &quot;The industry id field is required.&quot;,
  &quot;errors&quot;: {
    &quot;industry_id&quot;: [ &quot;The industry id field is required.&quot; ]
    &quot;country_id&quot;: [ &quot;The country id field is required.&quot; ]
  }
 }</code>
 </pre>
    </span>
<span id="execution-results-GETapi-salaries-career-paths" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-salaries-career-paths"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-salaries-career-paths"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-salaries-career-paths" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-salaries-career-paths">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-salaries-career-paths" data-method="GET"
      data-path="api/salaries/career-paths"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-salaries-career-paths', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-salaries-career-paths"
                    onclick="tryItOut('GETapi-salaries-career-paths');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-salaries-career-paths"
                    onclick="cancelTryOut('GETapi-salaries-career-paths');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-salaries-career-paths"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/salaries/career-paths</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-salaries-career-paths"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-salaries-career-paths"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>industry_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="industry_id"                data-endpoint="GETapi-salaries-career-paths"
               value="1"
               data-component="query">
    <br>
<p>Number Selected industry - Example: <code>1</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>country_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="country_id"                data-endpoint="GETapi-salaries-career-paths"
               value="1"
               data-component="query">
    <br>
<p>Number Selected country - Example: <code>1</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>num_items</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="number" style="display: none"
               step="any"               name="num_items"                data-endpoint="GETapi-salaries-career-paths"
               value="3"
               data-component="query">
    <br>
<p>Number of items returned - Example: <code>3</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>industry_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="industry_id"                data-endpoint="GETapi-salaries-career-paths"
               value="ut"
               data-component="body">
    <br>
<p>Example: <code>ut</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>country_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="country_id"                data-endpoint="GETapi-salaries-career-paths"
               value="sapiente"
               data-component="body">
    <br>
<p>Example: <code>sapiente</code></p>
        </div>
        </form>

                    <h2 id="salaries-GETapi-salaries-latest-openings">Get latest job openings</h2>

<p>
</p>

<p>Returns a list of recent job offers on the selected country and industry</p>

<span id="example-requests-GETapi-salaries-latest-openings">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "https://api.thetalentpoint.com/api/salaries/latest-openings?industry_id=31&amp;country_id=45&amp;num_items=3" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"industry_id\": \"eos\",
    \"country_id\": \"eius\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "https://api.thetalentpoint.com/api/salaries/latest-openings"
);

const params = {
    "industry_id": "31",
    "country_id": "45",
    "num_items": "3",
};
Object.keys(params)
    .forEach(key =&gt; url.searchParams.append(key, params[key]));

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "industry_id": "eos",
    "country_id": "eius"
};

fetch(url, {
    method: "GET",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-salaries-latest-openings">
            <blockquote>
            <p>Example response (200):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
x-ratelimit-limit: 2000
x-ratelimit-remaining: 1990
access-control-allow-origin: *
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">[
    {
        &quot;id&quot;: 109,
        &quot;company&quot;: {
            &quot;id&quot;: 63,
            &quot;background_banner_image&quot;: null,
            &quot;company_name&quot;: &quot;redford recruiters&quot;,
            &quot;company_slug&quot;: &quot;Admin&quot;,
            &quot;company_website&quot;: &quot;https://www.mediclinic.com/en/sustainable-development.html&quot;
        },
        &quot;industry&quot;: {
            &quot;id&quot;: 31,
            &quot;name&quot;: &quot;HR Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;
        },
        &quot;country&quot;: {
            &quot;id&quot;: 45,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;slug&quot;: &quot;united-arab-emirates&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
            &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
            &quot;capital&quot;: &quot;Abu Dhabi&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        &quot;job_title&quot;: &quot;HR (Only Male Arab Speaker) in Al Ain&quot;,
        &quot;job_slug&quot;: &quot;al-ain-city/hr-only-male-arab-speaker-in-al-ain-109&quot;,
        &quot;experience&quot;: &quot;4&quot;,
        &quot;skills_required&quot;: &quot;407&quot;,
        &quot;monthly_fixed_salary_currency&quot;: &quot;UAE Dirham (AED)&quot;,
        &quot;monthly_fixed_salary_min&quot;: &quot;6500&quot;,
        &quot;monthly_fixed_salary_max&quot;: &quot;6000&quot;,
        &quot;is_featured&quot;: 0,
        &quot;job_status&quot;: &quot;active&quot;
    },
    {
        &quot;id&quot;: 104,
        &quot;company&quot;: {
            &quot;id&quot;: 61,
            &quot;background_banner_image&quot;: null,
            &quot;company_name&quot;: &quot;Tafaseel Group&quot;,
            &quot;company_slug&quot;: &quot;HR&quot;,
            &quot;company_website&quot;: &quot;https://tafaseel.ae/about-us&quot;
        },
        &quot;industry&quot;: {
            &quot;id&quot;: 31,
            &quot;name&quot;: &quot;HR Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;
        },
        &quot;country&quot;: {
            &quot;id&quot;: 45,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;slug&quot;: &quot;united-arab-emirates&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
            &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
            &quot;capital&quot;: &quot;Abu Dhabi&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        &quot;job_title&quot;: &quot;Human Capital Director&quot;,
        &quot;job_slug&quot;: &quot;al-ain-city/human-capital-director-104&quot;,
        &quot;experience&quot;: &quot;5&quot;,
        &quot;skills_required&quot;: &quot;6&quot;,
        &quot;monthly_fixed_salary_currency&quot;: &quot;UAE Dirham (AED)&quot;,
        &quot;monthly_fixed_salary_min&quot;: &quot;8000&quot;,
        &quot;monthly_fixed_salary_max&quot;: &quot;5000&quot;,
        &quot;is_featured&quot;: 0,
        &quot;job_status&quot;: &quot;active&quot;
    },
    {
        &quot;id&quot;: 98,
        &quot;company&quot;: {
            &quot;id&quot;: 57,
            &quot;background_banner_image&quot;: null,
            &quot;company_name&quot;: &quot;Connectgroup&quot;,
            &quot;company_slug&quot;: &quot;connectgroup&quot;,
            &quot;company_website&quot;: &quot;https://connectgroup.co/&quot;
        },
        &quot;industry&quot;: {
            &quot;id&quot;: 31,
            &quot;name&quot;: &quot;HR Solutions&quot;,
            &quot;status&quot;: &quot;active&quot;
        },
        &quot;country&quot;: {
            &quot;id&quot;: 45,
            &quot;country_name&quot;: &quot;United Arab Emirates&quot;,
            &quot;slug&quot;: &quot;united-arab-emirates&quot;,
            &quot;flag&quot;: &quot;twemoji_flag-united-arab-emirates.png&quot;,
            &quot;currency&quot;: &quot;UAE Dirham (AED)&quot;,
            &quot;capital&quot;: &quot;Abu Dhabi&quot;,
            &quot;status&quot;: &quot;active&quot;,
            &quot;created_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;,
            &quot;updated_at&quot;: &quot;2023-08-30T22:19:35.000000Z&quot;
        },
        &quot;job_title&quot;: &quot;Chief Project Engineer&quot;,
        &quot;job_slug&quot;: &quot;dubai/chief-project-engineer-98&quot;,
        &quot;experience&quot;: null,
        &quot;skills_required&quot;: &quot;401&quot;,
        &quot;monthly_fixed_salary_currency&quot;: &quot;UAE Dirham (AED)&quot;,
        &quot;monthly_fixed_salary_min&quot;: &quot;7000&quot;,
        &quot;monthly_fixed_salary_max&quot;: &quot;5000&quot;,
        &quot;is_featured&quot;: 0,
        &quot;job_status&quot;: &quot;active&quot;
    }
]</code>
 </pre>
            <blockquote>
            <p>Example response (422, Missing arguments):</p>
        </blockquote>
                <pre>

<code class="language-json" style="max-height: 300px;">{
 &quot;message&quot;: &quot;The industry id field is required.&quot;,
 &quot;errors&quot;: {
   &quot;industry_id&quot;: [ &quot;The industry id field is required.&quot; ]
   &quot;country_id&quot;: [ &quot;The country id field is required.&quot; ]
 }
}</code>
 </pre>
    </span>
<span id="execution-results-GETapi-salaries-latest-openings" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-salaries-latest-openings"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-salaries-latest-openings"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-salaries-latest-openings" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-salaries-latest-openings">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-salaries-latest-openings" data-method="GET"
      data-path="api/salaries/latest-openings"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-salaries-latest-openings', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-salaries-latest-openings"
                    onclick="tryItOut('GETapi-salaries-latest-openings');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-salaries-latest-openings"
                    onclick="cancelTryOut('GETapi-salaries-latest-openings');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-salaries-latest-openings"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/salaries/latest-openings</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-salaries-latest-openings"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-salaries-latest-openings"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                            <h4 class="fancy-heading-panel"><b>Query Parameters</b></h4>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>industry_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="industry_id"                data-endpoint="GETapi-salaries-latest-openings"
               value="31"
               data-component="query">
    <br>
<p>Number Selected industry - Example: <code>31</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>country_id</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="country_id"                data-endpoint="GETapi-salaries-latest-openings"
               value="45"
               data-component="query">
    <br>
<p>Number Selected country - Example: <code>45</code></p>
            </div>
                                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>num_items</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
<i>optional</i> &nbsp;
                <input type="number" style="display: none"
               step="any"               name="num_items"                data-endpoint="GETapi-salaries-latest-openings"
               value="3"
               data-component="query">
    <br>
<p>Number of items returned - Example: <code>3</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>industry_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="industry_id"                data-endpoint="GETapi-salaries-latest-openings"
               value="eos"
               data-component="body">
    <br>
<p>Example: <code>eos</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>country_id</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="country_id"                data-endpoint="GETapi-salaries-latest-openings"
               value="eius"
               data-component="body">
    <br>
<p>Example: <code>eius</code></p>
        </div>
        </form>

            

        
    </div>
    <div class="dark-box">
                    <div class="lang-selector">
                                                        <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                                        <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                            </div>
            </div>
</div>
</body>
</html>
