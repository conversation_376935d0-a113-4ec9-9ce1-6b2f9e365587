name: Authentication
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/authentication/session
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get session'
      description: 'Returns the current session'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3vaPb1c4hDVgZ65k6fE8dae'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/authentication/logout
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: Logout
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer fh65acEVDP3v4Zeb6gda81k'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/authenticate
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'Must be a valid email address.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      password:
        name: password
        description: ''
        required: true
        example: occaecati
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
      password: occaecati
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
