name: Salaries
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/salaries/insights
    metadata:
      groupName: Salaries
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get salary insights'
      description: 'Return all information by industry and country'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      industry_id:
        name: industry_id
        description: ''
        required: true
        example: '31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        custom: []
      country_id:
        name: country_id
        description: ''
        required: true
        example: '45'
        type: string
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      industry_id: '31'
      country_id: '45'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"industry":{"id":31,"name":"HR Solutions","status":"active","created_at":"2023-08-30T22:19:36.000000Z","updated_at":"2023-08-30T22:19:36.000000Z"},"country":{"id":45,"country_name":"United Arab Emirates","slug":"united-arab-emirates","flag":"twemoji_flag-united-arab-emirates.png","currency":"UAE Dirham (AED)","capital":"Abu Dhabi","status":"active","created_at":"2023-08-30T22:19:35.000000Z","updated_at":"2023-08-30T22:19:35.000000Z"},"salary":{"average":6437.5,"min":"2000","max":"8000","total":8},"based_experience":[{"average":2000,"count":1,"time":2},{"average":7000,"count":4,"time":3},{"average":7000,"count":1,"time":"any"},{"average":8000,"count":1,"time":5},{"average":6500,"count":1,"time":4}],"based_company":[{"average":15000,"company":{"id":36,"background_banner_image":null,"company_name":"Connect Resources","company_slug":"connect-resources","company_website":"https:\/\/www.connectresources.ae"}},{"average":3500,"company":{"id":57,"background_banner_image":null,"company_name":"Connectgroup","company_slug":"connectgroup","company_website":"https:\/\/connectgroup.co\/"}},{"average":4000,"company":{"id":61,"background_banner_image":null,"company_name":"Tafaseel Group","company_slug":"HR","company_website":"https:\/\/tafaseel.ae\/about-us"}},{"average":3250,"company":{"id":63,"background_banner_image":null,"company_name":"redford recruiters","company_slug":"Admin","company_website":"https:\/\/www.mediclinic.com\/en\/sustainable-development.html"}}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '2000'
          x-ratelimit-remaining: '1992'
          access-control-allow-origin: '*'
        description: null
        custom: []
      -
        status: 400
        content: '{"error": "The industry selected not exists"}'
        headers: []
        description: 'Industry not exists'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/salaries/career-paths
    metadata:
      groupName: Salaries
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get salary similar career paths'
      description: 'Returns a list of other similar industries with the average salary based on published jobs offers'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      industry_id:
        name: industry_id
        description: 'Number Selected industry -'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      country_id:
        name: country_id
        description: 'Number Selected country -'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      num_items:
        name: num_items
        description: 'Number of items returned -'
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      industry_id: 1
      country_id: 1
      num_items: 3
    bodyParameters:
      industry_id:
        name: industry_id
        description: ''
        required: true
        example: ut
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      country_id:
        name: country_id
        description: ''
        required: true
        example: sapiente
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      industry_id: ut
      country_id: sapiente
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"industry":{"id":1,"name":"Tech Solutions","status":"active","created_at":"2023-08-30T22:19:36.000000Z","updated_at":"2023-08-30T22:19:36.000000Z"},"salary_average":50000,"total":1},{"industry":{"id":21,"name":"Legal Tech","status":"active","created_at":"2023-08-30T22:19:36.000000Z","updated_at":"2023-08-30T22:19:36.000000Z"},"salary_average":0,"total":0},{"industry":{"id":27,"name":"HealthCare Tech","status":"active","created_at":"2023-08-30T22:19:36.000000Z","updated_at":"2023-08-30T22:19:36.000000Z"},"salary_average":0,"total":0}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '2000'
          x-ratelimit-remaining: '1991'
          access-control-allow-origin: '*'
        description: null
        custom: []
      -
        status: 400
        content: '{"error": "The industry selected not exists"}'
        headers: []
        description: 'Industry not exists'
        custom: []
      -
        status: 422
        content: |-
          {
            "message": "The industry id field is required.",
            "errors": {
              "industry_id": [ "The industry id field is required." ]
              "country_id": [ "The country id field is required." ]
            }
           }
        headers: []
        description: 'Missing arguments'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/salaries/latest-openings
    metadata:
      groupName: Salaries
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get latest job openings'
      description: 'Returns a list of recent job offers on the selected country and industry'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      industry_id:
        name: industry_id
        description: 'Number Selected industry -'
        required: true
        example: 31
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      country_id:
        name: country_id
        description: 'Number Selected country -'
        required: true
        example: 45
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
      num_items:
        name: num_items
        description: 'Number of items returned -'
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        custom: []
    cleanQueryParameters:
      industry_id: 31
      country_id: 45
      num_items: 3
    bodyParameters:
      industry_id:
        name: industry_id
        description: ''
        required: true
        example: eos
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
      country_id:
        name: country_id
        description: ''
        required: true
        example: eius
        type: string
        enumValues: []
        exampleWasSpecified: false
        custom: []
    cleanBodyParameters:
      industry_id: eos
      country_id: eius
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":109,"company":{"id":63,"background_banner_image":null,"company_name":"redford recruiters","company_slug":"Admin","company_website":"https:\/\/www.mediclinic.com\/en\/sustainable-development.html"},"industry":{"id":31,"name":"HR Solutions","status":"active"},"country":{"id":45,"country_name":"United Arab Emirates","slug":"united-arab-emirates","flag":"twemoji_flag-united-arab-emirates.png","currency":"UAE Dirham (AED)","capital":"Abu Dhabi","status":"active","created_at":"2023-08-30T22:19:35.000000Z","updated_at":"2023-08-30T22:19:35.000000Z"},"job_title":"HR (Only Male Arab Speaker) in Al Ain","job_slug":"al-ain-city\/hr-only-male-arab-speaker-in-al-ain-109","experience":"4","skills_required":"407","monthly_fixed_salary_currency":"UAE Dirham (AED)","monthly_fixed_salary_min":"6500","monthly_fixed_salary_max":"6000","is_featured":0,"job_status":"active"},{"id":104,"company":{"id":61,"background_banner_image":null,"company_name":"Tafaseel Group","company_slug":"HR","company_website":"https:\/\/tafaseel.ae\/about-us"},"industry":{"id":31,"name":"HR Solutions","status":"active"},"country":{"id":45,"country_name":"United Arab Emirates","slug":"united-arab-emirates","flag":"twemoji_flag-united-arab-emirates.png","currency":"UAE Dirham (AED)","capital":"Abu Dhabi","status":"active","created_at":"2023-08-30T22:19:35.000000Z","updated_at":"2023-08-30T22:19:35.000000Z"},"job_title":"Human Capital Director","job_slug":"al-ain-city\/human-capital-director-104","experience":"5","skills_required":"6","monthly_fixed_salary_currency":"UAE Dirham (AED)","monthly_fixed_salary_min":"8000","monthly_fixed_salary_max":"5000","is_featured":0,"job_status":"active"},{"id":98,"company":{"id":57,"background_banner_image":null,"company_name":"Connectgroup","company_slug":"connectgroup","company_website":"https:\/\/connectgroup.co\/"},"industry":{"id":31,"name":"HR Solutions","status":"active"},"country":{"id":45,"country_name":"United Arab Emirates","slug":"united-arab-emirates","flag":"twemoji_flag-united-arab-emirates.png","currency":"UAE Dirham (AED)","capital":"Abu Dhabi","status":"active","created_at":"2023-08-30T22:19:35.000000Z","updated_at":"2023-08-30T22:19:35.000000Z"},"job_title":"Chief Project Engineer","job_slug":"dubai\/chief-project-engineer-98","experience":null,"skills_required":"401","monthly_fixed_salary_currency":"UAE Dirham (AED)","monthly_fixed_salary_min":"7000","monthly_fixed_salary_max":"5000","is_featured":0,"job_status":"active"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          x-ratelimit-limit: '2000'
          x-ratelimit-remaining: '1990'
          access-control-allow-origin: '*'
        description: null
        custom: []
      -
        status: 422
        content: |-
          {
           "message": "The industry id field is required.",
           "errors": {
             "industry_id": [ "The industry id field is required." ]
             "country_id": [ "The country id field is required." ]
           }
          }
        headers: []
        description: 'Missing arguments'
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
