import * as Yup from 'yup';

export const reviewValidationSchema = Yup.object()?.shape({
  feedback: Yup?.object().shape({
    work_environment: Yup?.number()?.required('').min(1),
    work_life_balance: Yup?.number()?.required('').min(1),
    career_growth: Yup?.number()?.required('').min(1),
    compensation_benefits: Yup?.number()?.required('').min(1),
    job_security: Yup?.number()?.required('').min(1),
  }),
  review: Yup?.object().shape({
    review_summary: Yup?.string()?.required(''),
    review_details: Yup?.string()?.required(''),
  }),
  work_experience: Yup?.object().shape({
    job_title: Yup?.string().required(''),
    job_location: Yup?.object()?.shape({
      country: Yup.string()?.required(''),
      city: Yup?.string()?.required(''),
    }),
    job_start_date: Yup?.string()?.required(''),
    present: Yup.boolean(),
    job_end_date: Yup.date().when('present', {
      is: false,
      then: schema => schema.required(''),
      otherwise: schema => schema.nullable(),
    }),
  }),
});
