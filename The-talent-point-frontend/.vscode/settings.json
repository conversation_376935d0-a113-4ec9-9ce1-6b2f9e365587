{
  // Indentation and Formatting
  "editor.tabSize": 3,
  "editor.detectIndentation": true,
  "editor.wordWrap": "on",
  "editor.minimap.enabled": false,

  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "[typescriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit"
    }
  },
  "deepscan.ignoreConfirmWarning": true,
  "cSpell.words": ["Frofile"],
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.fontSize": 12,
  "terminal.integrated.defaultProfile.osx": "bash",
  "workbench.settings.enableNaturalLanguageSearch": true,
  "window.commandCenter": true,
  "window.closeWhenEmpty": false,
  "window.autoDetectColorScheme": false
}
