const GTM_ID = 'GTM-NX4VWT66';

export const GTMPageView = url => {
  window.dataLayer.push({
    event: 'pageview',
    page: url,
  });
};

export const initializeGTM = () => {
  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    'gtm.start': new Date().getTime(),
    event: 'gtm.js',
  });
  const gtmScript = document.createElement('script');
  gtmScript.src = `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}`;
  gtmScript.async = true;
  document.head.appendChild(gtmScript);
};
