export type operators = 'IS NULL' | 'IS NOT NULL' | '<' | '>' | '=' | 'BETWEEN' | 'LIKE';

export const getAllOperatorOptions = () => {
  return [
    {
      label: 'IS NULL',
      value: 'IS NULL',
    },
    {
      label: 'IS NOT NULL',
      value: 'IS NOT NULL',
    },
    {
      label: '<',
      value: '<',
    },
    {
      label: '>',
      value: '>',
    },
    {
      label: '=',
      value: '=',
    },
    {
      label: 'BETWEEN',
      value: 'BETWEEN',
    },
    {
      label: 'LIKE',
      value: 'LIKE',
    },
  ];
};
