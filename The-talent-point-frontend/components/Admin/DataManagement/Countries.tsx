import React, {useState, useEffect} from 'react';
import {
  getAllCountriesForAdmin,
  deleteCountry,
  editAndSaveCountryData,
  updateCountryStatus,
} from '../../../lib/adminapi';
import {all} from 'axios';
import swal from 'sweetalert';
import {ToastContainer, toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from '../../../components/Common/NewPagination';
import {paginate} from '../../../helpers/paginate';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import SuccessToast from '../../Common/showSuccessTostrMessage';
import ErrorToast from '../../Common/showErrorTostrMessage';
import Image from 'next/image';
import {Switch} from 'antd';
interface allCountry {
  country_name: string;
  slug: string;
  flag: string;
  currency: string;
  capital: string;
  id: string;
}

export default function Countries() {
  const [allCountry, setAllLocation] = useState<allCountry[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredCountry, setFilteredCountry] = useState<allCountry[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const [country_name, setCountryName] = useState('');
  const [uploadLogoError, setUploadLogoError] = useState('');
  const [slug, setCountrySlug] = useState('');
  const [flag, setCountryFlag] = useState('');
  const [capital, setCountryCapital] = useState('');
  const [currency, setCountryCurrency] = useState('');
  const [id, setCountryId] = useState('');
  const [showimage, setShowImage] = useState(true);
  const [previewImage, setPreviewImage] = useState('');
  const [modalConfirm, setModalConfirm] = useState(false);

  const [newflag, setCountryNewFlag] = useState('');
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');
  const [processing, setProcessing] = useState(false);

  const modalConfirmClose = () => {
    setModalConfirm(false);
  };

  const pageSize = 20;

  useEffect(() => {
    getAllCountriesForAdmin()
      .then(response => {
        setAllLocation(response);
        filterBlogs(response, searchQuery);
      })
      .catch(error => {
        console.error(error);
      });
  }, [searchQuery]);

  const fetchData = async () => {
    try {
      const response = await getAllCountriesForAdmin();
      setAllLocation(response);
      filterBlogs(response, searchQuery);
    } catch (error) {
      console.error(error);
    }
  };

  const filterBlogs = (allCountry: allCountry[], searchValue: string) => {
    const filteredCountry = allCountry.filter((country: allCountry) =>
      country.country_name.toLowerCase().includes(searchValue.toLowerCase()),
    );
    setFilteredCountry(filteredCountry);
    setCurrentPage(1); // Reset to the first page when applying a filter
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value.toLowerCase();
    setSearchQuery(searchValue);
    filterBlogs(allCountry, searchValue);
  };

  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  const paginatedLocation = paginate(filteredCountry, currentPage, pageSize);

  const handlLocationSubmit = async (event: any) => {
    event.preventDefault();
    setProcessing(true);

    if (id) {
      const nameExists = allCountry.some(record => record.country_name == country_name && record.id != id);

      if (nameExists) {
        setShowmessage('Country name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }

      const slugExists = allCountry.some(record => record.slug == slug && record.id != id);

      if (slugExists) {
        setShowmessage('Country Slug already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }
    } else {
      const nameExists = allCountry.some(record => record.country_name == country_name);

      if (nameExists) {
        setShowmessage('Country name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }

      const slugExists = allCountry.some(record => record.slug == slug);

      if (slugExists) {
        setShowmessage('Country Slug already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
        return;
      }
    }
    try {
      const data = {
        id: id,
        country_name: country_name,
        slug: slug,
        capital: capital,
        currency: currency,
      };
      const res = await editAndSaveCountryData(data, flag);

      if (res.status == true) {
        setProcessing(false);
        setShowmessage(res.message);
        setShowPopupunsave(true);
        setTimeout(() => {
          setShowPopupunsave(false);
        }, 1000);
        fetchData();
        resetForm();
        setTimeout(() => {
          setModalConfirm(false);
          false;
        }, 1000);
      } else {
        setShowmessage(res.message);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 10000);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleCancel = () => {
    setModalConfirm(false);
  };

  const resetForm = () => {
    setCountryName('');
    setCountrySlug('');
    setCountryNewFlag('');
    setCountryCapital('');
    setCountryCurrency('');
    setCountryId('');
    setShowImage(true);
    setPreviewImage('');
  };

  const handleEdit = (id: any) => {
    const filteredRecords = allCountry.filter(record => record.id === id);
    if (filteredRecords.length > 0) {
      const {country_name} = filteredRecords[0];
      const {slug} = filteredRecords[0];
      const {flag} = filteredRecords[0];
      const {currency} = filteredRecords[0];
      const {capital} = filteredRecords[0];
      setCountryName(country_name);
      setCountrySlug(slug);

      setCountryCapital(currency);
      setCountryCurrency(capital);
      setCountryId(id);
      toast.dismiss();

      if (flag) {
        setShowImage(false);
        setCountryNewFlag(flag);
        setPreviewImage('');
      } else {
        setShowImage(true);
      }

      setModalConfirm(true);
    }
  };

  const handledeleteLocation = (id: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the Country',
      icon: 'warning',
      //buttons: true,
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: id,
        };

        deleteCountry(data)
          .then(res => {
            if (res.status === true) {
              setShowmessage(res.message);
              setShowPopupunsave(true);
              setTimeout(() => {
                setShowPopupunsave(false);
              }, 10000);

              fetchData();
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(() => {
            // Handle error
          });
      } else {
        console.log('Deletion failed');
      }
    });
  };

  const handleImageChange = (event: any) => {
    event.preventDefault();
    const selectedFile = event.target.files[0];
    if (selectedFile.type.includes('image')) {
      setCountryFlag(selectedFile);
      setPreviewImage(URL.createObjectURL(selectedFile));
      setShowImage(false);
      if (newflag) {
        setCountryNewFlag('');
      }
    } else {
      setUploadLogoError('Please upload an image file (JPEG,JPG,PNG).');
    }
  };
  const handleChangeCountryStatus = (checked: any, countryId: any) => {
    const data = {
      status: checked,
      countryId: countryId,
    };
    updateCountryStatus(data)
      .then(res => {
        if (res.status === true) {
          setShowmessage(res.message);
          setShowPopupunsave(true);
          setTimeout(() => {
            setShowPopupunsave(false);
          }, 10000);
          fetchData();
        } else {
          setShowmessage(res.message);
          setShowPopupunsave1(true);
          setTimeout(() => {
            setShowPopupunsave1(false);
          }, 10000);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  return (
    <>
      <div className="dash-right">
        <h1 className="data">Data Management</h1>
        <div className="data-management">
          <div className="row justify-content-end" id="sector_input">
            <div className="col-lg-4 col-md-12 text-end">
              <div className="d-flex gap-3">
                <div className="input-group mb-2 ">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search country here.."
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
                <div className="btnWidth">
                  <button
                    type="button"
                    className="btn-a primary-size-16 btn-bg-0055BA tab-w-100 w-100"
                    onClick={() => {
                      setModalConfirm(true);
                      resetForm();
                      toast.dismiss();
                    }}>
                    Add Country
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="table-part mt-4 max-w">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>Flag </th>
                  <th>Name</th>
                  <th>Captial</th>
                  <th>Currency</th>
                  <th>Status</th>
                  <th className="text-end">MANAGE </th>
                </tr>

                {paginatedLocation.map((country: any, index: any) => (
                  <tr key={index}>
                    <td data-th="Flag">
                      <img
                        src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/country/' + country.flag}
                        alt="logo-cir"
                        className=""
                        height={70}
                        width={70}
                      />
                    </td>

                    <td data-th="Name">
                      <p className="location">{country.country_name}</p>
                    </td>

                    <td data-th="Captial">
                      <p className="location">{country.capital}</p>
                    </td>

                    <td data-th="Currency">
                      <p className="location">{country.currency}</p>
                    </td>

                    <td data-th="Status">
                      <Switch
                        defaultChecked={country.status == 'active'}
                        onChange={checked => handleChangeCountryStatus(checked, country.id)}
                        checkedChildren="Enable"
                        unCheckedChildren="Disable"
                      />
                    </td>

                    <td data-th="MANAGE" className="text-end">
                      <i className="fa-solid fa-pencil edit-pencil" onClick={() => handleEdit(country.id)}></i>
                      <i
                        className="fa-regular fa-trash-can del-trash"
                        onClick={() => handledeleteLocation(country.id)}></i>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="pagination-wrapper mt-4">
            <div className="pagination-wrapper">
              <Pagination
                items={filteredCountry}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={onPageChange}
                activePage={currentPage} // Add the activePage property here
              />
            </div>
          </div>
        </div>
      </div>

      <PopupModal
        show={modalConfirm}
        handleClose={modalConfirmClose}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des '}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> {id ? 'Edit Location' : 'Add Location'}</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="p-4">
          <form className="form-experience-fieild" onSubmit={handlLocationSubmit}>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="United Kingdom"
                className="fild-des"
                name="name"
                onChange={e => setCountryName(e.target.value)}
                value={country_name}
                required
              />
              <label>Country Name*</label>
            </div>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="united-kingdom"
                className="fild-des"
                name="name"
                onChange={e => setCountrySlug(e.target.value)}
                value={slug}
                required
              />
              <label>Slug*</label>
            </div>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="london"
                className="fild-des"
                name="name"
                onChange={e => setCountryCapital(e.target.value)}
                value={capital}
                required
              />
              <label>Capital*</label>
            </div>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="pounds"
                className="fild-des"
                name="name"
                onChange={e => setCountryCurrency(e.target.value)}
                value={currency}
                required
              />
              <label>Currency*</label>
            </div>
            <label className="f-14 w-700 c-333333 w-100 mb-2 mt-4">Update Flag</label>

            <div className="row">
              <div className="col-lg-2 col-md-3 col-12">
                {showimage && (
                  <div className="logo-box-data">
                    <img
                      src={process.env.NEXT_PUBLIC_BASE_URL + 'images/logo-cir.png'}
                      alt="logo-cir"
                      className=""
                      width={70}
                      height={70}
                    />
                  </div>
                )}

                {newflag && (
                  <img
                    src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/country/' + newflag}
                    alt="logo-cir"
                    className=""
                    height={70}
                    width={70}
                  />
                )}
                <div className="logo-box-data">
                  {previewImage && <img src={previewImage} alt="Preview" className="" height={70} width={70} />}
                </div>
              </div>
              <div className="col-lg-8 col-md-6 col-sm-12">
                <a>
                  <div className="uploade-btn">
                    <input
                      type="file"
                      name="logo"
                      id="logo"
                      onChange={handleImageChange}
                      accept="image/jpeg, image/png"
                    />
                    <div className="btn-a primary-size-16 btn-bg-0055BA max-220 text-center">
                      <i className="fa-solid fa-upload mx-2"></i> Upload Logo
                    </div>
                    {uploadLogoError && <p className="error mt-2">{uploadLogoError}</p>}
                  </div>
                </a>
              </div>
            </div>

            <div className="text-right mt-3">
              <button type="button" className="cancel" onClick={handleCancel}>
                Cancel
              </button>
              <button type="submit" className="save" disabled={processing}>
                {processing ? 'Please wait...' : 'Save'}
              </button>
            </div>
          </form>
        </div>
      </PopupModal>
      {showPopupunsave && <SuccessToast message={showmessage} />}
      {showPopupunsave1 && <ErrorToast message={showmessage} />}
    </>
  );
}
