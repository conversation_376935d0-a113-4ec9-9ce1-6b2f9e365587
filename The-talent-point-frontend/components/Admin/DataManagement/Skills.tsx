import React, {useState, useEffect} from 'react';
import {getAllSector, deleteSkill, editAndSaveSkillData, getAllSkillsForAdmin} from '../../../lib/adminapi';
import swal from 'sweetalert';
import {ToastContainer, toast} from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from '../../../components/Common/NewPagination';
import {paginate} from '../../../helpers/paginate';
import PopupModal from '../../../components/Common/PopupModal';
import Link from 'next/link';
import SuccessToast from '../../Common/showSuccessTostrMessage';
import ErrorToast from '../../Common/showErrorTostrMessage';

interface allSkills {
  sector_name: string;
  sector_id: string;
  skills: string;
  id: string;
}

interface allSectors {
  sector_name: string;
  id: string;
}

export default function Skills() {
  const [allSkills, setAllSkills] = useState<allSkills[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredSkills, setfilteredSkills] = useState<allSkills[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const [skills, setSkillName] = useState('');
  const [id, setSkillId] = useState('');
  const [sector_id, setSectorId] = useState('');

  const [allSectors, setAllSector] = useState<allSectors[]>([]);

  const [modalConfirm, setModalConfirm] = useState(false);
  const [showPopupunsave, setShowPopupunsave] = useState(false);
  const [showPopupunsave1, setShowPopupunsave1] = useState(false);
  const [showmessage, setShowmessage] = useState('');

  const modalConfirmClose = () => {
    setModalConfirm(false);
  };

  const pageSize = 20;

  useEffect(() => {
    fetchSkillsData();
    fetchAllSector();
  });

  const fetchSkillsData = async () => {
    try {
      const response = await getAllSkillsForAdmin();
      setAllSkills(response.data);
      filterSkills(response.data, searchQuery);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchAllSector = async () => {
    try {
      const response = await getAllSector();
      setAllSector(response.sectors);
    } catch (error) {
      console.error(error);
    }
  };

  const filterSkills = (allSkills: allSkills[], searchValue: string) => {
    const filteredSkills = allSkills.filter((skills: allSkills) =>
      skills.skills.toLowerCase().includes(searchValue.toLowerCase()),
    );
    setfilteredSkills(filteredSkills);
    setCurrentPage(1); // Reset to the first page when applying a filter
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value.toLowerCase();
    setSearchQuery(searchValue);
    filterSkills(allSkills, searchValue);
  };

  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  const paginatedSector = paginate(filteredSkills, currentPage, pageSize);

  const handlSkillSubmit = async (event: any) => {
    event.preventDefault();

    if (id) {
      const nameExists = allSkills.some(record => record.skills === skills && record.id != id);

      if (nameExists) {
        setShowmessage('Skill name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
        return;
      }
    } else {
      const nameExists = allSkills.some(record => record.skills === skills);

      if (nameExists) {
        setShowmessage('Skill name already in use');
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
        return;
      }
    }
    try {
      const data = {
        id: id,
        skills: skills,
        sector_id: sector_id,
      };

      const res = await editAndSaveSkillData(data);

      if (res.status == true) {
        setShowmessage(res.message);
        setShowPopupunsave(true);
        setTimeout(() => {
          setShowPopupunsave(false);
        }, 1000);
        setTimeout(() => {
          setModalConfirm(false);
        }, 1000);
        fetchSkillsData();
        resetForm();
      } else {
        setShowmessage(res.message);
        setShowPopupunsave1(true);
        setTimeout(() => {
          setShowPopupunsave1(false);
        }, 1000);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleCancel = () => {
    setModalConfirm(false);
  };

  const resetForm = () => {
    setSkillName('');
    setSkillId('');
    setSectorId('');
  };

  const handleEdit = (id: any) => {
    const filteredRecords = allSkills.filter(record => record.id == id);
    if (filteredRecords.length > 0) {
      const {skills} = filteredRecords[0];
      const {sector_id} = filteredRecords[0];
      setSkillName(skills);
      setSkillId(id);
      setSectorId(sector_id);
      toast.dismiss();
      setModalConfirm(true);
    }
  };

  const handleDeleteSkill = (id: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the skill',
      icon: 'warning',
      //buttons: true,
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: id,
        };

        deleteSkill(data)
          .then(res => {
            if (res.status === true) {
              setShowmessage(res.message);
              setShowPopupunsave(true);
              setTimeout(() => {
                setShowPopupunsave(false);
              }, 1000);
              fetchSkillsData();
            } else {
              console.log('Deletion failed');
            }
          })
          .catch(() => {
            // Handle error
          });
      } else {
        console.log('Deletion failed');
      }
    });
  };

  return (
    <>
      <div className="dash-right">
        <h1 className="data">Data Management</h1>
        <div className="data-management">
          <div className="row justify-content-end" id="sector_input">
            <div className="col-lg-4 col-md-12 text-end">
              <div className="d-flex gap-3">
                <div className="input-group mb-2 ">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search country here.."
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                </div>
                <div className="btnWidth">
                  <button
                    type="button"
                    className="btn-a primary-size-16 btn-bg-0055BA tab-w-100 w-100"
                    onClick={() => {
                      setModalConfirm(true);
                      resetForm();
                      toast.dismiss();
                    }}>
                    Add Skill
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="table-part mt-4 max-w">
            <table className="rwd-table">
              <tbody>
                <tr>
                  <th>Skill name </th>
                  <th>Sector name </th>
                  <th className="text-end">MANAGE </th>
                </tr>

                {paginatedSector.map((skills: any, index: any) => (
                  <tr key={index}>
                    <td data-th="Skill name">
                      <p className="location">{skills.skills}</p>
                    </td>

                    <td data-th="Sector name">
                      <p className="location">{skills.sector_name}</p>
                    </td>

                    <td className="text-end" data-th="MANAGE">
                      <i className="fa-solid fa-pencil edit-pencil" onClick={() => handleEdit(skills.id)}></i>
                      <i className="fa-regular fa-trash-can del-trash" onClick={() => handleDeleteSkill(skills.id)}></i>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="pagination-wrapper mt-4">
            <div className="pagination-wrapper">
              <Pagination
                items={filteredSkills}
                currentPage={currentPage}
                pageSize={pageSize}
                onPageChange={onPageChange}
                activePage={currentPage} // Add the activePage property here
              />
            </div>
          </div>
        </div>
      </div>

      <PopupModal
        show={modalConfirm}
        handleClose={modalConfirmClose}
        customclass={'add_company_signup_popup modal-lg body-sp-0 '}
        closebtnclass={'close-x  bg-0055BA border-design close-b-des'}
        closebtnicon={'icon'}>
        <div className="head-box">
          <div className="row">
            <div className="col-sm-10">
              <p className="f-26 mb-2 mt-2"> {id ? 'Edit Skill' : 'Add Skill'}</p>
            </div>
            <div className="col-sm-2 text-right">
              <button
                type="button"
                className="close-x  bg-0055BA border-design"
                data-bs-dismiss="modal"
                aria-label="Close">
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          </div>
        </div>
        <div className="p-4">
          <form className="form-experience-fieild" onSubmit={handlSkillSubmit}>
            <div className="form_field_sec">
              <input
                type="text"
                placeholder="HTML and CSS.."
                className="fild-des"
                name="name"
                onChange={e => setSkillName(e.target.value)}
                value={skills}
                required
              />
              <label>Skill Name*</label>
            </div>
            <div className="form_field_sec">
              <select
                className="fild-des"
                name="sector"
                value={sector_id}
                onChange={e => setSectorId(e.target.value)}
                required>
                <option value="">Choose a sector</option>
                {allSectors.length > 0
                  ? allSectors.map(sector => (
                      <option key={sector.id} value={sector.id}>
                        {sector.sector_name}
                      </option>
                    ))
                  : ''}
              </select>
              <label>Sector Name</label>
            </div>
            <div className="text-right mt-3">
              <button type="button" className="cancel" onClick={handleCancel}>
                Cancel
              </button>
              <button type="submit" className="save">
                Save
              </button>
            </div>
          </form>
        </div>
      </PopupModal>
      {showPopupunsave && <SuccessToast message={showmessage} />}
      {showPopupunsave1 && <ErrorToast message={showmessage} />}
    </>
  );
}
