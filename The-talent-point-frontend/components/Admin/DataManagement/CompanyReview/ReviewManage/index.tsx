import {useRouter} from 'next/router';
import React, {useContext, useEffect, useState} from 'react';
import styles from './style.module.css';
import Image from 'next/image';
import {useGetCompanyDetails} from '@/modules/companies/query/useGetCompanyDetail';
import {TabsProps} from 'antd';
import {AdminReviewContainer} from './AdminReviewContainer';
import {TabsUi} from '@/ui/Tabs';
import {ManageReviewResponse, useGetCompanyManageReview} from '@/modules/admin/query/useGetManageReview';
import AuthContext from '@/Context/AuthContext';

export const ReviewManageContainer = () => {
  const [activeKey, setActiveKey] = useState('all');
  const router = useRouter();
  const {user} = useContext(AuthContext);

  const {data} = useGetCompanyDetails({
    slug: router.query.slug as string,
    user_id: user?.id as number,
  });
  useEffect(() => {
    if (router.query.tab && router.query.tab !== activeKey) {
      setActiveKey(router.query.tab as string);
    }
  }, [router.query.tab]);

  const handleTabChange = (key: string) => {
    setActiveKey(key);
    router.replace({
      pathname: router.pathname,
      query: {...router.query, tab: key},
    });
  };

  const {data: reviewDetails} = useGetCompanyManageReview({
    company_id: parseInt(router.query.slug as string),
  });

  const reviewItems: TabsProps['items'] = [
    {
      key: 'all',
      label: 'All',
      children: <AdminReviewContainer reviewDetails={reviewDetails} type="all" />,
    },
    {
      key: 'approved',
      label: 'Approved',
      children: (
        <AdminReviewContainer
          reviewDetails={{
            ...(reviewDetails as ManageReviewResponse),
            reviews: reviewDetails?.reviews.filter(review => review.is_approve === 1) || [],
          }}
          type="approved"
        />
      ),
    },
    {
      key: 'pending',
      label: 'Pending Approval',
      children: (
        <AdminReviewContainer
          reviewDetails={{
            ...(reviewDetails as ManageReviewResponse),
            reviews: reviewDetails?.reviews.filter(review => review.is_approve === 0) || [],
          }}
          type="pending"
        />
      ),
    },
    {
      key: 'reported',
      label: 'Reported',
      children: (
        <AdminReviewContainer
          reviewDetails={{
            ...(reviewDetails as ManageReviewResponse),
            reviews: reviewDetails?.reviews.filter(review => review.report_reviews.length > 0) || [],
          }}
          type="reported"
        />
      ),
    },
  ];

  return (
    <div className={styles.review_manage_container}>
      <div className={styles.header_container}>
        <div className={styles.back_arrow_container}>
          <img
            src={'/icons/data-management/back-arrow.svg'}
            height={13}
            width={13}
            alt="back-arrow"
            onClick={() => router.back()}
          />
        </div>
        <h4>{data?.company?.company_name}</h4>
      </div>
      <div className={styles.company_container}>
        <div className={styles.company_heading}>
          <div className={styles.heading_left}>
            <img
              src={data?.company?.company_logo || '/images/placeholder.jpg'}
              alt="Company Logo"
              width={50}
              height={50}
            />
            <div>
              <h6>{data?.company?.company_name}</h6>
              <div className={styles.company_portfolio}>
                <img src={'/icons/portfolio.svg'} alt="website" height={16} width={16} />
                <a href={data?.company?.company_website} target="_blank" rel="noreferrer">
                  {data?.company?.company_website}
                </a>
              </div>
            </div>
          </div>
          <div className={styles.social_link_container}>
            {data?.company?.twitter_link && (
              <a href={data?.company?.twitter_link} target="_blank" rel="noreferrer">
                <img src={'/icons/prime_twitter.svg'} alt="twitter" height={24} width={24} />
              </a>
            )}
            {data?.company?.facebook_link && (
              <a href={data?.company?.facebook_link} target="_blank" rel="noreferrer">
                <img src={'/icons/prime_facebook.svg'} alt="facebook" height={24} width={24} />
              </a>
            )}
            {data?.company?.linkedin_link && (
              <a href={data?.company?.linkedin_link} target="_blank" rel="noreferrer">
                <img src={'/icons/prime_linkedin.svg'} alt="linkedin" height={24} width={24} />
              </a>
            )}
          </div>
        </div>
        <div className={styles.card_container}>
          {!!reviewDetails?.company_avg_rating && (
            <div className={styles.card}>
              <h4 className={styles.card_review}>
                {reviewDetails?.company_avg_rating}{' '}
                <span className={styles.star}>
                  <svg width={18} height={18} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="change star" clip-path="url(#clip0_8508_36677)">
                      <g id="Group 1000002031">
                        <g id="starboy">
                          <path
                            id="Vector"
                            d="M17.9602 6.92666C17.6842 6.06348 16.1651 5.86626 14.5566 5.6534C13.675 5.53775 12.6733 5.40702 12.371 5.18298C12.0666 4.96062 11.6419 4.04604 11.2665 3.23761C10.5838 1.76768 9.93737 0.381005 9.0334 0.37877L9.01664 0.375977C8.12049 0.391061 7.46738 1.76098 6.77292 3.21526C6.39022 4.01922 5.955 4.93045 5.65163 5.15113C5.3449 5.36958 4.34316 5.4925 3.45875 5.59976C1.85195 5.79531 0.330624 5.97856 0.046807 6.83727C-0.23701 7.69598 0.873115 8.75136 2.04972 9.86651C2.69558 10.48 3.42691 11.175 3.542 11.5325C3.65709 11.8901 3.4621 12.8823 3.2917 13.7545C2.97995 15.3434 2.68552 16.8479 3.41406 17.386C3.58837 17.5106 3.80738 17.5737 4.06605 17.5681C4.80967 17.5564 5.87957 16.9776 6.91371 16.4166C7.68862 15.9931 8.56745 15.5155 8.95854 15.511C9.33677 15.511 10.2184 16.0021 10.9961 16.4351C12.0537 17.0223 13.1482 17.6301 13.8929 17.6178C14.1253 17.6145 14.3237 17.5547 14.4857 17.4379C15.2187 16.91 14.9394 15.406 14.6449 13.8109C14.4829 12.9371 14.3013 11.9426 14.4159 11.5851C14.5343 11.2292 15.274 10.5431 15.926 9.93411C17.1132 8.82957 18.234 7.78984 17.9602 6.92666Z"
                            fill={'#0EB1D2'}
                          />
                        </g>
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_8508_36677">
                        <rect width="18" height="18" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
              </h4>
              <span className={styles.no_of_review}>{reviewDetails?.reviews?.length} reviews</span>
            </div>
          )}
          {data?.company?.sector?.sector_name && (
            <div className={styles.card}>
              <h4 className={styles.card_title}>INDUSTRY</h4>
              <span className={styles.card_content}>{data?.company?.sector?.sector_name}</span>
            </div>
          )}

          {data?.company?.country?.country_name && (
            <div className={styles.card}>
              <h4 className={styles.card_title}>LOCATION</h4>
              <span className={styles.card_content}>{data?.company?.company_name}</span>
            </div>
          )}

          {data?.company?.no_of_employees && (
            <div className={styles.card}>
              <h4 className={styles.card_title}>Company size</h4>
              <span className={styles.card_content}>{data?.company?.no_of_employees}</span>
            </div>
          )}
        </div>

        <div className={styles.divider}></div>

        <div className={styles.rating_container}>
          <h5 className={styles.titles}>Ratings and reviews</h5>

          <div className={styles.tabContainer}>
            <TabsUi
              activeKey={activeKey}
              items={reviewItems as any}
              variant="button"
              className={styles.tabs}
              onChange={handleTabChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
