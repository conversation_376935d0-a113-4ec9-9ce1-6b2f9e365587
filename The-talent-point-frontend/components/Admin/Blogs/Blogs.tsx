import React, {useState, useEffect, useContext} from 'react';
import Link from 'next/link';
import swal from 'sweetalert';
import Image from 'next/image';
import {Button, Col, Form, Input, Pagination, PaginationProps, Row, Select, Space, notification} from 'antd';
import {getAllBlogs, EditAndSaveBlogData, getAllBlogCategories, deleteBlog} from '@/lib/adminapi';
// import { HtmlEditor } from '@/components/Common/HtmlEditor';
import {QuillHtmlEditor} from '@/components/Common/QuillHtmlEditor';
import ModalForm from '@/components/Common/ModalForm';
import {PlusOutlined} from '@ant-design/icons';
import AuthContext from '@/Context/AuthContext';
import ErrorHandler from '@/lib/ErrorHandler';
import {getAllAuthors} from '@/lib/frontendapi';
import {type Blogs} from '@/lib/types';
import LoadingIndicator from '@/components/Common/LoadingIndicator';
import BlogContentSection from './BlogContentSection';
import {appendValueForBlogContent, appendValueForJobTags} from '@/lib/constant';
import {useForm, Controller} from 'react-hook-form';
import JobSearchFieldSection from './JobSearchFieldSection';
import axios from 'axios';

import {getCountries} from '@/lib/ApiAdapter';
import {getalljobs} from '@/lib/adminapi';
import {Country} from '@/lib/types';
import Cookies from 'js-cookie';
import BlogCtaSection from './CtaContentSection';

interface BlogCategory {
  id?: string;
  category_name?: string;
}
type BlogContentType = {
  article_content_link: string;
  article_content_desc: string;
};
type JobSearchTagsType = {
  keyword: string;
  country: string;
  name?: string;
};
interface FormValues {
  name?: string;
  // articles?: BlogContentType[],
  // job_searches?: JobSearchTagsType[],
  blog_category_id?: string | number;
  tag?: string;
  author_id?: string | number;
  meta_desc?: string;
  meta_tag?: string;
  slug?: string;
  // heading_section_content?: string
  // blog_cta: any;
  heading?: string;
  content?: string;
}

export enum BlogErrorCode {
  BLOG_SLUG_DUPLICATE = 'BLOG_SLUG_DUPLICATE',
}

export default function Blogs() {
  const {
    register,
    reset,
    handleSubmit,
    formState: {errors},
    control,
    watch,
    setError,
  } = useForm<FormValues>();
  // {
  //   defaultValues: {
  //     articles: [
  //       {
  //         article_content_link: "",
  //         article_content_desc: ""
  //       }
  //     ],
  //     job_searches: [
  //       {
  //         keyword: "",
  //         country: ""
  //       }
  //     ],
  //     blog_cta: [
  //       {
  //         title: "",
  //         description: "",
  //         link_name: "",
  //         link_url: "",
  //         position: "",
  //         status: ""
  //       }
  //     ]
  //   }
  // }
  const [blogs, setAllBlogs] = useState<Blogs[] | any>([]);
  const [modalConfirm, setModalConfirm] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [showImage, setShowImage] = useState(true);
  const [image, setBlogImage] = useState('');
  const [description, setBlogDesc] = useState<string>('');
  const [newBlogImage, setBlogNewImage] = useState('');
  const [blogCategory, setBlogCategory] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const {user} = useContext(AuthContext);
  const [author, setAuthor] = useState([]);
  const [formData, setFormData] = useState<Blogs | any>({});
  const [page, setPage] = useState(1);
  const [reload, setReload] = useState(false);
  const [sortBy, setSortBy] = useState<string>();
  const [searchName, setSearchName] = useState<string>();
  const [pageSize, setPageSize] = useState(10);
  const [id, setId] = useState(null);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [countries, setCountries] = useState<Country[]>();
  const [keyword, setKeyword] = useState<any>();

  const userId = Cookies.get('user_id');

  watch();

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    getCountries(undefined, undefined, cancelTokenSource)
      .then(res => {
        setCountries(res);
      })
      .catch(error => {
        console.error(error);
      });

    return cancelTokenSource.cancel;
  }, []);

  useEffect(() => {
    getalljobs()
      .then(res => {
        setKeyword(res);
      })
      .catch(error => {
        console.error(error);
      });
  }, []);

  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setPage(current);
    setPageSize(pageSize);
  };

  useEffect(() => {
    setLoading(true);
    getAllBlogs({page: page, pageSize: pageSize, name: searchName, status})
      .then(res => {
        setLoading(false);
        setAllBlogs(res.data);
      })
      .catch(error => {
        setLoading(false);
        console.error(error);
      });
  }, [reload, sortBy, page, searchName, pageSize]);

  useEffect(() => {
    getAllBlogCategories()
      .then(res => {
        if (res.status == true) {
          setBlogCategory(res.data);
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
    getAllAuthors().then(res => {
      setAuthor(res.data);
    });
  }, []);

  useEffect(() => {
    let fields = watch();
  }, []);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return date.toLocaleDateString(undefined, options);
  };

  const handleCancel = () => {
    setModalConfirm(false);
    setIsEdit(false);
  };

  const handleImageChange = (event: any) => {
    event.preventDefault();
    const selectedFile = event.target.files[0];
    setBlogImage(selectedFile);

    // if (selectedFile.size > 1.5 * 1024 * 1024) {
    //   notification.error({ message: 'Image size should be less than or equal to 1.5MB' });
    //   return;
    // }

    if (selectedFile.size > 150 * 1024) {
      notification.error({message: 'Image size should be less than or equal to 150KB'});
      return;
    }

    if (selectedFile.type.includes('image')) {
      if (selectedFile) {
        setPreviewImage(URL.createObjectURL(selectedFile));
        setShowImage(false);
        if (newBlogImage) {
          setBlogNewImage('');
        }
      } else {
        setShowImage(true);
      }
    } else {
      notification.error({message: 'Please upload an image file (JPEG,JPG,PNG).'});
    }
  };

  const resetForm = () => {
    const defaultFormData = {};

    reset(defaultFormData);
    setFormData(defaultFormData);
  };

  const handleEdit = (id: any) => {
    const blog = blogs.data?.filter((record: any) => record.id === id);
    setId(id);
    setIsEdit(true);

    const desc = blog[0]?.description === null ? '' : blog[0]?.description;
    const head = blog[0]?.heading === null ? '' : blog[0]?.heading;

    const formattedBlogData: FormValues = {
      name: blog[0].name || '',
      // articles: blog[0].articles.length ? blog[0].articles : [{ article_content_link: "", article_content_desc: "" }],
      // job_searches: blog[0].job_searches.length ? blog[0].job_searches : [{ keyword: "", country: "" }],
      blog_category_id: blog[0].blog_category_id || '',
      tag: blog[0].tag || '',
      author_id: blog[0].author_id || '',
      meta_desc: blog[0].meta_desc || '',
      meta_tag: blog[0].meta_tag || '',
      slug: blog[0].slug || '',
      content: head + desc || '',

      // blog_cta: blog[0].blog_cta.length ? blog[0].blog_cta : [
      //   {
      //     title: "",
      //     description: "",
      //     link_name: "",
      //     link_url: "",
      //     position: "",
      //     status: ""
      //   }
      // ],
    };

    // Update form fields directly
    reset(formattedBlogData);
    setPreviewImage(blog?.image ? URL.createObjectURL(blog.image) : '');
    setModalConfirm(true);
  };

  const handleDeleteBlog = (id: any) => {
    swal({
      title: 'Are you sure?',
      text: 'You want to delete the Blog',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        const data = {
          id: id,
        };
        deleteBlog(data)
          .then(res => {
            if (res.status === true) {
              notification.success({message: res.message});
              setReload(!reload);
            } else {
              notification.error({message: 'Deletion failed'});
            }
          })
          .catch(error => {
            ErrorHandler.showNotification(error);
          });
      } else {
        notification.error({message: 'Deletion Cancelled'});
      }
    });
  };

  const appendValue = (fieldName: string, valueToAppend: any) => {
    setFormData({
      ...formData,
      [fieldName]: [...formData[fieldName], {...valueToAppend}],
    });
  };

  const onSubmit = async (values: any) => {
    // if (values.job_searches && Array.isArray(values.job_searches)) {
    //   const updatedJobSearchTags = values.job_searches.map((tag: any) => {
    //     const keyword = String(tag.keyword);
    //     const title = `${tag.keyword} jobs in ${tag.country}`;
    //     return { ...tag, keyword, id: tag.id !== undefined ? tag.id : null, title };
    //   });

    //   const updatedArticles = values.articles.map((tag: any) => {
    //     return { ...tag, id: tag.id !== undefined ? tag.id : null };
    //   });

    values.created_by_id = userId;
    // values.job_searches = updatedJobSearchTags;
    // values.articles = updatedArticles;
    values.id = id ? id : undefined;
    // }
    try {
      const res = await EditAndSaveBlogData(values, image);

      if ((res.status = true)) {
        setModalConfirm(false);
        setIsEdit(false);
        notification.success({message: 'Blog Data Saved Successfully'});
        setLoading(true);
        getAllBlogs({page: page, pageSize: pageSize, name: searchName, status})
          .then(res => {
            setLoading(false);
            setAllBlogs(res.data);
          })
          .catch(error => {
            setLoading(false);
            console.error(error);
          });
      }
    } catch (error: any) {
      if (error.error_code === BlogErrorCode.BLOG_SLUG_DUPLICATE) {
        setError('slug', {
          message: error.message,
        });
      }
    }
  };

  return (
    <>
      <div className="dash-right">
        <h1 className="data">Blogs</h1>
        <div className="text-end">
          <Space>
            <Input.Search
              size={'large'}
              placeholder="Search blog here.."
              onChange={e => {
                setSearchName(e.target.value);
              }}
            />
            <Button
              size={'large'}
              type="primary"
              onClick={() => {
                setPreviewImage('');
                resetForm();
                reset();
                setModalConfirm(true);
                setIsEdit(false);
              }}
              icon={<PlusOutlined />}>
              Add Blog
            </Button>
          </Space>
        </div>
        <div className="table-part mt-4 admin-tab-table">
          <LoadingIndicator visible={loading} />
          <table className="rwd-table">
            <tbody>
              <tr>
                <th>Name</th>
                <th>Author Name</th>
                <th>Date</th>
                <th className="text-end">MANAGE</th>
              </tr>
              {blogs &&
                blogs?.data?.map((blog: any, index: any) => (
                  <tr key={index}>
                    <td data-th="Name">
                      <Link prefetch={false} href={`/blog/${blog.slug}`}>
                        <p className="location">{blog.name?.slice(0, 60)}</p>
                      </Link>
                    </td>

                    <td data-th="Author Name">
                      <p className="location">{blog.author?.name ?? blog.author_name}</p>
                    </td>

                    <td data-th="Date">
                      <p className="location">{formatDate(blog.created_at || '')}</p>
                    </td>

                    <td data-th="MANAGE" className="text-end">
                      <div className="d-flex align-items-center gap-2">
                        <i className="fa-solid fa-pencil edit-pencil" onClick={() => handleEdit(blog.id)}></i>
                        <i className="fa-regular fa-trash-can del-trash" onClick={() => handleDeleteBlog(blog.id)}></i>
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        <div className="pagination-wrapper mt-4">
          <div className="pagination-wrapper">
            <Pagination
              pageSize={blogs?.per_page || 1}
              onChange={onShowSizeChange}
              total={blogs?.total}
              current={blogs?.current_page}
              showSizeChanger
              onShowSizeChange={onShowSizeChange}
            />
          </div>
        </div>
        <ModalForm title={isEdit ? 'Edit blog' : 'Add blog'} open={modalConfirm} onCancel={handleCancel}>
          {/* <Form layout="vertical" size="large" onFinish={handleBlogSubmit} initialValues={formData}> */}
          <form className="blog-form" onSubmit={handleSubmit(onSubmit)}>
            {/* <Form.Item name={'name'} label={'Blog Name'} rules={[{ required: true }]}>
              <Input placeholder="Enter Title here" />
            </Form.Item> */}

            <label className="blog-label"> Blog Name</label>
            <input
              className="blog-field ant-input ant-input-outlined"
              placeholder="Enter Title here"
              {...register('name', {required: 'Please enter blog name'})}
            />
            {errors?.name && <p className="error-field">{errors?.name?.message}</p>}

            <Row gutter={15}>
              <Col md={12}>
                {/* <Form.Item label={'Blog Category'} name={'blog_category_id'} rules={[{ required: true }]}>
                <Select
                  className='blog-select'
                  placeholder={'Select Blog Category'}
                  options={blogCategory.map((b, index) => {
                    return { value: b.id, label: b.category_name };
                  })}
                />
                </Form.Item> */}
                <label className="blog-label"> Blog Category</label>
                <Controller
                  control={control}
                  name={`blog_category_id`}
                  rules={{required: 'Please enter blog category'}}
                  render={({field}) => (
                    <Select
                      {...field}
                      onChange={selectedOption => field.onChange(selectedOption)}
                      className="blog-select"
                      placeholder={'Select Blog Category'}
                      options={blogCategory.map((b, index) => {
                        return {value: b.id, label: b.category_name};
                      })}
                    />
                  )}
                />

                {errors?.blog_category_id && <p className="error-field">{errors?.blog_category_id?.message}</p>}
              </Col>
              <Col md={12}>
                {/* <Form.Item name={'tag'} label={'Tags'} rules={[{ required: true }]}>
                  <Input placeholder="Tags: (Management,Payroll)" />
                </Form.Item> */}
                <div className="blog-field">
                  <label className="blog-label d-block"> Tags</label>
                  <input
                    {...register('tag', {required: 'Please enter tags'})}
                    className="blog-field ant-input ant-input-outlined"
                    type="text"
                  />
                  {errors?.tag && <p className="error-field">{errors?.tag?.message}</p>}
                </div>
              </Col>
              <Col md={12}>
                {/* <Form.Item name={'author_id'} label={'Author Name'} rules={[{ required: true }]}>
                  <Select
                    placeholder={'Select author'}
                    options={author.map((item: any, index) => {
                      return { value: item.id, label: item.name };
                    })}
                  />
                </Form.Item> */}
                <label className="blog-label"> Author Name</label>
                <Controller
                  control={control}
                  name={`author_id`}
                  rules={{required: 'Please enter author name'}}
                  render={({field}) => (
                    <Select
                      {...field}
                      onChange={selectedOption => field.onChange(selectedOption)}
                      className="blog-select"
                      placeholder={'Select author'}
                      options={author.map((item: any, index) => {
                        return {value: item.id, label: item.name};
                      })}
                    />
                  )}
                />

                {errors?.author_id && <p className="error-field">{errors?.author_id?.message}</p>}
              </Col>
              <Col md={12}>
                {/* <Form.Item name={'slug'} label={'Blog Slug'} rules={[{ required: true }]}>
                  <Input placeholder="Blog Slug" />
                </Form.Item> */}
                <label className="blog-label"> Blog Slug</label>
                <input
                  className="blog-field ant-input ant-input-outlined"
                  {...register('slug', {required: 'Please enter blog slug '})}
                  placeholder="Blog Slug"
                />
                {errors?.slug && <p className="error-field">{errors?.slug?.message}</p>}
              </Col>
              {/* <BlogContentSection control={control} blogContent={watch("articles")} handleFieldChange={handleFieldChange} handleAppend={handleAppendBlogContent} handleRemove={handleRemove} register={register} errors={errors} /> */}

              <Col md={24}>
                {/* <Form.Item label={'Description'} className='mt-5'>
                  <HtmlEditor value={description} onChange={(name, value) => setBlogDesc(value || '')} />
                </Form.Item> */}
                {/* <label className="blog-label">Heading</label> */}
                {/* <Controller control={control} name={`heading`} rules={{ required: "Please enter header section content" }} render={({ field }) => (
                  <QuillHtmlEditor {...field} onChange={(name, value) => field.onChange(value)} />
                )} /> */}

                {errors?.heading && <p className={`error-field`}>{errors?.heading?.message}</p>}
              </Col>

              <Col md={24}>
                <label className="blog-label">Content</label>
                <Controller
                  control={control}
                  name={`content`}
                  rules={{required: 'Please enter table of content value'}}
                  render={({field}) => <QuillHtmlEditor {...field} onChange={(name, value) => field.onChange(value)} />}
                />
                {errors?.content && <p className={`error-field`}>{errors?.content?.message}</p>}
              </Col>

              <Col md={12}>
                {/* <Form.Item name={'meta_tag'} label={'Meta Tag'}>
                  <Input placeholder="Meta Tag" />
                </Form.Item> */}
                <label className="blog-label"> Meta Tag</label>
                <input
                  className="blog-field ant-input ant-input-outlined"
                  placeholder="Meta Tag"
                  {...register('meta_tag', {required: 'Please enter meta tag'})}
                />
                {errors?.meta_tag && <p className="error-field">{errors?.meta_tag?.message}</p>}
              </Col>
              <Col md={12}>
                {/* <Form.Item name={'meta_desc'} label={'Meta Description'} rules={[{ required: true }]}>
                  <Input placeholder="Meta description" />
                </Form.Item> */}
                <label className="blog-label"> Meta Description</label>
                <input
                  className="blog-field ant-input ant-input-outlined"
                  placeholder="Meta description"
                  {...register('meta_desc', {required: 'Please enter meta description'})}
                />
                {errors?.meta_desc && <p className="error-field">{errors?.meta_desc?.message}</p>}
              </Col>
              {/* <JobSearchFieldSection register={register} control={control} errors={errors} jobSearchTags={watch("job_searches")} countryData={countries} keywordData={keyword?.data} /> */}

              {/* <BlogCtaSection control={control} blogCta={watch("blog_cta")} register={register} errors={errors} /> */}
            </Row>
            <label className="f-14 w-700 c-333333 w-100 mb-2 mt-4"> Blog Image</label>

            <div className="row">
              <div className="col-lg-2 col-md-3 col-3">
                {
                  <div className="logo-box-data">
                    {previewImage ? (
                      <img src={previewImage} alt="Preview" className="" height={70} width={70} />
                    ) : formData?.image ? (
                      <img
                        src={process.env.NEXT_PUBLIC_IMAGE_URL + 'images/blogs/' + formData?.image}
                        alt="logo-cir"
                        className=""
                        height={70}
                        width={70}
                      />
                    ) : (
                      <div className="logo-box-data">
                        <img
                          src={process.env.NEXT_PUBLIC_BASE_URL + 'images/logo-cir.png'}
                          alt="logo-cir"
                          className=""
                          width={70}
                          height={70}
                        />
                      </div>
                    )}
                  </div>
                }
              </div>
              <div className="col-lg-8 col-md-6 col-sm-12 col-9">
                <a>
                  <div className="uploade-btn w-50 m-w-100">
                    <input
                      type="file"
                      name="logo"
                      id="logo"
                      onChange={handleImageChange}
                      accept="image/jpeg, image/png"
                    />
                    <div className="btn-a primary-size-16 upload-text-m-13 btn-bg-0055BA tab-w-100">
                      <i className="fa-solid fa-upload mx-2"></i> Upload Blog Image
                    </div>
                  </div>
                </a>
                <p className="upload-text-m-13" style={{fontSize: '11px', marginTop: '5px'}}>
                  <strong>
                    <i>Note:</i>
                  </strong>{' '}
                  Image size should be less than 150KB
                </p>
              </div>
            </div>

            <div className="text-right">
              <Space>
                <Button onClick={handleCancel} size={'large'}>
                  Cancel
                </Button>
                <Button loading={loading} htmlType="submit" type={'primary'} size={'large'}>
                  Save
                </Button>
              </Space>
            </div>
          </form>
          {/* </Form> */}
        </ModalForm>
      </div>
    </>
  );
}
