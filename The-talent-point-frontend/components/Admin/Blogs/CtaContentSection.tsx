import { Col, But<PERSON>, Select, Input } from "antd";
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import React, { Fragment } from "react";
import { Controller, useFieldArray } from "react-hook-form";

const { Option } = Select;

interface BlogCtaSectionProps {
  blogCta: any;
  control: any;
  register: any;
  errors: any;
}

const BlogCtaSection = ({ blogCta, control, register, errors }: BlogCtaSectionProps) => {
  const { fields, remove, append } = useFieldArray({
    control,
    name: "blog_cta"
  });

  const handleCtaAppend = () => {
    const valueToAppend = {
      title: "",
      description: "",
      link_name: "",
      link_url: "",
      position: "",
      status: ""
    };
    append(valueToAppend);
  };

  return (
    <>
      {fields.map((field, index) => (
        <Fragment key={field.id}>
          <Col md={24}>
            <label className="cta-label">Title</label>
            <input
              className="cta-field ant-input ant-input-outlined"
              placeholder="Enter CTA title"
              {...register(`blog_cta.${index}.title`, { required: "Please enter CTA title" })}
            />
            {errors?.blog_cta?.[index]?.title && (
              <p className="error-field">{errors.blog_cta[index].title.message}</p>
            )}
          </Col>
          <Col md={24}>
            <label className="cta-label">Description</label>
            <Controller
              control={control}
              name={`blog_cta.${index}.description`}
              rules={{ required: "Please enter CTA description" }}
              render={({ field }) => (
                <Input.TextArea {...field} placeholder="Enter CTA description" />
              )}
            />
            {errors?.blog_cta?.[index]?.description && (
              <p className="error-field">{errors.blog_cta[index].description.message}</p>
            )}
          </Col>
          <Col md={12}>
            <label className="cta-label">Link Name</label>
            <input
              className="cta-field ant-input ant-input-outlined"
              placeholder="Enter link name"
              {...register(`blog_cta.${index}.link_name`, { required: "Please enter link name" })}
            />
            {errors?.blog_cta?.[index]?.link_name && (
              <p className="error-field">{errors.blog_cta[index].link_name.message}</p>
            )}
          </Col>
          <Col md={12}>
            <label className="cta-label">Link URL</label>
            <input
              className="cta-field ant-input ant-input-outlined"
              placeholder="Enter link URL"
              {...register(`blog_cta.${index}.link_url`, { required: "Please enter link URL" })}
            />
            {errors?.blog_cta?.[index]?.link_url && (
              <p className="error-field">{errors.blog_cta[index].link_url.message}</p>
            )}
          </Col>
          <Col md={12}>
            <label className="blog-label">Position</label>
            <Controller
              control={control}
              name={`blog_cta.${index}.position`}
              rules={{ required: "Please select a position" }}
              render={({ field }) => (
                <Select
                  {...field}
                  placeholder="Select position"
                  onChange={(value) => field.onChange(value)}
                  className='blog-select'
                >
                  <Option value="left">Left</Option>
                  <Option value="right">Right</Option>
                </Select>
              )}
            />
            {errors?.blog_cta?.[index]?.position && (
              <p className="error-field">{errors.blog_cta[index].position.message}</p>
            )}
          </Col>
          <Col md={12}>
            <label className="blog-label">Status</label>
            <Controller
              control={control}
              name={`blog_cta.${index}.status`}
              rules={{ required: "Please select a status" }}
              render={({ field }) => (
                <Select
                  {...field}
                  placeholder="Select status"
                  onChange={(value) => field.onChange(value)}
                  className='blog-select'
                >
                  <Option value={'1'}>True</Option>
                  <Option value={'0'}>False</Option>
                </Select>
              )}
            />
            {errors?.blog_cta?.[index]?.status && (
              <p className="error-field">{errors.blog_cta[index].status.message}</p>
            )}
          </Col>
          {blogCta?.length !== 1 && (
            <Col md={2}>
              <Button
                onClick={() => remove(index)}
                className="delete-button"
                type="text"
                icon={<DeleteOutlined className="delete-icon" />}
              />
            </Col>
          )}
        </Fragment>
      ))}
      <Button
        onClick={handleCtaAppend}
        className="add-button"
        type="text"
        icon={<PlusCircleOutlined className="plus-icon" />}
      />
    </>
  );
};

export default BlogCtaSection;
