.candidate_container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 0px 0 16px;
}
.candidate_header {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: space-between;
}
.candidate_header_content {
  display: flex;
  align-items: center;
  gap: 8px;
}
.candidate_header_content h3 {
  color: #191919;
  font-size: 31px;
  font-weight: 500;
  line-height: 120%;
}

.candidate_header_content span {
  display: flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 9999px;
  border: 0.5px solid rgba(0, 85, 186, 0.08);
  background: #cfe5ff;
  color: var(--Grayscale-08, #2c2c2c);
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.candidate_header_action {
  display: flex;
  align-items: center;
  gap: 8px;
}
.candidate_header_action button {
  display: flex;
  height: 40px;
  padding: 11px 12px 11px 16px;
  justify-content: center;
  align-items: center;
  color: #0055ba;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%; /* 19.2px */
  border-radius: 8px;
  border: 1px solid #0055ba;
  background-color: transparent;
}
.candidate_header_action .filter_container {
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid rgba(0, 85, 186, 0.08);
  background: #cfe5ff;
  cursor: pointer;
}
.filter_icon {
  position: relative;
}
.filter_dot {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #fdca40;
  height: 8px;
  width: 8px;
  border-radius: 50%;
}
.candidate_section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-radius: 8px 8px 0px 0px;
  background: #f9f9f9;
  min-height: calc(100vh - 500px);
}
.select_all_container {
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}
.candidate_info_container {
  display: flex;
  padding: 0px 8px;
  gap: 12px;
}
.candidate_card_container {
  display: flex;
  width: 416px;
  flex-direction: column;
  gap: 8px;
}
.no_selected_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
  text-align: center;
  padding-top: 99.5px;
  border-radius: 8px 8px 0px 0px;
  border-top: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  border-left: 1px solid #d9d9d9;
  background: #fff;
}
.no_selected_title {
  color: #4d4d4d;
  font-family: 'Open Sans';
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.pagination_container {
  padding-top: 20px;
  border-top: 1px solid #d9d9d9;
  margin-top: 32px;
  padding-bottom: 30px;
}
.chip_container {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 4px;
  flex-wrap: wrap;
  cursor: pointer;
}
.chip {
  display: flex;
  padding: 9px 14px;
  align-items: center;
  gap: 8px;
  border-radius: 9999px;
  background: #191919;
  color: #f9f9f9;
  font-size: 16px;
  line-height: 140%;
}
.chip img {
  cursor: pointer;
}
.no_candidate_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
  text-align: center;
  padding-top: 99.5px;
}
.candidate_info_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px 4px 20px;
  align-items: center;
}
.selectUser_container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}
.selectUser_container p {
  color: #0070f5;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  cursor: pointer;
}

.selectUser_container span {
  color: #747474;
  font-size: 16px;
  line-height: 140%;
}
.backContainer {
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 8px;
  cursor: pointer;
}
.backContainer p {
  color: #0070f5;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.backContainer img {
  cursor: pointer;
}
.selected_collection {
  font-size: 26px !important;
}
.no_available_candidate {
  height: 1254px;
  padding: 100px 294.5px 983px 294.5px;
  flex: 1 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.no_available_candidate h3 {
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
  padding: 8px 0 4px 0;
}

.no_available_candidate p {
  color: #747474;
  line-height: 140%;
  padding-bottom: 16px;
}

.no_available_candidate button {
  border-radius: 8px;
  border: 1px solid var(--Grayscale-03, #d9d9d9);
  background: var(--Grayscale-White, #fff);
  display: flex;
  height: 40px;
  padding: 11px 12px 11px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
