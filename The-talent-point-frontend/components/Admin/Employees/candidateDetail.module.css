.job_detail_section_card {
  border-radius: 8px 8px 0px 0px;
  border-top: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  border-left: 1px solid #d9d9d9;
  background: #fff;
  width: 100%;
  padding: 12px 12px 47px 12px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.candidate_detail_header {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.candidate_detail_header_info {
  display: flex;
  padding-bottom: 16px;
  flex-direction: column;
  gap: 16px;
  border-bottom: 1px solid #eee;
}
.candidate_detail_header_content {
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.candidate_detail_header_personal_info {
  display: flex;
  justify-content: space-between;
}
.header_left {
  display: flex;
  gap: 16px;
}
.candidate_detail_profile_image {
  width: 100px;
  height: 124.992px;
  /* background: lightgray 50% / cover no-repeat; */
  border-radius: 12px;
}
.user_name {
  overflow: hidden;
  color: #2c2c2c;
  text-overflow: ellipsis;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
  max-width: 180px;
}
.user_position {
  overflow: hidden;
  color: #0055ba;
  text-overflow: ellipsis;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
  max-width: 220px;
  padding-bottom: 8px;
}
.user_location {
  padding-bottom: 8px;
}
.user_location,
.user_personal_info,
.user_dob,
.user_gender {
  display: flex;
  align-items: center;
  gap: 4px;
}

.user_location,
.user_personal_info,
.user_dob,
.user_gender span {
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}

.user_personal_info {
  display: flex;
  align-items: center;
  gap: 16px;
}
.user_personal_info span {
  color: #999;
}
.user_detail_action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
}
.user_status_and_link {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}
.user_status {
  display: flex;
  padding: 4px 8px;
  align-items: center;
  gap: 6px;
  border-radius: 4px;
  border: 1px solid #3d9f79;
  background: #dcf2ea;
}
.user_status p {
  color: #0c5a14;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.view_profile_link {
  display: flex;
  width: 25px;
  height: 25px;
  padding: 4px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  background: #eee;
  cursor: pointer;
}
.view_resume_section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}
.view_resume_button button {
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: #0070f5;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  line-height: 120%;
}
.active_status {
  color: #999;
  font-size: 12px;
  font-weight: 300;
  line-height: 140%;
}
.candidate_detail_header_contact_info {
  display: flex;
  align-items: center;
  gap: 8px;
}
.candidate_detail_header_contact_info div {
  display: flex;
  padding: 8px 4px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 8px;
  background: #ebf4ff;
  color: #0070f5;
  font-size: 14px;
  font-weight: 600;
  line-height: 140%;
}
.candidate_detail_header_content {
  color: #4d4d4d;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.work_experience_container,
.education_container,
.skills_container,
.candidate_portfolio,
.portfolio_container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.education_container,
.skills_container {
  gap: 8px !important;
}
.work_experience_title,
.education_text,
.skill_title,
.language_title,
.portfolio_title {
  font-size: 12px;
  line-height: 140%;
}
.candidate_work_experience {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.work_experience_position,
.education_degree,
.portfolio_title,
.language_name {
  color: #2c2c2c;
  font-size: 18px;
  font-weight: 700;
  line-height: 160%;
  padding-bottom: 4px;
}
.work_experience_company,
.education_title,
.language_proficiency {
  color: #0055ba;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
.work_experience_duration,
.education_duration,
.portfolio_duration {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-top: 8px;
}

.work_experience_duration p,
.education_duration p,
.portfolio_duration p {
  color: #999;
  font-size: 16px;
  line-height: 140%;
}

.skills_list {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}
.skill_item {
  display: flex;
  padding: 6px 20px;
  align-items: center;
  gap: 8px;
  border-radius: 28px;
  background: rgba(0, 85, 186, 0.04);
  color: #0055ba;
  font-size: 16px;
  line-height: 140%;
}
.candidate_portfolio {
  gap: 8px !important  ;
}
.portfolio_link {
  display: flex;
  align-items: center;
  gap: 4px;
}
.portfolio_link a {
  color: #0070f5;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}
