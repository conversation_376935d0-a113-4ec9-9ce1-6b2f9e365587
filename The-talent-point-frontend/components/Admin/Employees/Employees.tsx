import React, {useState, useEffect, useContext} from 'react';
import Link from 'next/link';
import swal from 'sweetalert';
import {useRouter} from 'next/router';
import Image from 'next/image';
import axios from 'axios';
import {Button, Pagination, PaginationProps, notification} from 'antd';
import moment from 'moment';
import {useForm} from 'react-hook-form';
import * as XLSX from 'xlsx';
import {deleteuser, getInActiveEmployee, getAllinactiveUsers, Activateuser} from '@/lib/adminapi';
import {getSingleUserDetails, sendMessage, getFirstMessageCheckCount} from '@/lib/frontendapi';
import {PaginationMeta, User} from '@/lib/types';
import ErrorHandler from '@/lib/ErrorHandler';
import PopupModal from '../../../components/Common/PopupModal';
import LoadingIndicator from '@/components/Common/LoadingIndicator';
import AuthContext from '@/Context/AuthContext';

export default function Employees() {
  const {
    register,
    formState: {errors},
    handleSubmit,
  } = useForm({
    mode: 'onChange',
  });
  const {user} = useContext(AuthContext);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [page, setPage] = useState(1);
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta>();
  const [reload, setReload] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>();
  const [inactiveUsers, setInactiveUsers] = useState<User[]>([]);
  const [modalCandidateProfilePopup, setModalCandidateProfilePopup] = useState(false);
  const [singleuser, setSingleUser] = useState<any>([]);
  const [activeTab, setActiveTab] = useState('home');
  const [modalCandidateMessagePopup, setModalCandidateMessagePopup] = useState(false);
  const [messageDesc, setMessageDesc] = useState('');
  const [firstMessageCount, setFirstMessageCount] = useState(0);
  const router = useRouter();
  const [pageSize, setPageSize] = useState(10);
  const [loading, setLoading] = useState(false);

  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setPage(current);
    setPageSize(pageSize);
  };

  const modalConfirmCandidateProfilePopupClose = () => {
    setModalCandidateProfilePopup(false);
  };

  useEffect(() => {
    setLoading(true);
    const cancelTokenSource = axios.CancelToken.source();
    const config = {
      cancelToken: cancelTokenSource.token,
      params: {page: page, pageSize: pageSize, status: statusFilter},
    };

    axios
      .get(`users/employees`, config)
      .then(response => {
        if (response) {
          setAllUsers(response.data.data);
          setPaginationMeta(response.data.meta);
        }
        setLoading(false);
      })
      .catch(e => {
        ErrorHandler.showNotification(e);
      });

    return cancelTokenSource.cancel;
  }, [reload, page, pageSize, statusFilter]);

  const handleExportExcel = () => {
    const dataToExport = allUsers.map(user => ({
      Name: user.name,
      Email: user.email,
      ContactNO: user.contact_no,
      Gender: user.gender,
      Location: user.country_name,
      Position: user.current_position,
      Status: user.job_status,
      UserStatus: user.status,
      // Add other properties you want to include in the Excel file
    }));
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'JobsData');

    XLSX.writeFile(workbook, 'candidate_data.xlsx'); // Use XLSX.writeFile to trigger the file download
  };

  const handleInactiveExportExcel = () => {
    const dataToExport = inactiveUsers.map(user => ({
      Name: user.name,
      Email: user.email,
      ContactNO: user.contact_no,
      Gender: user.gender,
      Location: user.country_name,
      Position: user.current_position,
      Status: user.job_status,
      UserStatus: user.status,
    }));
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'JobsData');

    XLSX.writeFile(workbook, 'candidate_data.xlsx'); // Use XLSX.writeFile to trigger the file download
  };

  const handleDelete = (e: any, id: any) => {
    e.preventDefault();
    swal({
      title: 'Are you sure?',
      text: 'You want to delete your User',
      icon: 'warning',
      dangerMode: true,
      buttons: ['Cancel', 'Yes, I am sure!'],
    }).then(willDelete => {
      if (willDelete) {
        deleteuser(id, 'deleted')
          .then(res => {
            if (res.status === true) {
              swal('User has been deleted!', {
                icon: 'success',
              });
              getAllinactiveUsers()
                .then(response => {
                  if (response.status == true) setInactiveUsers(response.data);
                })
                .catch(error => {
                  console.error(error);
                });
              setReload(!reload);
            } else {
              notification.error({message: res.message});
            }
          })
          .catch(err => {
            ErrorHandler.showNotification(err);
          });
      }
    });
  };

  const modalConfirmCandidateProfilePopupOpen = (e: any, id: any) => {
    setModalCandidateProfilePopup(true);
    getSingleUserDetails(id)
      .then(res => {
        if (res.status == true) {
          setSingleUser(res.user);
        }
      })
      .catch(err => {
        console.log(err);
      });
    const data = {
      sender_id: user?.id,
      receiver_id: id,
    };
    getFirstMessageCheckCount(data)
      .then(res => {
        if (res.status == true) {
          setFirstMessageCount(res.total_message_count);
        } else {
          setFirstMessageCount(0);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };
  const modalConfirmCandidateMessagePopupOpen = () => {
    setModalCandidateMessagePopup(true);
  };
  const modalConfirmCandidateMessagePopupClose = () => {
    setModalCandidateMessagePopup(false);
  };
  const submitMessageForm = (data: any) => {
    let candidate_id = $('.hd_candidate_id').val();
    let current_user_id = user?.id;
    const formData = {
      applicants_id: '',
      candidate_id: candidate_id,
      job_id: '',
      current_user_id: current_user_id,
      message_title: null,
      message_description: data.message_desc,
      attachment_path: null,
      message_type: 'admin_contact',
      message_status: 'unread',
    };
    sendMessage(formData)
      .then(res => {
        if (res.status == true) {
          notification.success({message: res.message});
          modalConfirmCandidateMessagePopupClose();
        } else {
          notification.error({message: res.message});
        }
      })
      .catch(err => {
        ErrorHandler.showNotification(err);
      });
  };

  return (
    <>
      <div className="dash-right">
        <h1>Candidates</h1>
        <div className="tab-filter employees mt-2">
          <nav>
            <div className="nav" id="nav-tab" role="tablist">
              {(activeTab === 'home' || activeTab === 'profile') && (
                <button
                  className="export"
                  id="nav-export-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#nav-home"
                  type="button"
                  role="tab"
                  aria-controls="nav-home"
                  aria-selected="true"
                  onClick={handleExportExcel}>
                  <i className="fa-solid fa-file-arrow-down"></i> Export as .xls
                </button>
              )}

              {activeTab === 'contact' && (
                <button
                  className="export"
                  id="nav-export-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#nav-home"
                  type="button"
                  role="tab"
                  aria-controls="nav-home"
                  aria-selected="true"
                  onClick={handleInactiveExportExcel}>
                  <i className="fa-solid fa-file-arrow-down"></i> Export as .xls
                </button>
              )}
            </div>
          </nav>
        </div>
        <Button.Group size={'large'} className="status_filter_tab_section">
          <Button className={statusFilter == undefined ? 'active' : ''} onClick={() => setStatusFilter(undefined)}>
            All ({paginationMeta?.total})
          </Button>
          <Button className={statusFilter == 'active' ? 'active' : ''} onClick={() => setStatusFilter('active')}>
            Active
          </Button>
          <Button className={statusFilter == 'deleted' ? 'active' : ''} onClick={() => setStatusFilter('deleted')}>
            Inactive
          </Button>
          {/* <Button onClick={() => setStatusFilter('pending')}>Pending</Button> */}
        </Button.Group>

        <div className="tab-content" id="myTabContent">
          <div
            className="tab-pane fade show active admin-tab-table"
            id="home"
            role="tabpanel"
            aria-labelledby="home-tab">
            <LoadingIndicator visible={loading} />
            {allUsers.map((user: any, index: any) => {
              const profileImage = user.profile_image
                ? process.env.NEXT_PUBLIC_IMAGE_URL + 'images/userprofileImg/' + user.profile_image
                : process.env.NEXT_PUBLIC_BASE_URL + 'images/user-p.png';
              return (
                <div className="filter filter-sp m-center mt-4  border-radius-16" key={index}>
                  <div className="row">
                    <div className="col pr-0 mr-1 max-134">
                      <img src={profileImage} alt=" user-p" className="logo-filter w-100" height={122} width={134} />
                    </div>
                    <div className="col-sm-7">
                      <p className="p-18 blue-text mb-2">
                        <Link target="_blank" href={'/candidate-profile/' + user.id}>
                          {user.name}
                        </Link>
                      </p>
                      <p className="p-16 black-text">
                        {user.current_position}
                        {user.company ? '@' + user.company : ''}
                      </p>
                      <ul className="full-time">
                        {user.country?.country_name ? (
                          <li>
                            <i className="fa-solid fa-location-dot"></i> {user.country?.country_name}{' '}
                          </li>
                        ) : (
                          ''
                        )}
                        {user.job_status ? (
                          <li>
                            <i className="fa-solid fa-business-time"></i>{' '}
                            {user.job_status == ' ready_to_interview'
                              ? 'Ready To Interview'
                              : user.job_status == 'open_to_offer'
                                ? 'Open To Offer'
                                : user.job_status == 'not_looking'
                                  ? 'Not Looking'
                                  : 'Ready To interview'}
                          </li>
                        ) : (
                          ''
                        )}
                      </ul>
                      <ul className="full-time">
                        {user.email ? (
                          <li>
                            <i className="fa fa-envelope" aria-hidden="true"></i> {user.email}{' '}
                          </li>
                        ) : (
                          ''
                        )}
                        {user.contact_no && (
                          <li>
                            <i className="fa-solid fa-phone-volume"></i> {user.contact_no}
                          </li>
                        )}
                      </ul>
                    </div>
                    <div className="col-sm-3 text-right m-center">
                      <div className="dropdown">
                        <button
                          className="dropdown-toggle btn-a primary-size-16 btn-bg-0055BA"
                          type="button"
                          id="dropdownMenuButton1"
                          data-bs-toggle="dropdown"
                          aria-expanded="false">
                          <i className="fa-solid fa-circle-chevron-down sp-right"></i> Options
                        </button>
                        <ul className="dropdown-menu view-right" aria-labelledby="dropdownMenuButton1">
                          <li>
                            <Link className="dropdown-item item-1" href={'/candidate-profile/' + user.slug}>
                              View public profile
                            </Link>
                          </li>
                          <li>
                            <a
                              className="dropdown-item item-2"
                              href="#"
                              onClick={(e: any) => modalConfirmCandidateProfilePopupOpen(e, user.id)}>
                              Contact information
                            </a>
                          </li>
                          {user.status != 'deleted' ? (
                            <li>
                              <a className="dropdown-item item-3" href="#" onClick={e => handleDelete(e, user.id)}>
                                Delete profile
                              </a>
                            </li>
                          ) : (
                            ''
                          )}
                        </ul>
                      </div>
                      {user.resumes.length > 0 ? (
                        <button
                          className="download mt-4 max-210 mob-mt-sp"
                          onClick={() => {
                            router.push(
                              process.env.NEXT_PUBLIC_IMAGE_URL +
                                'images/employee/resume/' +
                                user.resumes[0].resume_pdf_path,
                            );
                          }}>
                          <i className="fa-solid fa-download"></i> Download Resume
                        </button>
                      ) : (
                        <>
                          {' '}
                          <br />
                          No resume uploaded
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            {paginationMeta && (
              <Pagination
                pageSize={paginationMeta.per_page || 1}
                onChange={onShowSizeChange}
                total={paginationMeta.total}
                current={paginationMeta.current_page}
                showSizeChanger
                onShowSizeChange={onShowSizeChange}
              />
            )}
          </div>
        </div>

        <PopupModal
          show={modalCandidateProfilePopup}
          handleClose={modalConfirmCandidateProfilePopupClose}
          customclass={'modal-lg  header-remove body-sp-0'}>
          <div className="popup-body candidate_application_popup_body">
            <div className="row">
              <div className="col-sm-8">
                <i
                  className="fa-solid fa-arrow-left f-20 c-0055BA"
                  onClick={modalConfirmCandidateProfilePopupClose}></i>
                <p className="f-37 w-700 c-2C2C2C mb-2">{singleuser.name}</p>
                <p className="f-22  c-0055BA">
                  {singleuser.current_position} @{singleuser.company}
                </p>
              </div>
              <div className="col-sm-4 text-right">
                {firstMessageCount > 0 ? (
                  <Link href={'/admin/messages/inbox/' + singleuser.id}>
                    <button className="btn-a primary-size-16 btn-bg-0055BA w-100">Message</button>
                  </Link>
                ) : (
                  <button
                    className="btn-a primary-size-16 btn-bg-0055BA w-100"
                    onClick={modalConfirmCandidateMessagePopupOpen}>
                    Message
                  </button>
                )}
              </div>
            </div>
            <hr className="hr-line" />
            <div className="tab-popup">
              <ul className="nav nav-pills mb-3" id="pills-tab" role="tablist">
                <li className="nav-item" role="presentation">
                  <button
                    className="nav-link active"
                    id="pills-home-tab"
                    data-bs-toggle="pill"
                    data-bs-target="#pills-home"
                    type="button"
                    role="tab"
                    aria-controls="pills-home"
                    aria-selected="true">
                    Application
                  </button>
                </li>
              </ul>
              <div className="tab-content" id="pills-tabContent">
                <div
                  className="tab-pane fade show active"
                  id="pills-home"
                  role="tabpanel"
                  aria-labelledby="pills-home-tab">
                  <div className="row">
                    <div className="col-sm-8">
                      <ul className="skills pop-skils open-sans">
                        <li>
                          <p className="f-16 c-999999 w-600">
                            <i className="fa-regular fa-envelope"></i>{' '}
                            <a href={`mailto:${singleuser.email}`}>{singleuser.email || '<EMAIL>'}</a>
                          </p>
                        </li>
                        {singleuser.contact_no && (
                          <li>
                            <p className="f-16 c-999999 w-600">
                              <i className="fa-solid fa-phone"></i>
                              <a target="_blank" href={'https://wa.me/' + singleuser.contact_no}>
                                {singleuser.contact_no || '+999 9 999 9999'}
                              </a>
                            </p>
                          </li>
                        )}
                        {singleuser.job_status ? (
                          <li>
                            <p className="f-16 c-999999 w-600">
                              <i className="fa-solid fa-briefcase"></i>
                              {singleuser.job_status == ' ready_to_interview'
                                ? 'Ready To Interview'
                                : singleuser.job_status == 'open_to_offer'
                                  ? 'Open To Offer'
                                  : singleuser.job_status == 'not_looking'
                                    ? 'Not Looking'
                                    : 'Ready To interview'}
                            </p>
                          </li>
                        ) : (
                          ''
                        )}
                        {singleuser.country_name ? (
                          <li>
                            <p className="f-16 c-999999 w-600">
                              <i className="fa-solid fa-location-dot"></i>
                              {singleuser.country_name}{' '}
                            </p>
                          </li>
                        ) : (
                          ''
                        )}
                        {singleuser.date_of_birth ? (
                          <li>
                            <p className="f-16 c-999999 w-600">
                              <i className="fa-regular fa-user"></i> Age:{' '}
                              {moment().diff(singleuser.date_of_birth, 'years')}
                            </p>
                          </li>
                        ) : (
                          ''
                        )}
                        {singleuser.gender ? (
                          <li>
                            <p className="f-16 c-999999 w-600">
                              <i className="fa-regular fa-circle-user"></i> {singleuser.gender}{' '}
                            </p>
                          </li>
                        ) : (
                          ''
                        )}
                        {singleuser.degree ? (
                          <li>
                            <p className="f-16 c-999999 w-600">
                              <i className="fa-solid fa-graduation-cap"></i> {singleuser.degree}{' '}
                            </p>
                          </li>
                        ) : (
                          ''
                        )}
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4">
                    {singleuser.bio ? (
                      <>
                        <p className="f-16 c-000">About</p>
                        <p className="f-18 c-4D4D4D w-400  mb-4">{singleuser.bio}</p>
                      </>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
        <PopupModal
          show={modalCandidateMessagePopup}
          handleClose={modalConfirmCandidateMessagePopupClose}
          customclass={'modal-lg header-remove body-sp-0'}>
          <div className="popup-body">
            <p className="f-31 c-191919 text-left">Message {singleuser.name}</p>
            <hr className="hr-line"></hr>
            <div className="form-experience-fieild">
              <form className="form-experience-fieild" onSubmit={handleSubmit(submitMessageForm)}>
                <input type="hidden" name="hd_candidate_id" className="hd_candidate_id" value={singleuser.id} />
                <p className="f-12 c-2C2C2C">Your Message</p>
                <textarea
                  placeholder="Your Message"
                  className="fild-des"
                  {...register('message_desc', {required: true})}
                  onChange={(e: any) => setMessageDesc(e.target.value)}></textarea>
                {errors.message_desc && errors.message_desc.type === 'required' && (
                  <p className="text-danger" style={{textAlign: 'left'}}>
                    Message Field is required.
                  </p>
                )}
                <div className="text-right mt-3">
                  <div className="row">
                    <div className="col-4">
                      <button className="cancel  w-100" onClick={modalConfirmCandidateMessagePopupClose}>
                        Cancel
                      </button>
                    </div>
                    <div className="col-8">
                      <button className="save w-100" type="submit">
                        Send
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </PopupModal>
      </div>
    </>
  );
}
