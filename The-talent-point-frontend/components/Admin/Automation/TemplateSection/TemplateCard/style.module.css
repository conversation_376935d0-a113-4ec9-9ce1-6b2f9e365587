.template_card {
  display: flex;
  padding: 8px;
  flex-direction: column;
  gap: 16px;
  background-color: #ebf4ff;
  border-radius: 8px;
  background: #ebf4ff;
  max-width: 332px;
}
.template_card_header {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  position: relative;
}
.template_card_image {
  width: 100%;
  height: 315px;
  object-fit: cover;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  position: relative;
  border: 1px solid #cfe5ff;
  background-color: white;
  margin: 0 auto;
}

.template_card_image div {
}

.template_content h5 {
  color: #0070f5;
  font-size: 22px;
  font-weight: 700;
  line-height: 120%;
  padding-bottom: 16px;
}

.template_content p {
  color: #4d4d4d;
  font-size: 18px;
  line-height: 160%;
}

.template_card_option_container {
  position: relative;
}

.template_card_option_container img {
  cursor: pointer;
}

.template_card_option {
  display: flex;
  width: 120px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 4px;
  box-shadow: 0px 4px 8px 0px rgba(5, 27, 68, 0.08);
  background: #fff;
  position: absolute;
  z-index: 50;
  right: 0;
}

.option_dropdown {
  width: 100%;
}
.option_dropdown ul li {
  display: flex;
  padding: 8px 12px;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  align-self: stretch;
  box-shadow: 0px -1px 0px 0px #edeff5 inset;
  width: 100%;
  color: #2c2c2c;
  text-align: right;
  font-size: 12px;
  line-height: 150%;
  letter-spacing: 0.24px;
  cursor: pointer;
}

.option_dropdown ul li:hover {
  background: #cfe5ff;
  color: #0055ba;
}
