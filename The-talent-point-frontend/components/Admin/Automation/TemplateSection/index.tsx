import {ButtonUi} from '@/ui/Button';
import styles from './styles.module.css';
import {SearchContainer} from '@/components/Common/SearchContainer';
import {useGetTemplatesList} from '@/modules/admin/automation/query/useGetTemplates';
import {useRouter} from 'next/router';
import CommonTable from '@/components/Common/CommonTable';
import {TemplateDetails} from '@/modules/admin/automation/query/useGetTemplatesById';
import {BadgeChip} from '@/components/Common/Badge';
import {Popconfirm, Space} from 'antd';
import {AiFillEye} from 'react-icons/ai';
import {MdCopyAll} from 'react-icons/md';
import {useDeleteTemplateList} from '@/modules/admin/automation/mutation/useDeleteTemplate';
import {useCreateTemplateList} from '@/modules/admin/automation/mutation/useCreateTemplateList';
import NiceModal from '@ebay/nice-modal-react';
import {TemplatePreview} from './TemplatePreview';
import {useDebounce} from '@/hooks/useDebounceCallback';
import {useState} from 'react';
import {FaBookmark, FaRegBookmark} from 'react-icons/fa6';
import {useUpdateTemplateList} from '@/modules/admin/automation/mutation/useUpdateTemplateList';
import moment from 'moment';

interface TemplateSectionProps {
  variant: 'template' | 'workflow';
}

export const TemplateSection = ({variant}: TemplateSectionProps) => {
  const [name, setName] = useState('');
  const {data} = useGetTemplatesList({
    template_name: name,
  });
  const router = useRouter();
  const {mutate: deleteTemplate} = useDeleteTemplateList();
  const {mutate: createTemplate} = useCreateTemplateList();
  const nodeId = router?.query?.nodeId as string;
  const {mutate: updateTemplate} = useUpdateTemplateList();

  const handleEditTemplate = (id: number) => {
    router.push(`/admin/automation/template-list/edit-template/${id}`);
  };

  const handleDeleteTemplate = (id: number) => {
    deleteTemplate({
      id: id,
    });
  };

  const handlePreviewTemplate = (id: number) => {
    NiceModal.show(TemplatePreview, {
      html: data?.find(item => item.id === id)?.template_html,
    });
  };

  const handleCopyTemplate = (id: number) => {
    const template = data?.find(item => item.id === id);
    template &&
      createTemplate({
        props: {
          bookmark: template?.bookmark,
          status: template?.status,
          template_html: template?.template_html,
          template_json: template?.template_json,
          template_name: template?.template_name,
          subject: template.subject,
        },
      });
  };

  const handleBack = () => {
    router.back();
  };

  const handleSelectTemplate = (id: number) => {
    router.push(`/admin/automation/workflows/edit-template/${id}?nodeId=${nodeId}`);
  };

  const handleSearchName = (e: any) => {
    setName(e.target.value);
  };

  const handleSearch = useDebounce(handleSearchName, 500);

  const handleBookmarkFill = (id: number) => {
    const template = data?.find(item => item.id === id);
    if (template) {
      updateTemplate({
        id: id,
        props: {
          ...template,
          bookmark: 1,
        },
      });
    }
  };

  const handleBookmarkUnfill = (id: number) => {
    const template = data?.find(item => item.id === id);
    if (template) {
      updateTemplate({
        id: id,
        props: {
          ...template,
          bookmark: 0,
        },
      });
    }
  };

  return (
    <div className={styles.template_section_container}>
      {variant === 'template' && (
        <>
          <div className={styles.template_header_container}>
            <h5>All Email Templates</h5>
            <ButtonUi
              variant="contained"
              color="primary"
              onClick={() => router.push('/admin/automation/template-list/create-template')}>
              Create Template
            </ButtonUi>
          </div>
          <div className={styles.template_filter_container}>
            <div className={styles.filter_left}>
              <div className={styles.search_container}>
                <SearchContainer
                  placeholder="Search..."
                  onChange={e => {
                    handleSearch(e);
                  }}
                />
              </div>
              {/* <CustomOptionDropdown placeholder="All" items={[]}  /> */}
            </div>
          </div>
        </>
      )}

      {variant === 'workflow' && (
        <div className={styles.workflow_template_header_container}>
          <h5>Choose a template</h5>

          <div className={styles.back_container} onClick={handleBack}>
            <img src={'/icons/automation/arrow_back.svg'} height={16} width={16} alt="back" />
            <p>Go back</p>
          </div>

          <div className={styles.template_filter_container}>
            <div className={styles.filter_left}>
              <div className={styles.search_container}>
                <SearchContainer placeholder="Search..." onChange={e => handleSearch(e)} />
              </div>
              {/* <CustomOptionDropdown placeholder="All" items={[]}  /> */}
            </div>
          </div>
        </div>
      )}

      <CommonTable
        data={data ?? []}
        columns={[
          {
            title: <th>Name</th>,
            dataIndex: 'name',
            key: 'name',
            render: (data: TemplateDetails) => {
              return <span>{data?.template_name}</span>;
            },
          },
          {
            title: <th>Status</th>,
            dataIndex: 'status',
            key: 'status',
            render: (data: TemplateDetails) => {
              return (
                <div>
                  <BadgeChip
                    label={data?.status === 1 ? 'In Use' : 'DRAFT'}
                    variant={data?.status === 1 ? 'active' : 'inactive'}
                  />
                </div>
              );
            },
          },
          {
            title: <th>Updated At</th>,
            dataIndex: 'updated_at',
            key: 'updated_at',
            render: (data: TemplateDetails) => {
              return <span>{data?.updated_at ? moment(data.updated_at).format('DD-MM-YYYY') : ''}</span>;
            },
          },
          {
            title: <th>Action</th>,
            dataIndex: 'action',
            key: 'action',
            render: (data: TemplateDetails) => {
              return (
                <Space>
                  {variant === 'template' && (
                    <>
                      <div className={styles.edit_container} onClick={() => handleEditTemplate(data?.id)}>
                        <img src={'/icons/employer/edit-pencil.svg'} width={16} height={16} alt="edit" />
                      </div>
                      <div className={styles.preview_container} onClick={() => handlePreviewTemplate(data?.id)}>
                        <AiFillEye color="#0055BA" />
                      </div>
                    </>
                  )}

                  <div className={styles.copy_container} onClick={() => handleCopyTemplate(data?.id)}>
                    <MdCopyAll color="#0055BA" />
                  </div>
                  <div className={styles.edit_container}>
                    {data?.bookmark === 0 ? (
                      <FaRegBookmark color="#0055BA" onClick={() => handleBookmarkFill(data?.id)} />
                    ) : (
                      <FaBookmark color="#0055BA" onClick={() => handleBookmarkUnfill(data?.id)} />
                    )}
                  </div>
                  <Popconfirm
                    title={'Are you sure to delete this template?'}
                    onConfirm={() => handleDeleteTemplate(data?.id)}>
                    <div className={styles.delete_container}>
                      <img src={'/icons/data-management/delete.svg'} width={16} height={16} alt="delete" />
                    </div>
                  </Popconfirm>
                  {variant === 'workflow' && (
                    <div>
                      <ButtonUi
                        color="primary"
                        variant="contained"
                        onClick={() => handleSelectTemplate(data?.id)}
                        style={{
                          width: '140px',
                          marginLeft: '12px',
                          height: '36px',
                          borderRadius: '4px',
                          fontWeight: 'bold',
                        }}>
                        Select
                      </ButtonUi>
                    </div>
                  )}
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};
