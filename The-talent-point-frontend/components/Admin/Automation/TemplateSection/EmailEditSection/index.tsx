import React from 'react';
import EmailEditor from 'react-email-editor';
import styles from './style.module.css';
import {EmailEditorContainer} from '@/components/Common/EmailEditorContainer';
import {useGetTemplatesById} from '@/modules/admin/automation/query/useGetTemplatesById';
import {useRouter} from 'next/router';
import NiceModal, {useModal} from '@ebay/nice-modal-react';
import {ModalWrapper} from '@/components/modal';
import {Modal} from 'antd/lib';

interface EmailEditorSectionProps {
  editorId?: number;
  setTemplateHtml: (html: string) => void;
  setTemplateJson: (json: any) => void;
}

export const EmailEditorSection = NiceModal.create(
  ({editorId, setTemplateHtml, setTemplateJson}: EmailEditorSectionProps) => {
    const {visible, remove} = useModal();
    const {data} = useGetTemplatesById({
      id: editorId,
    });
    return (
      <ModalWrapper
        open={visible}
        onCancel={remove}
        closeIcon={<></>}
        className={styles.email_editor_container}
        size="fullScreen">
        <EmailEditorContainer
          html={data?.template_html}
          json={data?.template_json ? JSON.parse(data.template_json) : undefined}
          onClose={remove}
          setTemplateHtml={setTemplateHtml}
          setTemplateJson={setTemplateJson}
        />
      </ModalWrapper>
    );
  },
);
