import {<PERSON><PERSON><PERSON>rapper} from '@/components/modal';
import NiceModal, {useModal} from '@ebay/nice-modal-react';
import styles from './style.module.css';
import {ButtonUi} from '@/ui/Button';
import {useFormik} from 'formik';
import {InputUi} from '@/components/Common/Input';
import {DropdownUi} from '@/components/Common/DropdownUi';
import {useGetDataPoints} from '@/modules/admin/automation/query/useGetDataPoints';
import {useMemo} from 'react';

export const TestEmailSendContainer = NiceModal.create(() => {
  const {visible, remove} = useModal();
  const {data} = useGetDataPoints({
    search: '',
  });

  const allData = useMemo(() => {
    return data?.pages?.map(item => item).flat() || [];
  }, [data]);

  const dataPointOptions = allData
    ?.map((item: any) => ({
      label: item.data_point_name,
      value: item.data_point_name,
    }))
    .filter((item, index, self) => self.findIndex(t => t.value === item.value) === index);
  const {values, handleChange, handleSubmit, setFieldValue} = useFormik({
    initialValues: {
      dataPoints: [
        {
          name: '',
          value: '',
        },
      ],
    },
    onSubmit: values => {
      console.log(values);
    },
  });

  const handleAddDataPoint = () => {
    setFieldValue('dataPoints', [
      ...values.dataPoints,
      {
        name: '',
        value: '',
      },
    ]);
  };

  const handleRemoveDataPoint = (index: number) => {
    setFieldValue(
      'dataPoints',
      values.dataPoints.filter((_, i) => i !== index),
    );
  };
  return (
    <ModalWrapper open={visible} onCancel={remove} size="large">
      <div className={styles.test_email_send_container}>
        <h4>Test Email Send</h4>

        <div className={styles.data_point_container}>
          <div className={styles.data_point_header}>
            <ButtonUi variant="outlined" color="primary" onClick={handleAddDataPoint}>
              Add Data Point
            </ButtonUi>
          </div>

          <form onSubmit={handleSubmit}>
            {' '}
            <div className={styles.data_point_item_container}>
              {values.dataPoints.map((dataPoint, index) => (
                <div key={index} className={styles.data_point_item}>
                  <DropdownUi
                    placeholder="Data Point Name"
                    options={dataPointOptions ?? []}
                    value={dataPoint.name || undefined}
                    onChange={e => setFieldValue(`dataPoints[${index}].name`, e)}
                    style={{
                      maxWidth: '200px',
                      width: '100%',
                      marginBottom: 0,
                    }}
                    dropDownSize="small"
                  />
                  <InputUi
                    name={`dataPoints[${index}].value`}
                    placeholder="Data Point Value"
                    value={dataPoint.value}
                    onChange={handleChange}
                    bottomLabel={false}
                    inputSize="small"
                  />
                  <ButtonUi
                    variant="outlined"
                    color="primary"
                    onClick={() => handleRemoveDataPoint(index)}
                    disabled={values.dataPoints.length === 1}>
                    Remove
                  </ButtonUi>
                </div>
              ))}
            </div>
            <div className={styles.test_email_send}>
              <ButtonUi
                variant="contained"
                color="primary"
                disabled={values.dataPoints.some(dataPoint => !dataPoint.name || !dataPoint.value)}>
                <button>Send Test Email</button>
              </ButtonUi>
            </div>
          </form>
        </div>
      </div>
    </ModalWrapper>
  );
});
