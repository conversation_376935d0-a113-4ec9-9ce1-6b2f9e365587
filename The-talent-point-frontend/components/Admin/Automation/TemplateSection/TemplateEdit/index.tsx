import styles from './style.module.css';
import {useFormik} from 'formik';
import {ButtonUi} from '@/ui/Button';
import {useEffect, useRef, useState} from 'react';
import {InputUi} from '@/components/Common/Input';
import {useRouter} from 'next/router';
import {useGetTemplatesById} from '@/modules/admin/automation/query/useGetTemplatesById';
import {useCreateTemplateList} from '@/modules/admin/automation/mutation/useCreateTemplateList';
import {useUpdateTemplateList} from '@/modules/admin/automation/mutation/useUpdateTemplateList';
import NiceModal from '@ebay/nice-modal-react';
import {EmailEditorSection} from '../EmailEditSection';
import {useWorkflowNodes} from '@/hooks/useAddWorkflowItem';
import {Switch} from 'antd';

interface TemplateCreateContainerProps {
  variant: 'template' | 'workflow';
}

export const TemplateCreateContainer = ({variant}: TemplateCreateContainerProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<any>(null);
  const router = useRouter();
  const id = router.query.id as string;
  const nodeId = router.query.nodeId as string;
  const {nodes, addNode, updateNodes} = useWorkflowNodes();

  const currentNode = nodes?.find(el => el.id === nodeId);

  const {mutate: createList, isLoading: isCreating} = useCreateTemplateList();
  const {mutate: updateList, isLoading: isUpdating} = useUpdateTemplateList();
  const {data: templateDetails} = useGetTemplatesById({
    id: Number(id),
  });
  const [templateHtml, setTemplateHtml] = useState(templateDetails?.template_html ?? '');
  const [templateJson, setTemplateJson] = useState(templateDetails?.template_json ?? '');

  const {handleSubmit, handleChange, values, setFieldValue} = useFormik({
    initialValues: {
      subject: templateDetails?.subject ?? '',
      status: templateDetails?.status ?? 0,
      name: templateDetails?.template_name ?? 'Template Name',
    },
    enableReinitialize: true,
    onSubmit: values => {
      if (variant === 'workflow') {
        if (currentNode) {
          updateNodes([
            {
              ...currentNode,
              data: {
                ...currentNode.data,
                templates: {
                  ...values,
                  templateId: Number(id),
                },
              },
            },
          ]);
          updateList(
            {
              id: Number(id),
              props: {
                template_html: templateHtml,
                template_json: templateJson,
                template_name: values.name,
                bookmark: templateDetails?.bookmark ?? 0,
                status: values.status ?? 1,
                subject: values.subject,
              },
            },
            {
              onSuccess: () => {
                router.push('/admin/automation/workflows/add/workflow');
              },
            },
          );
        } else {
          addNode('templates', {
            templates: {
              ...values,
              templateId: Number(id),
            },
            type: 'templates',
          });
          updateList(
            {
              id: Number(id),
              props: {
                template_html: templateHtml,
                template_json: templateJson,
                template_name: values.name,
                bookmark: templateDetails?.bookmark ?? 0,
                status: values.status ?? 1,
                subject: values.subject,
              },
            },
            {
              onSuccess: () => {
                router.push('/admin/automation/workflows/add/workflow');
              },
            },
          );
        }

        return;
      }
      id
        ? updateList(
            {
              id: Number(id),
              props: {
                template_html: templateHtml,
                template_json: templateJson,
                template_name: values.name,
                bookmark: templateDetails?.bookmark ?? 0,
                status: values.status ?? 0,
                subject: values.subject,
              },
            },
            {
              onSuccess: () => {
                router.push('/admin/automation/template-list');
              },
            },
          )
        : createList(
            {
              props: {
                bookmark: 0,
                status: 0,
                template_html: templateHtml,
                template_json: templateJson,
                template_name: values.name,
                subject: values.subject,
              },
            },
            {
              onSuccess: () => {
                router.push('/admin/automation/template-list');
              },
            },
          );
    },
  });

  const handleEditClick = () => {
    setIsEditing(true);
    inputRef?.current?.focus?.();
  };

  const handleInputChange = (e: any) => {
    setFieldValue('name', e.target.value);
  };

  const handleInputBlur = () => {
    setIsEditing(false);
  };

  const handleOpenEmailEditor = () => {
    if (router.query.id) {
      NiceModal.show(EmailEditorSection, {
        editorId: Number(router.query.id),
        setTemplateHtml,
        setTemplateJson,
      });
    } else {
      NiceModal.show(EmailEditorSection, {
        setTemplateHtml,
        setTemplateJson,
      });
    }
  };

  const handleBack = () => {
    router.back();
  };

  useEffect(() => {
    if (templateDetails?.template_html || templateDetails?.template_json) {
      setTemplateHtml(templateDetails?.template_html);
      setTemplateJson(templateDetails?.template_json);
    }
  }, [templateDetails?.template_html, templateDetails?.template_json]);

  return (
    <div className={styles.template_edit_container}>
      <form onSubmit={handleSubmit}>
        <div className={styles.template_header_container}>
          <div>
            <div className={styles.back_container} onClick={handleBack}>
              <img src={'/icons/automation/arrow_back.svg'} height={16} width={16} alt="back" />
              <p>Go back</p>
            </div>
            <div className={styles.template_name_container} title={values.name}>
              {true ? (
                <input
                  type="text"
                  value={values.name}
                  onChange={handleInputChange}
                  onBlur={handleInputBlur}
                  className={styles.template_name_input}
                  autoFocus
                  ref={inputRef}
                />
              ) : (
                <h5>{values.name}</h5>
              )}
              <img
                src={'/icons/automation/edit-outlined.svg'}
                height={16}
                width={16}
                alt="edit"
                onClick={handleEditClick}
                className={styles.edit_icon}
              />
            </div>
          </div>
          <div className={styles.button_container}>
            <ButtonUi
              variant="contained"
              color="primary"
              onClick={(e: any) => handleSubmit(e)}
              disabled={!values.subject || !templateHtml}>
              <button>{variant === 'template' ? 'Save Changes' : 'Save & Continue'}</button>
            </ButtonUi>
          </div>
        </div>
      </form>

      <div className={styles.form_container}>
        <InputUi
          label="Subject"
          name="subject"
          onChange={handleChange}
          placeholder="Enter subject here"
          value={values.subject}
          bottomLabel={false}
        />
      </div>

      <div>
        <p
          style={{
            fontSize: '14px',
            color: '#333',
            marginBottom: '10px',
          }}>
          Status
        </p>
        <Switch
          checked={values.status === 1}
          onChange={checked => {
            setFieldValue('status', checked ? 1 : 0);
          }}
        />
      </div>

      <div className={styles.preview_container}>
        <div className={styles.preview_header}>
          <ButtonUi color="primary" variant="contained" onClick={handleOpenEmailEditor}>
            Open Email Editor
          </ButtonUi>
        </div>

        {templateDetails?.template_html && (
          <div
            dangerouslySetInnerHTML={{
              __html: templateHtml,
            }}
            className={styles.preview_content}
          />
        )}
      </div>
    </div>
  );
};
