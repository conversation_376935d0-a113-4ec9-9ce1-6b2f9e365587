import {ModalWrapper} from '@/components/modal';
import NiceModal, {useModal} from '@ebay/nice-modal-react';
import styles from './style.module.css';

interface TemplatePreviewProps {
  html: string;
}

export const TemplatePreview = NiceModal.create(({html}: TemplatePreviewProps) => {
  const {visible, remove} = useModal();
  return (
    <ModalWrapper onCancel={remove} open={visible} size="large">
      <div className={styles.preview_template_container}>
        <h3>Preview Template</h3>
        <div
          dangerouslySetInnerHTML={{
            __html: html,
          }}
        />
      </div>
    </ModalWrapper>
  );
});
