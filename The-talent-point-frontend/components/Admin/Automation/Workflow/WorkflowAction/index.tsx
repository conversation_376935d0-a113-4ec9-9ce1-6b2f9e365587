import {useClickOutside} from '@/hooks/useOutSideClick';
import {WorkFLowList} from '@/modules/admin/automation/query/useGetWorkflowList';
import Image from 'next/image';
import {useRef, useState} from 'react';
import styles from './style.module.css';
import {OptionCard} from '@/components/Common/OptionCard';
import {useRouter} from 'next/router';
import {useDeleteWorkflows} from '@/modules/admin/automation/mutation/useDeleteWorkflow';
import {useUpdateWorkflowStatus} from '@/modules/admin/automation/mutation/useUpdateWorkflowStatus';
import {useGetWorkflowById} from '@/modules/admin/automation/query/useGetWorkflowById';
import {useCreateWorkflow} from '@/modules/admin/automation/mutation/useCreateWorkflow';

interface WorkflowActionCardProps {
  record: WorkFLowList;
}

export const WorkflowActionCard = ({record}: WorkflowActionCardProps) => {
  const [showModal, setShowModal] = useState(false);
  const modalRef = useRef(null);
  useClickOutside(modalRef, [], () => {
    setShowModal(false);
  });
  const router = useRouter();
  const {mutate: deleteWorkflow} = useDeleteWorkflows();
  const {mutate: updateWorkflow} = useUpdateWorkflowStatus();
  const {mutate: createWorkflow} = useCreateWorkflow();
  const {data} = useGetWorkflowById({
    id: record.id,
  });

  console.log({data});

  const handleEditWorkflow = (id: number) => {};

  const handleToggleWorkflow = (id: number, status: number) => {
    updateWorkflow(
      {id, status},
      {
        onSuccess: () => {
          setShowModal(false);
        },
      },
    );
    setShowModal(false);
  };

  const handleDuplicateWorkflow = () => {
    data?.workflow &&
      createWorkflow({
        workflows: [
          {
            AM_PM: data?.workflow.AM_PM,
            conditions: data?.workflow.conditions,
            contacts: data?.workflow.contacts,
            delay_unit: data?.workflow.delay_unit,
            delay_value: data?.workflow.delay_value,
            email_template_id: data?.workflow.email_template_id.toString(),
            execution_type: data?.workflow.execution_type,
            frequency_period: data?.workflow.frequency_period,
            frequency_time: data?.workflow.frequency_time,
            frequency_value: data?.workflow.frequency_value,
            status: 0,
            segments: data?.workflow.segments,
            name: data?.workflow.name,
          },
        ],
      });
    setShowModal(false);
  };

  const handleDeleteWorkflow = (id: number) => {
    deleteWorkflow({id});
    setShowModal(false);
  };

  return (
    <div className={styles.workflow_action_container} ref={modalRef}>
      <div className={styles.workflow_action_button} onClick={() => setShowModal(true)}>
        <img src={'/icons/automation/menu_primary.svg'} alt="menu" width={20} height={20} />
      </div>
      {showModal && (
        <div className={styles.option_card_container}>
          <OptionCard
            items={[
              {
                label: 'Edit',
                value: 'edit',
                onClick: () => router.push(`/admin/automation/workflows/edit/${record.id}/basic_info`),
              },

              {
                label: 'Duplicate',
                value: 'duplicate',
                onClick: () => {
                  handleDuplicateWorkflow();
                },
              },
              {
                label: record?.status === 1 ? 'Stop' : 'Start',
                value: record?.status === 1 ? 'stop' : 'start',
                onClick: () => {
                  handleToggleWorkflow(record.id, record?.status === 1 ? 0 : 1);
                },
              },
              {
                label: 'Delete',
                value: 'delete',
                onClick: () => {
                  handleDeleteWorkflow(record.id);
                },
                color: '#D04E4F',
              },
            ]}
          />
        </div>
      )}
    </div>
  );
};
