import clsx from 'clsx';
import styles from './style.module.css';
import Image from 'next/image';
interface WorkflowPartsProps {
  variant: 'condition' | 'contacts' | 'frequency' | 'templates';
  active?: boolean;
  selected?: boolean;
  onClick?: () => void;
}

export const WorkflowParts = ({variant, active, selected, ...props}: WorkflowPartsProps) => {
  const imgSrc = () => {
    switch (variant) {
      case 'condition':
        return '/icons/automation/bolt.svg';
      case 'contacts':
        return '/icons/automation/mail.svg';
      case 'frequency':
        return '/icons/automation/hourglass.svg';
      case 'templates':
        return '/icons/automation/article.svg';
    }
  };

  const backGroundColor = () => {
    switch (variant) {
      case 'condition':
        return '#FDCA40';
      case 'contacts':
        return '#FF8D74';
      case 'frequency':
        return '#0EB1D2';
      case 'templates':
        return '#48E5C2';
    }
  };
  return (
    <div
      style={{backgroundColor: backGroundColor()}}
      className={clsx(styles.workflow_part, selected ? styles.selected : '', active ? styles.active : styles.inactive)}>
      <img src={imgSrc()} width={24} height={24} alt={variant} />
    </div>
  );
};
