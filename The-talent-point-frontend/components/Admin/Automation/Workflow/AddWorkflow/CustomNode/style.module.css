.customNodes_container {
  display: flex;
  width: 212px;
  padding: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: #fff;
  position: relative;
}

.customNodes_container h6 {
  color: #747474;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}

.customNodes_container p {
  color: #2c2c2c;
  font-size: 12px;
  line-height: 140%;
}
.plus_container {
  display: inline-flex;
  padding: 4px;
  align-items: center;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background: #f9f9f9;
}
.custom_added_node {
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 20;
}
.customNodes_add__container {
  display: flex;
  align-items: start;
  gap: 8px;
  padding-left: 90px;
  padding-top: 8px;
  position: absolute;
}
.line {
  width: 196px;
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
}
.customNodes_action_container {
  display: flex;
  gap: 8px;
  position: absolute;
  right: -100px;
  top: 0;
}

.customNodes_action_container .edit_container {
  display: flex;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  background: #ebf4ff;
  padding: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
}

.customNodes_action_container .plus_container {
  border-radius: 4px !important;
  background: #eee !important;
  border: none !important;
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
}

.customNodes_action_container .delete_container {
  display: flex;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  background: var(--Status-Negative-Light, #ffebeb);
}
.custom_boolean_add {
  position: absolute;
  left: 220px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
}
.new_flow_add_container {
  position: absolute;
  right: -230px;
  top: 0px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.setting_config_container {
  position: absolute;
  right: 4px;
  top: 4px;
  z-index: 30;
  transition: all 0.3s;
}

.show_action_card {
  opacity: 1 !important;
  right: -100px !important;
  z-index: 50 !important;
  pointer-events: all !important;
}

.customNodes_action_container {
  position: absolute;
  right: 4px;
  top: 4px;
  opacity: 0;
  transition: all 0.3s;
  cursor: pointer;
  pointer-events: none;
}
.show_card {
  rotate: 180deg;
  transition: all 0.3s;
}
.plus_icon_card {
  right: -70px !important;
}
