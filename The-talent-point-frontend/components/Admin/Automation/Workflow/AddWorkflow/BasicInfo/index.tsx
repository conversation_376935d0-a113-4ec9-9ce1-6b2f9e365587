import {StepperUi} from '@/components/Common/Steper';
import styles from './style.module.css';
import {useFormik} from 'formik';
import {InputUi} from '@/components/Common/Input';
import {DropdownUi} from '@/components/Common/DropdownUi';
import {ButtonUi} from '@/ui/Button';
import {useRouter} from 'next/router';
import {useGetAutomationInstance} from '@/modules/admin/automation/query/useGetAutomationInstance';
import {useWorkflowStore} from '@/modules/admin/automation/store/CreateWorkflow';
import {useGetWorkflowById} from '@/modules/admin/automation/query/useGetWorkflowById';

export const AddWorkflowBasicInfo = () => {
  const router = useRouter();
  const {setBasicInfo} = useWorkflowStore();

  const {data: instances} = useGetAutomationInstance();
  const id = router.query.id;
  const {data} = useGetWorkflowById({
    id: Number(id as string),
  });

  const {values, handleSubmit, setFieldValue} = useFormik({
    initialValues: {
      workflow_title: data?.workflow?.name || '',
      instance: '',
    },
    enableReinitialize: true,
    onSubmit: value => {
      setBasicInfo({
        instance: value.instance,
        title: value.workflow_title,
      });
      if (id) {
        router.push(`/admin/automation/workflows/edit/${id}/workflow`);
        return;
      }
      router.push('/admin/automation/workflows/add/workflow');
    },
  });

  return (
    <form className={styles.workflow_container} onSubmit={handleSubmit}>
      <h1>Create a workflow</h1>

      <StepperUi
        steps={[
          {
            title: 'Basic Information',
          },
          {
            title: 'Workflow',
          },
          {
            title: 'Review',
          },
        ]}
        currentStep={0}
      />

      <div className={styles.workflow_form_container}>
        <InputUi
          label="Workflow Title"
          name="workflow_title"
          placeholder="Enter title here"
          type="text"
          value={values.workflow_title}
          onChange={e => {
            setFieldValue('workflow_title', e.target.value);
          }}
        />
        <DropdownUi
          label="Instance"
          placeholder="Select an instance"
          options={instances?.map(el => {
            return {
              label: el.title,
              options: el.options,
            };
          })}
          prefixCls="instance"
          onChange={(value: string) => {
            setFieldValue('instance', value);
          }}
        />
      </div>

      <div className={styles.workflow_button_container}>
        <ButtonUi variant="outlined" color="primary">
          Cancel
        </ButtonUi>
        <ButtonUi variant="contained" color="primary">
          <button
            type="submit"
            style={{
              border: 'none',
              backgroundColor: 'transparent',
            }}>
            Continue
          </button>
        </ButtonUi>
      </div>
    </form>
  );
};
