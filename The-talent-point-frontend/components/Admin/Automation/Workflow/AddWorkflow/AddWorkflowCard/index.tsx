import React, {Dispatch, SetStateAction} from 'react';
import styles from './style.module.css';
import {WorkflowParts} from '../WorkflowParts';
import clsx from 'clsx';
import NiceModal from '@ebay/nice-modal-react';
import {AddConditionModal} from '@/components/modal/AddConditionModal';
import {AddContactModal} from '@/components/modal/AddContactModal';
import {FrequencyModal} from '@/components/modal/AddFrequencyModal';
import {useRouter} from 'next/router';

export type NodeAddType = 'condition' | 'contacts' | 'frequency' | 'templates';
interface AddWorkflowCardProps {
  addNode: (type: any, data: any, logic: 'AND' | 'OR') => void;
  closeModal: () => void;
  selectedLogic?: 'AND' | 'OR';
  id?: string;
  nodes?: any;
  activeIndex: number;
  setBooleanModalOpen?: Dispatch<SetStateAction<boolean>>;
}

export const AddWorkflowCard = ({
  addNode,
  closeModal,
  selectedLogic,
  nodes,
  activeIndex,
  setBooleanModalOpen,
}: AddWorkflowCardProps) => {
  const router = useRouter();
  const addWorkflowData = [
    {
      id: 1,
      title: 'Condition',
      description: 'When a condition is met',
      icon: '/images/automation/condition.svg',
      key: 'condition',
    },
    {
      id: 2,
      title: 'Contacts',
      description: 'Send email to contacts',
      icon: '/images/automation/contacts.svg',
      key: 'contacts',
    },
    {
      id: 3,
      title: 'Frequency',
      description: 'Every duration @ time',
      icon: '/images/automation/frequency.svg',
      key: 'frequency',
    },
    {
      id: 4,
      title: 'Template',
      description: 'Add template',
      icon: '/images/automation/template.svg',
      key: 'templates',
    },
  ];

  const activeCard = activeIndex === 4 ? 1 : activeIndex + 1;

  const handleAddNode = (type: NodeAddType) => {
    switch (type) {
      case 'condition':
        NiceModal.show(AddConditionModal, {
          addNode,
          closeModal,
          selectedLogic,
          id: 'condition',
        });
        setBooleanModalOpen?.(false);

        break;
      case 'contacts':
        NiceModal.show(AddContactModal, {
          addNode,
          closeModal,
          selectedLogic,
          id: 'contacts',
        });
        setBooleanModalOpen?.(false);
        break;
      case 'frequency':
        NiceModal.show(FrequencyModal, {
          addNode,
          closeModal,
          selectedLogic,
          id: 'frequency',
        });
        setBooleanModalOpen?.(false);
        break;
      case 'templates':
        router.push('/admin/automation/workflows/template');
        setBooleanModalOpen?.(false);
        break;
      default:
    }
  };

  return (
    <div className={styles.workflow_add_card}>
      <div className={styles.card_header}>
        <h5>ADD</h5>
      </div>
      <div className={styles.line} />
      <div className={styles.card_body_container}>
        {addWorkflowData.map(data => {
          return (
            <div
              key={data.id}
              onClick={() => {
                if (data.id !== activeCard) {
                  return;
                }
                handleAddNode(data.key as NodeAddType);
              }}
              className={clsx(styles.card_body, {
                [styles.card_body_active]:
                  data.key === 'condition' ||
                  data.key === 'contacts' ||
                  data.key === 'frequency' ||
                  data.key === 'templates',
              })}>
              <WorkflowParts variant={data.key as NodeAddType} active={data.id === activeCard} selected={false} />
              <span>{data.title}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};
