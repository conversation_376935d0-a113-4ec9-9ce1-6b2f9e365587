.workflow_add_card {
  display: flex;
  width: 212px;
  padding: 8px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: #fff;
}
.card_header h5 {
  color: #2c2c2c;
  font-size: 12px;
  font-weight: 600;
  line-height: 140%;
}
.line {
  width: 196px;
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
}
.card_body {
  display: flex;
  width: 90px;
  padding: 8px 0px;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  opacity: 0.5;
  background: #fff;
  cursor: pointer;
}
.card_body_container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}
.card_body:hover {
  border: 1px solid #0070f5;
  transition: all 0.3s ease;
  box-shadow:
    0px 1px 3px 0px rgba(21, 21, 21, 0.12),
    0px 2px 5px 0px rgba(21, 21, 21, 0.1),
    0px 4px 12px 0px rgba(21, 21, 21, 0.12);
}
.card_body_active {
  opacity: 1;
}
