import clsx from 'clsx';
import styles from './style.module.css';
interface BooleanCardProps {
  selected?: string;
  onClick?: (selected: 'AND' | 'OR') => void;
}
export const BooleanCard = ({selected, onClick}: BooleanCardProps) => {
  return (
    <div className={styles.boolean_card}>
      <div
        className={clsx(styles.boolean_button, {
          [styles.selected]: selected === 'AND',
        })}
        onClick={() => {
          onClick?.('AND');
        }}>
        AND
      </div>
      <div
        className={clsx(styles.boolean_button, {
          [styles.selected]: selected === 'OR',
        })}
        onClick={() => {
          onClick?.('OR');
        }}>
        OR
      </div>
    </div>
  );
};
