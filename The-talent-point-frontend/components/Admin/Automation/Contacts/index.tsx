import {useRouter} from 'next/router';
import {useState, useEffect, useMemo} from 'react';
import {ButtonUi} from '@/ui/Button';
import {TabsUi} from '@/ui/Tabs';
import styles from './style.module.css';
import {ContactTableData, ContactType, useGetContactList} from '@/modules/admin/automation/query/useGetContactLists';
import {TableColumn} from '@/components/Common/CommonTable';
import {ContactTableContainer} from './ContactTableContainer';
import NiceModal from '@ebay/nice-modal-react';
import {ExportContactListModal} from '@/components/modal/ExportContactListModal';
import SortingFilter from '../../Employers/FilterModalCard';
import moment from 'moment';
import {useDebounce} from '@/hooks/useDebounceCallback';

export const AutomationContacts = () => {
  const router = useRouter();
  const [location, setLocation] = useState<string>('');
  const {query} = router;
  const limit = 20;
  const [search, setSearch] = useState<string>('');
  const [name, setName] = useState<string>('');
  const initialTab = query.tab || 'employee';
  const [activeKey, setActiveKey] = useState<ContactType>(initialTab as ContactType);

  const [nameModal, setNameModal] = useState(false);
  const [emailModal, setEmailModal] = useState(false);
  const [locationModal, setLocationModal] = useState(false);
  const [addedModal, setAddedModal] = useState(false);

  const [order_by, setOrderBy] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('');

  const props: any = useMemo(() => {
    return {
      type: activeKey,
      location,
      name: name,
      per_page: limit,
    };
  }, [activeKey, location, name, order_by, selectedFilter]);

  const {data, fetchNextPage, isFetchingNextPage, isLoading} = useGetContactList(props);

  const allData: ContactTableData[] = useMemo(() => {
    return data?.pages?.map(item => item).flat() || [];
  }, [data]);

  const handleTabChange = (key: any) => {
    setActiveKey(key);
    router.push(
      {
        pathname: router.pathname,
        query: {...query, tab: key},
      },
      undefined,
      {shallow: true},
    );
  };

  useEffect(() => {
    if (query.tab) {
      setActiveKey(query.tab as ContactType);
    }
  }, [query]);

  useEffect(() => {
    if (order_by && selectedFilter) {
      props.sort_by = selectedFilter;
      props.sort_order = order_by;
    }
  }, [order_by, selectedFilter]);

  const filterModalData = {
    email: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
    name: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
    location: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
    created_at: {
      items: [
        {label: 'Low to High', value: 'asc'},
        {label: 'High to Low', value: 'desc'},
      ],
    },
  };

  const handleSelect = (column: 'name' | 'email' | 'location' | 'created_at', value: string) => {
    switch (column) {
      case 'name':
        setSelectedFilter('name');
        setOrderBy(value);
        break;
      case 'email':
        setSelectedFilter('email');
        setOrderBy(value);
        break;
      case 'location':
        setSelectedFilter('location');
        setOrderBy(value);
        break;
      case 'created_at':
        setSelectedFilter('created_at');
        setOrderBy(value);
        break;
      default:
        break;
    }
  };

  const handleSearch = useDebounce((e: any) => {
    setName(e);
  }, 500);

  const columns: TableColumn[] = [
    {
      key: 'email',
      title: (
        <th
          onMouseLeave={() => setEmailModal(false)}
          onMouseEnter={() => setEmailModal(true)}
          style={{
            position: 'relative',
          }}>
          EMAIL
          <SortingFilter
            data={filterModalData.email}
            onSelect={(value: string) => handleSelect('email', value)}
            isOpen={emailModal}
            setIsOpen={setEmailModal}
            selectedValue={selectedFilter === 'email' ? order_by : ''}
          />
        </th>
      ),
      render: (item: ContactTableData) => {
        return <td className={styles.email_text}>{item?.email}</td>;
      },
    },
    {
      key: 'name',
      title: (
        <th
          onMouseLeave={() => setNameModal(false)}
          onMouseEnter={() => setNameModal(true)}
          style={{
            position: 'relative',
          }}>
          NAME
          <SortingFilter
            data={filterModalData.name}
            onSelect={(value: string) => handleSelect('name', value)}
            isOpen={nameModal}
            setIsOpen={setNameModal}
            selectedValue={selectedFilter === 'name' ? order_by : ''}
          />
        </th>
      ),
      render: (item: ContactTableData) => {
        return <td>{item?.name}</td>;
      },
    },
    {
      key: 'location',
      title: (
        <th
          onMouseLeave={() => setLocationModal(false)}
          onMouseEnter={() => setLocationModal(true)}
          style={{
            position: 'relative',
          }}>
          LOCATION
          <SortingFilter
            data={filterModalData.location}
            onSelect={(value: string) => handleSelect('location', value)}
            isOpen={locationModal}
            setIsOpen={setLocationModal}
            selectedValue={selectedFilter === 'location' ? order_by : ''}
          />
        </th>
      ),
      render: (item: ContactTableData) => {
        return <td>{item?.location}</td>;
      },
    },
    {
      key: 'added',
      title: (
        <th
          onMouseLeave={() => setAddedModal(false)}
          onMouseEnter={() => setAddedModal(true)}
          style={{
            position: 'relative',
          }}>
          ADDED
          <SortingFilter
            data={filterModalData.created_at}
            onSelect={(value: string) => handleSelect('created_at', value)}
            isOpen={addedModal}
            setIsOpen={setAddedModal}
            selectedValue={selectedFilter === 'created_at' ? order_by : ''}
          />
        </th>
      ),
      render: (item: ContactTableData) => {
        return <td>{item?.created_at ? moment(item.created_at).format('DD/MM/YYYY') : null}</td>;
      },
    },
  ];

  const handleShowExportModal = () => {
    NiceModal.show(ExportContactListModal);
  };

  return (
    <div className={styles.contacts_container}>
      <div className={styles.contacts_header}>
        <h4>Contact Lists</h4>
        <ButtonUi variant="outlined" color="primary" onClick={handleShowExportModal}>
          Export List
        </ButtonUi>
      </div>
      <TabsUi
        variant="highlight"
        items={[
          {
            key: 'employee',
            label: 'Candidates',
            children: (
              <div>
                <ContactTableContainer
                  columns={columns}
                  data={allData}
                  setLocation={setLocation}
                  onSearch={(e: any) => {
                    setSearch(e);
                    handleSearch(e);
                  }}
                  fetchNextPage={fetchNextPage}
                  isFetchingNextPage={isFetchingNextPage}
                  loading={isLoading}
                />
              </div>
            ),
          },
          {
            key: 'employer',
            label: 'Employers',
            children: (
              <div>
                <ContactTableContainer
                  columns={columns}
                  data={allData}
                  setLocation={setLocation}
                  onSearch={(e: any) => {
                    setSearch(e);
                    handleSearch(e);
                  }}
                  fetchNextPage={fetchNextPage}
                  isFetchingNextPage={isFetchingNextPage}
                  loading={isLoading}
                />
              </div>
            ),
          },
          // {
          //   key: 'test',
          //   label: 'Test',
          //   children: (
          //     <div>
          //       <ContactTableContainer
          //         columns={columns}
          //         data={allData}
          //         setLocation={setLocation}
          //         setSearchName={setSearch}
          //         fetchNextPage={fetchNextPage}
          //         isFetchingNextPage={isFetchingNextPage}
          //         loading={isLoading}
          //       />
          //     </div>
          //   ),
          // },
        ]}
        activeKey={activeKey}
        onChange={handleTabChange}
      />
    </div>
  );
};
