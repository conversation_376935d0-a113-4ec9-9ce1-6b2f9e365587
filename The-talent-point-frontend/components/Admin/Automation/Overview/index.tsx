import {ButtonUi} from '@/ui/Button';
import styles from './style.module.css';
import {AutomationOverviewCard} from './OverviewCard';
import {useEffect, useRef, useState} from 'react';
import dayjs from 'dayjs';
import {EmailStatisticsChart} from './StatisticChart';
import {CustomOptionDropdown} from '@/components/Common/CustomOptionDropdown';
import {useGetOverview} from '@/modules/admin/automation/query/useGetEmailStatistic';
import moment from 'moment';
import {DatePicker} from 'antd/lib';
import {useRouter} from 'next/router';
import {Spinner} from 'react-bootstrap';

export const AutomationOverView = () => {
  const router = useRouter();
  const [timePeriod, setTimePeriod] = useState<{startDate: string; endDate: string}>({
    startDate: moment().startOf('year').toISOString(),
    endDate: moment().endOf('year').toISOString(),
  });
  const [customDate, setCustomDate] = useState<{startDate: string | null; endDate: string | null}>({
    startDate: null,
    endDate: null,
  });
  const [isOpen, setIsOpen] = useState(false);
  const datePickerRef = useRef(null);
  const {
    data: overview,
    isLoading,
    error,
  } = useGetOverview({
    start_date: timePeriod?.startDate,
    end_date: timePeriod?.endDate,
    year: dayjs().year(),
  });
  const [_document, setDocument] = useState<any>(null);
  const dropDownRef = useRef(null);

  const timeOptions = [
    {label: 'Last 24 hours', value: moment().subtract(1, 'day').format('YYYY-MM-DD')},
    {label: 'Last week', value: moment().subtract(1, 'week').format('YYYY-MM-DD')},
    {label: 'Last month', value: moment().subtract(1, 'month').format('YYYY-MM-DD')},
    {label: 'Select Range', value: 'custom', type: 'date', disabled: true},
  ];

  const handleTimePeriodChange = (selectedOption: {label: string; value: string}) => {
    if (selectedOption.value === 'custom') {
      setTimePeriod({
        startDate: customDate?.startDate ?? moment().toISOString(),
        endDate: customDate?.endDate ?? moment().toISOString(),
      });
    } else {
      setTimePeriod({
        startDate: moment(selectedOption.value, 'YYYY-MM-DD').startOf('day').toISOString(),
        endDate: moment().endOf('day').toISOString(),
      });
    }
  };

  useEffect(() => {
    if (customDate?.startDate && customDate?.endDate) {
      setTimePeriod({
        startDate: customDate.startDate,
        endDate: customDate.endDate,
      });
      setIsOpen(false);
    }
  }, [customDate]);

  useEffect(() => {
    setDocument(_document);
  }, []);

  const handleAddWorkflow = () => {
    router.push('/admin/automation/workflows/add/basic_info');
  };

  return (
    <div className={styles.overview_container}>
      <div className={styles.overview_top_container}>
        <div className={styles.overview_header}>
          <h5>Automation Overview</h5>
          <ButtonUi color="white" variant="contained" onClick={handleAddWorkflow}>
            + Create Workflow
          </ButtonUi>
        </div>
        <div className={styles.overview_card_container}>
          <AutomationOverviewCard title="candidate Contacts" value={overview?.total_candidates ?? 0} tab='employee'/>
          <AutomationOverviewCard title="employer contacts" value={overview?.total_employers ?? 0} tab='employer'/>
          <AutomationOverviewCard title="Active Workflows" value={overview?.total_active_workflows ?? 0} />
        </div>
      </div>
      <div className={styles.overview_statistic}>
        <div className={styles.statistic_header} ref={dropDownRef}>
          <h2>Statistics</h2>
          <div ref={datePickerRef}>
            <CustomOptionDropdown
              open={isOpen}
              setOpen={setIsOpen}
              items={timeOptions}
              labelExtractor={(item: any) => item.label}
              valueExtractor={(item: any) => item.value}
              onSelect={handleTimePeriodChange}
              placeholder="Last 24 hours"
              customContent={(item: any) =>
                item.type === 'date' && (
                  <div className={styles.date_range_container}>
                    <DatePicker
                      getPopupContainer={() => datePickerRef.current || document.body}
                      onChange={(date, dateString) => {
                        setCustomDate({
                          startDate: date?.startOf('day').toISOString() ?? null,
                          endDate: customDate?.endDate ?? null,
                        });
                      }}
                      value={customDate.startDate ? dayjs(customDate?.startDate) : null}
                      popupClassName={styles.popupClassName}
                    />
                    <DatePicker
                      getPopupContainer={() => datePickerRef.current || document.body}
                      onChange={(date, dateString) => {
                        setCustomDate({
                          startDate: customDate?.startDate ?? null,
                          endDate: date?.endOf('day').toISOString() ?? null,
                        });
                      }}
                      value={customDate?.endDate ? dayjs(customDate?.endDate) : null}
                      popupClassName={styles.popupClassName}
                    />
                  </div>
                )
              }
              optionClassName={styles.time_period_dropdown}
              outSideRef={dropDownRef}
            />
          </div>
        </div>
        <div className={styles.statistic_card_section}>
          <div className={styles.statistic_card_container}>
            <div className={styles.line}></div>
            <div className={styles.statistic_card}>
              <p>Delivered Emails</p>
              <div className={styles.card_content}>
                <h6>{overview?.delivered?.count ?? 0}</h6>
                <span>{overview?.delivered?.percentage ?? 0}%</span>
              </div>
            </div>
          </div>
          <div className={styles.statistic_card_container}>
            <div className={styles.line}></div>
            <div className={styles.statistic_card}>
              <p>Opened </p>
              <div className={styles.card_content}>
                <h6>{overview?.opened?.count ?? 0}</h6>
                <span>{overview?.opened?.percentage ?? 0}%</span>
              </div>
            </div>
          </div>
          <div className={styles.statistic_card_container}>
            <div className={styles.line}></div>
            <div className={styles.statistic_card}>
              <p>Spam </p>
              <div className={styles.card_content}>
                <h6>{overview?.spam?.count ?? 0}</h6>
                <span>{overview?.spam?.percentage ?? 0}%</span>
              </div>
            </div>
          </div>
        </div>
        {isLoading ? (
          <div className={styles.loading_container}>
            <Spinner
              style={{
                color: '#0070F5',
              }}
            />
          </div>
        ) : (
          overview && <EmailStatisticsChart props={overview} />
        )}
      </div>
    </div>
  );
};
