.search_container {
  display: flex;
  width: 320px;
  flex-direction: column;
}
.search_container input {
  height: 32px;
}
.data_point_container {
  border-radius: 8px;
  background: #fff;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.data_point_container h4 {
  color: #191919;
  font-size: 22px;
  font-weight: 500;
  line-height: 120%;
}
.data_point {
  color: #0070f5;
  font-size: 18px;
  font-weight: 600;
  line-height: 160%;
}
.user_type_container {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}
.user_type {
  display: flex;
  padding: 5px 8px;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: #eee;
  color: #2c2c2c;
  font-size: 16px;
  line-height: 140%;
}
.data_header_container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  align-self: stretch;
}
.dropdown_container {
  display: flex;
  align-items: center;
  gap: 16px;
}
