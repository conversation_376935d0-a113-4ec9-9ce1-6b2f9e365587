import React, {useState, useEffect} from 'react';
import {getActiveEmployer, getActiveEmployee, getActiveJobs} from '@/lib/adminapi';
import {exportMultipleChartsToPdf} from '@/lib/utils';
import Link from 'next/link';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement,
} from 'chart.js';
import {Line} from 'react-chartjs-2';
import dayjs from 'dayjs';
import {Button, Segmented} from 'antd';
import ErrorHandler from '@/lib/ErrorHandler';
import LoadingIndicator from '@/components/Common/LoadingIndicator';
import {ReloadOutlined} from '@ant-design/icons';
import axios from 'axios';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, PointElement, LineElement);

export default function Dashboard() {
  const [totalEmployer, setTotalEmployer] = useState([]);
  const [totalEmployee, setTotalEmployee] = useState([]);
  const [totalJobs, settoalJobs] = useState([]);
  const [totalChartActiveJobs, setTotalChartActiveJobs] = useState([]);
  const [totalChartCandidates, setTotalChartCandidates] = useState([]);
  const [totalChartEmployer, setTotalChartEmployer] = useState([]);
  const [chartLabelsData, setChartLabelsData] = useState([]);
  const [statsFilter, setStatsFilter] = useState<{time: number; interval: string}>({time: 12, interval: 'months'});
  const [loadingStats, setLoadingStats] = useState(false);
  const [reload, setReload] = useState(false);

  useEffect(() => {
    const cancelTokenSource = axios.CancelToken.source();
    const config = {
      cancelToken: cancelTokenSource.token,
      params: {
        time_type: statsFilter?.interval,
        time_val: statsFilter?.time,
      },
    };

    setLoadingStats(true);
    axios
      .get(`admin/settings/chart-data`, config)
      .then(response => {
        if (response) {
          setLoadingStats(false);
          const labels = response.data.map((d: any) =>
            dayjs(d.date).format(statsFilter?.interval.includes('month') ? 'MM/YYYY' : 'D/M'),
          );
          const candidates = response.data.map((d: any) => d.candidates);
          const employers = response.data.map((d: any) => d.employers);
          const jobs = response.data.map((d: any) => d.jobs);

          setChartLabelsData(labels);
          setTotalChartCandidates(candidates);
          setTotalChartEmployer(employers);
          setTotalChartActiveJobs(jobs);
        }
      })
      .catch(e => {
        ErrorHandler.showNotification(e);
      });

    return cancelTokenSource.cancel;
  }, [statsFilter, reload]);

  useEffect(() => {
    getActiveEmployer()
      .then(response => {
        setTotalEmployer(response.data);
      })
      .catch(error => {
        console.error(error);
      });
    getActiveEmployee()
      .then(response => {
        setTotalEmployee(response.data);
      })
      .catch(error => {
        console.error(error);
      });
    getActiveJobs()
      .then(response => {
        settoalJobs(response.data);
      })
      .catch(error => {
        console.error(error);
      });
  }, []);

  const data: any = {
    labels: chartLabelsData,
    datasets: [
      {
        label: 'Candidates',
        data: totalChartCandidates,
        borderColor: 'rgb(255, 141, 116)',
        backgroundColor: 'rgb(255, 141, 116)',
      },
      {
        label: 'Active Jobs',
        data: totalChartActiveJobs,
        borderColor: 'rgb(72, 229, 194)',
        backgroundColor: 'rgb(72, 229, 194)',
      },
      {
        label: 'Employers',
        data: totalChartEmployer,
        borderColor: 'rgb(253, 202, 64)',
        backgroundColor: 'rgb(253, 202, 64)',
      },
    ],
  };

  // Make chart data available for PDF export
  useEffect(() => {
   (window as Window & { chartData?: any }).chartData = data;
  }, [data]);

  const chartOptions = {
    responsive: true,
    stacked: false,
    plugins: {
      htmlLegend: {
        // ID of the container to put the legend in
        containerID: 'legend-container',
      },
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        type: 'linear' as const,
        display: false,
        position: 'left' as const,
      },
      y1: {
        type: 'linear' as const,
        display: false,
        position: 'right' as const,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return (
    <>
      <div className="dash-right">
        <h1>Home</h1>
        <div className="row mt-4">
          <div className="col-lg-4 col-md-6">
            <Link href="/admin/jobs">
              <div className="dash-card d-c-1">
                <div className="row">
                  <div className="col-6">
                    <h5 className="dash-card-h5">{totalJobs}</h5>
                  </div>
                  <div className="col-6">
                    <div className="text-right">
                      <h6>Active Jobs</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-4 col-md-6">
            <Link href="/admin/employers">
              <div className="dash-card d-c-2">
                <div className="row">
                  <div className="col-6">
                    <h5 className="dash-card-h5">{totalEmployer}</h5>
                  </div>
                  <div className="col-6">
                    <div className="text-right">
                      <h6>Employers</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-lg-4 col-md-6">
            <Link href="/admin/employees">
              <div className="dash-card d-c-3">
                <div className="row">
                  <div className="col-6">
                    <h5 className="dash-card-h5">{totalEmployee}</h5>
                  </div>
                  <div className="col-6">
                    <div className="text-right">
                      <h6>Candidates</h6>
                      <p className="f-12">View All</p>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
        <p className="over mt-4 mb-4">Overview</p>
        <div style={{background: '#fff', padding: '15px', borderRadius: 9}}>
          <div className="chart_section">
            <LoadingIndicator visible={loadingStats} />
            <div className="row" id="legend-container">
              <div className="col-sm-9">
                <Segmented
                  options={[
                    {label: '12 months', value: '12-months'},
                    {label: '6 months', value: '6-months'},
                    {label: '30 days', value: '30-days'},
                    {label: '7 days', value: '7-days'},
                  ]}
                  onChange={val => {
                    // @ts-ignore
                    const values = val.split('-');
                    setStatsFilter({time: parseInt(values[0]), interval: values[1]});
                  }}
                />
              </div>
              <div className="col-sm-3 text-right">
                <Button type={'text'} onClick={() => setReload(!reload)} icon={<ReloadOutlined />}>
                  Reload
                </Button>
                <button
                  style={{
                    padding: '7px 20px 7px 20px',
                    background: '#ebf1f9',
                    border: '2px solid #0055ba',
                    color: '#0055ba',
                    fontSize: '18px',
                    fontWeight: '600',
                    borderRadius: '10px',
                  }}
                  onClick={exportMultipleChartsToPdf}>
                  Export PDF
                </button>
              </div>
            </div>
            <Line
              data={data}
              //plugins={[htmlLegendPlugin]}
              options={chartOptions}
            />
          </div>
        </div>
      </div>
    </>
  );
}
