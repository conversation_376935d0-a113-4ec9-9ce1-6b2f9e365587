.tabButton {
  display: flex;
  padding: 6px 20px;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: rgba(0, 85, 186, 0.04);
  color: #0055ba;
  font-size: 18px;
  font-weight: 400;
  line-height: 160%;
  transition:
    background 0.3s ease,
    color 0.3s ease;
  margin: 0 !important;
  box-shadow: none !important;
  cursor: pointer;
}
.tab {
  display: flex;
  padding: 8px 8px 0px 8px;
  flex-direction: column;
  align-items: center;
  border-radius: 4px 4px 0px 0px;
  background: #0055ba;
  cursor: pointer;
  font-size: 16px;
  line-height: 140%;
  transition:
    background 0.3s ease,
    color 0.3s ease;
}

.activeButtonTab {
  background: #0055ba;
  color: #ffffff;
}

.tabButton {
  border: none;
  outline: none;
  cursor: pointer;
  font-size: 14px;
}
.customTabs {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}
.customTabs {
  display: flex;
  flex-direction: column;
}

.tabHeaders {
  display: flex;
  gap: 8px;
}

.tabContent {
  width: 100%;
}
.firstActiveTabBorder {
  display: flex;
  height: 2px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 2px 2px 0px 0px;
  background: #fff;
  margin-top: 8px;
}
.activeTabBorder {
  display: flex;
  height: 2px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 2px 2px 0px 0px;
  background: #0070f5;
  margin-top: 8px;
}
.highlightTab {
  color: #cfe5ff;
  background-color: transparent;
  font-size: 16px;
  font-weight: 600;
  line-height: 140%;
}

.activeButtonTab {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px 4px 0 0;
  background: #fff;
  color: #0055ba;
}

.tabHeaders {
  display: flex;
  gap: 8px;
}

.tabContent {
  width: 100%;
}
.highlightedHeader {
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 12px;
  border-radius: 8px;
  background: #ebf4ff;
  width: fit-content;
}
