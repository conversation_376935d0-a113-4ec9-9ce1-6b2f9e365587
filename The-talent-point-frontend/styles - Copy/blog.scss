.breadcrumb {
  margin-top: 20px;
  ol {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;

    li {
      font-weight: 600;
      font-size: 13px;
      color: #bababa;

      &:not(:last-child):after {
        content: '/';
        display: inline-block;
        margin: 0 5px;
      }

      &:last-child {
        color: #0070f5;
      }
    }
  }
}

.list-tags {
  display: flex;
  margin: 20px 0 20px 0;
  padding: 0;
  list-style: none;
  color: var(--primary-color);
  flex-wrap: wrap;

  li {
    font-weight: 600;
    background: #cfe5ff;
    border-radius: 24px;
    padding: 8px 20px;
    font-size: 13px;
    margin: 4px 5px 4px 0;
    text-transform: capitalize;
  }
}

.blog-author-card {
  display: flex;
  margin-top: 30px;
  align-items: center;

  img {
    width: 48px;
    border-radius: 30px;
    box-shadow: 2px 0 7px rgba(0, 0, 0, 0.1);
    margin-right: 12px;
  }

  .name {
    margin: 0;
  }
}

.tab-card-box {
  box-shadow: 0 2px 8px rgba(21, 21, 21, 0.1);
  border-radius: 16px;
  padding: 0;
  height: 100%;
  margin-bottom: 20px;

  .image {
    width: 100%;
    border-radius: 10px;
  }

  .title {
    color: var(--primary-color);
  }
}

@media screen and (max-width: 500px) {
  .breadcrumb {
    ol {
      flex-direction: column;
    }
  }

  .list-tags {
    li {
      padding: 4px 12px;
      font-size: 12px;
    }
  }
}

.blog-author-card {
  .name {
    font-family: var(--opensans-font) !important;
  }
  small {
    font-family: var(--opensans-font) !important;
  }
}
.blog-details {
  .tab-card-box {
    box-shadow: unset;
  }
  .related-resources .list-tags li {
    font-family: var(--opensans-font) !important;
  }
}
.blog-outer-part {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-flow: row wrap;
  .left-side-outer {
    width: 148px;
    .left-side {
      width: 100%;
      padding-left: 0;
      padding-right: 0;
      h4 {
        color: #0b1347;
        font-family: var(--opensans-font) !important;
      }
      .know-your-para {
        font-family: var(--opensans-font) !important;
        font-weight: 300;
        line-height: 16.8px;
      }
      .know-your-btn {
        font-weight: 500;
        width: 100%;
      }
    }
  }
  .middle-part {
    width: calc(100% - 454px);
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
    .blog-main-content {
      p {
        font-family: var(--opensans-font) !important;
        color: #151515;
        a {
          color: #0070f5;
        }
      }
      .article-box {
        margin-bottom: 15px;
        p {
          font-family: var(--archivo-font), sans-serif !important;
          margin-bottom: 20px;
        }
        .article-ul li {
          font-family: var(--archivo-font), sans-serif !important;
          font-weight: 700;
          list-style: disc;
          margin-top: 0;
          margin-left: 40px;
          a {
            color: #0070f5;
          }
          &:first-child {
            margin-left: 20px;
          }
        }
      }
      .expand-job {
        h5 {
          color: #2c2c2c;
          font-size: 22px;
          line-height: 26.4px;
          font-weight: 700;
        }
        ul li {
          a {
            font-family: var(--opensans-font) !important;
            .fa-solid {
              color: #0070f5;
            }
          }
        }
      }
    }
    .create-profile-sec {
      p {
        font-family: var(--opensans-font) !important;
      }
      .cps-btn {
        border-radius: 16px;
      }
    }
    .apply-ready-block {
      min-height: 208px;
      h3 {
        font-family: var(--opensans-font) !important;
        font-weight: 600 !important;
      }
      .btn-a {
        background: #0070f5 !important;
        border: 0 !important;
        &:hover {
          background: var(--background-color_5) !important;
        }
      }
    }
    .blog-share-sec {
      .icon-soc {
        img {
          padding: 0;
          width: 20px;
          height: auto;
          vertical-align: top;
        }
        button {
          margin-right: 12px;
          height: 20px;
        }
        .copy-clipbpard {
          img {
            width: 24px !important;
          }
        }
      }
    }
  }
  .third-box {
    width: 306px;
    padding-left: 0;
    padding-right: 0;
    .blog-overlay {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      a {
        margin-top: auto;
        border: 0;
        word-wrap: break-word;
        white-space: normal;
        word-break: break-all;
      }
      button {
        margin-top: auto;
        border: 0;
        word-wrap: break-word;
        white-space: normal;
        word-break: break-all;
      }
      .sidebar-title {
        font-weight: 700 !important;
        font-family: var(--archivo-font), sans-serif !important;
      }
      .blog_looking_to_hire_head {
        font-size: 26px !important;
        line-height: 31.2px !important;
      }
      .blog_looking_to_hire_sub_head {
        font-family: var(--opensans-font) !important;
      }
      p {
        word-wrap: break-word;
        white-space: normal;
        word-break: break-all;
      }
    }
  }
}

@media screen and (max-width: 1499px) {
  .apply-ready-block {
    h3,
    h4 {
      max-width: 40%;
    }
  }
}

@media screen and (max-width: 1024px) {
  .blog-outer-part {
    flex-flow: row wrap;
    .left-side-outer {
      width: 100%;
      order: 3;
      display: flex;
      flex-flow: row wrap;
      justify-content: flex-start;
      align-items: stretch;
      margin-left: 1.33%;
      .left-side {
        max-width: 32%;
        margin-right: 1.33%;
        display: flex;
        flex-direction: column;
        align-items: center;
        .know-your-btn {
          margin-top: auto;
        }
        .know-your-para {
          text-align: center;
        }
      }
    }
    .middle-part {
      width: 100%;
      order: 1;
      p {
        font-size: 16px;
        line-height: 25.6px;
        margin-bottom: 25px;
      }
      .expand-job ul li a {
        font-size: 16px;
        line-height: 25px;
      }
    }
    .third-box {
      width: 100%;
      order: 2;
      display: flex;
      flex-flow: row wrap;
      justify-content: flex-start;
      align-items: stretch;
      margin-left: 1.33%;
      .third-box-item {
        max-width: 32%;
        margin-right: 1.33%;
      }
      .blog-overlay {
        .blog_looking_to_hire_head {
          font-size: 23px !important;
          line-height: 27.6px !important;
          text-align: center;
        }
        .blog_looking_to_hire_sub_head {
          font-size: 16px;
          text-align: center;
        }
        button {
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }
  .blog-main-outer {
    display: flex;
    flex-direction: column;
    .blog-top {
      order: 1;
    }
    .blog-part {
      order: 2;
    }
    .blog-details {
      order: 3;
      .blog-cards {
        .f-45 {
          font-size: 40px !important;
          line-height: 48px !important;
        }
      }
      .tab-card-box {
        display: flex;
        flex-direction: column;
      }
      .related-resources {
        align-items: stretch;
        margin-top: 45px;
      }
      .tab-card-box.related-resource-block h4 {
        font-size: 19px;
        font-weight: 700;
        line-height: 22.6px;
        margin-bottom: 30px;
      }
      .related-resources .list-tags {
        margin: 20px 0 13px;
        li {
          font-size: 11px;
          line-height: 15.4px;
        }
      }
      .blog-author-card {
        margin-top: auto;
        img {
          width: 40px;
          border-radius: 200px;
        }
        .name {
          font-size: 16px;
          color: #2e3135;
          line-height: 25.6px;
        }
        small {
          font-size: 13px;
          line-height: 18.2px;
        }
      }
    }
  }
  .blog-part {
    padding: 20px 0 16px;
  }
}

@media screen and (max-width: 991px) {
  .blog-main-outer .blog-details .related-resources {
    .col-md-4 {
      width: 50%;
      margin-bottom: 25px;
    }
  }
  .blog-part {
    padding: 20px 0 16px;
  }
  .blog-title h1 {
    font-size: 31px;
    line-height: 37.2px;
  }
  .blog-top .breadcrumb {
    margin-bottom: 15px;
  }
  .blog-author-card .name {
    font-size: 18px;
    line-height: 28.8px;
  }
  .blog-outer-part .middle-part .blog-main-content .article-box .article-ul li {
    font-size: 16px;
    line-height: 25.6px;
  }
  .article-box {
    margin-bottom: 50px;
  }
  .blog-main-content {
    h2 {
      font-size: 40px;
      line-height: 48px;
    }
    h3 {
      font-size: 33px;
      line-height: 39.6px;
    }
    h5 {
      font-size: 23px;
      line-height: 27.6px;
    }
  }
  .blog-outer-part .middle-part .blog-main-content .expand-job h5 {
    font-size: 19px;
    line-height: 22.8px;
  }
  .apply-ready-block .btn-a {
    max-width: 153px;
  }
}

@media screen and (max-width: 767px) {
  .blog-outer-part .middle-part {
    padding-left: 0px;
    padding-right: 0px;
  }
  .blog-outer-part .middle-part .blog-main-content .article-box {
    margin-bottom: 50px;
  }
  .create-profile-sec {
    flex-direction: column;
    padding-left: 18px;
    padding-right: 18px;
    align-items: flex-start;
    h3 {
      font-size: 24px;
      font-weight: 700;
      line-height: 33px;
    }
  }
  .blog-outer-part .middle-part .create-profile-sec .cps-btn {
    font-size: 16px;
    line-height: 19.2px;
  }
  .blog-outer-part .third-box {
    margin-left: 0;
    .third-box-item {
      max-width: 100%;
      margin-right: 0;
      width: 100%;
    }
    .blog-overlay button {
      line-height: 19.2px;
    }
  }
  .blog-outer-part .left-side-outer {
    margin-left: 0;
    .left-side {
      max-width: 100%;
      margin-right: 0;
    }
  }
  .blog-main-outer .blog-details .related-resources .col-md-4 {
    width: 100%;
  }
  .blog-cards .justify-content-end {
    justify-content: flex-start !important;
  }
  .blog-main-outer .blog-details .blog-cards .f-45 {
    margin-bottom: 20px;
  }
  .apply-ready-block h3,
  .apply-ready-block h4 {
    max-width: 100%;
  }
}
