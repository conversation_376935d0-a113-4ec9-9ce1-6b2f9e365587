.uae-new {
  background: #69a5ff;

  h1 {
    color: var(--color_4);
    font-size: 35px;
    font-weight: 700;
    margin: 17px 0 0 0;
  }
}

.glas {
  width: 175px;
}

.bg-dark-blue {
  background: var(--primary-color);
}

.explore-jobs {
  color: var(--primary-color);
}

.jobs-by-location {
  h4 {
    font-size: 22px;
    line-height: 26.4px;
    color: var(--color_9);
    font-weight: 700;
  }

  border: 1px solid #d9d9d9;
  border-radius: 16px;
  padding: 40px;
}

.view-right-all {
  a {
    color: var(--color_6);
    line-height: 21.6px;
    font-size: 18px;
    font-weight: 700;
    text-decoration: none;
    text-transform: capitalize;
  }
  button {
    color: var(--color_6);
    line-height: 21.6px;
    font-size: 18px;
    font-weight: 700;
    text-decoration: none;
    background: none;
    padding: 0;
    border: 0;
    text-transform: capitalize;
  }
}
ul.uae-part.uae4block {
  display: flex;
  flex-wrap: wrap;
  li {
    width: 25%;
  }
}

@media (max-width: 767px) {
  ul.uae-part.uae4block {
    li {
      width: 100%;
    }
  }
}

ul.uae-part {
  padding: 0;
  margin: 0;

  li {
    list-style: none;

    color: var(--color_11);
    line-height: 45.4px;
    font-size: 18px;
    text-decoration: none;
    font-weight: 400;
    transition: 0.5s;
    cursor: pointer;

    &:hover {
      color: var(--color_6);
    }
  }
}
/*new*/
ul.uae-part li.select {
  color: var(--color_6);
}

.span-highlight {
  color: var(--color_26);
}

@media (max-width: 981px) {
  .jobs-by-location {
    padding: 16px;

    h4 {
      font-size: 19px;
      line-height: 22.8px;
    }
  }

  .view-right-all {
    a {
      line-height: 19.2px;
      font-size: 16px;
      font-weight: 600;
    }
  }

  ul.uae-part {
    a {
      line-height: 28.8px;
      font-size: 16px;
    }
  }

  img.glas {
    display: none;
  }
}

/**********jobs subdropown css *********/

$font-family_1: var(--opensans-font);
$background-color_1: lightsalmon;

#jobs-main-id {
  .navbar-nav-main {
    .job-link {
      color: var(--color_47);
      text-decoration: none;
      list-style: none;
    }
  }

  .nav-item {
    list-style: none;
  }

  .dropdown-item-jab {
    font-weight: var(--font_weight_600);
    font-size: var(--font_16);
    line-height: var(--font_19_line-height);
    color: var(--color_8) !important;
    padding: 0 13px;
    font-family: $font-family_1;
    text-decoration: none;
    display: block;
  }

  .dropend {
    .dropdown-toggle {
      color: var(--color_8) !important;
      font-size: 16px;
      padding: 8px 12px 8px 12px !important;
      line-height: 22.4px;
      font-weight: 400;

      &::after {
        vertical-align: 0;
        position: absolute;
        right: 14px;
        top: 4px;
        content: '';
        border-left: 0px;
        font-family: monospace;
        font-size: 17px;
        font-weight: 100;
      }
    }

    &:hover {
      > .dropdown-menu {
        display: block;
        margin-top: 0.125em;
        margin-left: 0.125em;
        width: 264px;
        transition: all 0.2s linear;
        background-color: var(--background-color_4);
        border-radius: 4px;
        border: none;
        box-shadow: 0 3px 8px 0 rgba(88, 88, 88, 0.2);
        position: absolute;
        top: 22px;
        position: absolute;
        top: 0px;
        left: 100%;
      }
    }
  }

  .dropdown-item {
    &:hover {
      background-color: $background-color_1;
      color: var(--color_47);
    }
  }

  .dropdown {
    .dropdown-menu {
      display: none;
    }

    &:hover {
      > .dropdown-menu {
        display: block;
        margin-top: 0.125em;
        margin-left: 0.125em;
        width: 232px;
        transition: all 0.2s linear;
        background-color: var(--background-color_4);
        border-radius: 4px;
        border: none;
        box-shadow: 0 3px 8px 0 rgba(88, 88, 88, 0.2);
        position: absolute;
        top: 22px;
      }
    }
  }
}

#sub-id {
  .dropdown-item-jab {
    font-size: 16px;
    line-height: 22.4px;
    font-weight: 400;
    color: var(--color_8);
    padding: 8px 12px !important;
  }
}

div#jobs-main-id {
  ul.dropdown-menu {
    li {
      transition: 0.5s;

      &:hover {
        background: #ebf4ff;
        color: var(--color_6);

        a {
          color: var(--color_6) !important;
          font-weight: 600;
        }

        ul.dropdown-menu {
          li {
            a {
              color: var(--color_8) !important;
            }
          }
        }
      }

      ul#sub-id {
        background: #ebf4ff;
        padding: 12px 0;

        li {
          a {
            &:hover {
              color: var(--color_6) !important;
            }
          }
        }
      }
    }
  }
}

div#jobs-main-id {
  ul.dropdown-menu {
    li {
      position: unset;

      a {
        position: relative;
      }

      ul#sub-id {
        top: 0 !important;
        position: absolute;
        bottom: unset !important;
        margin: 0;
        border-radius: 0 4px 4px 0 !important;
        height: 250px;
      }
    }
  }
}

#jobs-main-id {
  .dropdown {
    &:hover {
      > .dropdown-menu {
        height: 250px;
        margin: 0px 0 0 0 !important;
        border-radius: 4px 0 0 4px;
        width: 320px;
        padding-top: 8px;
        top: 80px;
      }
    }
  }
}

$background-color_1: #ebf4ff;
$background-color_2: unset;
$border-color_1: unset;

div#navbarSupportedContent {
  #accordion-nav {
    a.accordion-button {
      color: var(--color_6) !important;
      font-size: 16px;
      line-height: 19.2px;
      font-weight: 600;
      text-decoration: none;
    }

    .accordion-button {
      &:focus {
        border-color: $border-color_1 !important;
        box-shadow: unset !important;
      }

      font-size: 16px;
      line-height: 22.4px;
      font-weight: 600;
      color: var(--color_6) !important;
      transition: 0.6s;
      padding: 17px 15px !important;

      &:not(.collapsed) {
        background-color: $background-color_1 !important;
        box-shadow: unset !important;
        border-bottom: 1px solid #cfe5ff !important;
      }
    }

    .accordion-button.collapsed {
      color: var(--color_9) !important;
      font-weight: 400;
    }

    ul.faq-nav {
      li {
        a {
          font-size: 16px;
          line-height: 22.4px;
          font-weight: 400;
          text-decoration: none;
          color: var(--color_9) !important;
          transition: 0.6s;
          padding: 8px 0 !important;
          display: block !important;

          &:hover {
            color: var(--color_6) !important;
            font-weight: 600;
          }
        }

        list-style: none !important;
        margin: 0 !important;
      }

      padding: 0;
    }

    .accordion-body {
      padding: 0px 20px !important;
      background: #ebf4ff !important;
    }

    .accordion-item {
      background-color: $background-color_2 !important;
      border: unset !important;
    }
  }
}

div#jobs-main-id {
  ul.dropdown-menu {
    li {
      ul#sub-id {
        top: 1px !important;
        position: absolute;
        bottom: unset !important;
        margin: 0;
        border-radius: 0 !important;
        box-shadow: unset;

        li {
          a {
            font-weight: 400;

            &:hover {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

.ac-label {
  font-weight: 700;
  position: relative;
  display: block;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;

  &:after {
    content: '';
    position: absolute;
    display: block;
    right: 10px;
    top: 5px;
    line-height: 2.25em;
    text-align: center;
    transition: background-color 0.15s ease-in-out;
    font-weight: 100;
    font-family: monospace;
    transform: rotate(90deg);
    font-size: 30px;
    width: 10px;
    height: 17px;
    background-image: url('/right-arrow.svg');
    background-size: cover;
  }
}

.ac-input {
  &:checked + .ac-label {
    &:after {
      content: '';
      position: absolute;
      display: block;
      right: 10px;
      top: 5px;
      line-height: 2.25em;
      text-align: center;
      transition: background-color 0.15s ease-in-out;
      font-weight: 100;
      font-family: monospace;
      transform: rotate(90deg);
      font-size: 30px;
      width: 10px;
      /* Set the width of the image */
      height: 17px;
      /* Set the height of the image */
      background-image: url('/right-arrow.svg');
      /* Path to your image */
      background-size: cover;
      /* Adjust the size of the image */
    }

    ~ {
      .ac-text {
        opacity: 1;
        height: auto;
      }
    }
  }

  display: none;
}

.ac-text {
  opacity: 0;
  height: 0;
  transition: opacity 0.5s ease-in-out;
  overflow: hidden;
}

.ac-sub-text {
  opacity: 0;
  height: 0;
  transition: opacity 0.5s ease-in-out;
  overflow: hidden;
  padding: 0 1em 0 2em;
}

.ac-sub {
  .ac-input {
    &:checked {
      ~ {
        .ac-sub-text {
          opacity: 1;
          height: auto;
        }
      }

      + {
        .ac-label {
          &:after {
            left: 0;
            background: none;
          }
        }

        label {
          background: none;
        }
      }
    }
  }

  .ac-label {
    background: none;
    font-weight: 600;
    padding: 0.5em 2em;
    margin-bottom: 0;

    &:checked {
      background: none;
      border-bottom: 1px solid whitesmoke;
    }

    &:after {
      left: 0;
      background: none;
    }

    &:hover {
      background: none;
    }
  }
}

.ac-label.border-bottom-2 {
  &:checked {
    border-bottom: 1px solid #d9d9d9;
    padding-bottom: 17px;
  }
}

@media (min-width: 992px) {
  #navbarSupportedContent {
    .single-menu-space {
      padding: 28px 13px !important;
    }
  }
}

@media (max-width: 991px) {
  .jobs-mobile-menu {
    margin: 0px !important;
  }
}

.hover-link-1:hover {
  .black-arrow-1 {
    display: none;
  }

  .hover-blue-arrow-1 {
    display: block;
  }
}

.hover-link-2:hover {
  .black-arrow-2 {
    display: none;
  }

  .hover-blue-arrow-2 {
    display: block;
  }
}

.hover-blue-arrow-2,
.hover-blue-arrow-1 {
  display: none;
}

ul.location-list {
  li {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 12px;
  }
}
