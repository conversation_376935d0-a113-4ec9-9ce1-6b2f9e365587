:root {
  --primary-color: #0055ba;
  --color_1: #0055ba;
  --color_2: #99c8ff;
  --color_3: #cfe5ff;
  --color_4: #fff;
  --color_5: #002552;
  --color_6: #0070f5;
  --color_7: #00387a;
  --color_8: #191919;
  --color_9: #2c2c2c;
  --color_10: #999;
  --color_11: #747474;
  --color_12: #d9d9d9;
  --color_13: #cc3f40;
  --color_14: #3d9f79;
  --color_15: #bababa;
  --color_16: #051b44;
  --color_17: #06f;
  --color_18: #c1c4d6;
  --color_19: #4d4d4d;
  --color_20: #0c5a14;
  --color_21: #d04e4f;
  --color_22: #ffe6e6;
  --color_23: #fd7373;
  --color_24: #fffceb;
  --color_25: #d57b11;
  --color_26: #fdca40;
  --color_27: #151515;
  --color_28: #0eb1d2;
  --color_29: #191d23;
  --color_30: #eee;
  --color_31: #f7f8f9;
  --color_32: #292929;
  --color_33: #ffe2db;
  --color_34: #fee6a4;
  --color_35: #d6d6d6;
  --color_36: #ebf1f9;
  --color_37: #797979;
  --color_38: #001a38;
  --color_39: #ddd;
  --color_40: #ebf4ff;
  --color_41: #fff7e1;
  --color_42: #ffc3c3;
  --color_43: #f0d05c;
  --color_44: #d57b11;
  --color_45: #f5f9fc;
  --color_46: #ebf3f9;
  --color_47: #000;
  --color_48: #ffc3c3;
  --color_49: #f9f9f9;
  --color_50: #48e5c2;
  --color_51: #ffebeb;
  --c-fff: #fff;
  --color_52: #c2d6ef;
  --color_53: #00387b;
  --background-color_1: #0055ba;
  --background-color_2: #99c8ff;
  --background-color_3: #cfe5ff;
  --background-color_4: #fff;
  --background-color_5: #00387a;
  --background-color_6: #002552;
  --background-color_7: #cfe5ff;
  --background-color_8: #edeff5;
  --background-color_9: #c1c4d6;
  --background-color_10: #e9f1ff;
  --background-color_11: #dcf2ea;
  --background-color_12: #e9f1ff;
  --background-color_13: #e9f1ff;
  --background-color_14: #e9f1ff;
  --background-color_15: #f5f9ff;
  --border-1: 2px solid #0055ba;
  --border-2: 2px solid #99c8ff;
  --border-3: 2px solid #cfe5ff;
  --border-4: 2px solid #fff;
  --border-5: 2px solid #00387a;
  --border-6: 2px solid #002552;
  --border-7: 2px solid #cfe5ff;
  --border-8: 2px solid #7474741f;
  --border-9: 2px solid #7474741f;
  --border-10: 2px solid #bababa;
  --border-11: 2px solid #7b61ff;
  --font_48: 48px;
  --font_48_line-height: 57.6px;
  --font_45: 45px;
  --font_45_line-height: 54px;
  --font_40: 40px;
  --font_40_line-height: 48px;
  --font_37: 37px;
  --font_37_line-height: 44.4px;
  --font_33: 33px;
  --font_33_line-height: 39.6px;
  --font_31: 31px;
  --font_31_line-height: 37.2px;
  --font_28: 28px;
  --font_28_line-height: 33.6px;
  --font_26: 26px;
  --font_26_line-height: 31.2px;
  --font_24_line-height: 28.8px;
  --font_23: 23px;
  --font_23_line-height: 27.6px;
  --font_22: 22px;
  --font_22_line-height: 26.4px;
  --padding_22: 16px 32px;
  --font_19: 19px;
  --font_19_line-height: 22.8px;
  --padding_19: 16px 32px;
  --font_18: 18px;
  --font_18_line-height: 21.6px;
  --padding_18: 12px 24px;
  --font_16: 16px;
  --font_16_line-height: 19.2px;
  --font_14: 14px;
  --font_14_line-height: 24px;
  --font_13: 13px;
  --font_13_line-height: 15.6px;
  --font_12: 12px;
  --font_12_line-height: 16.8px;
  --font_11: 11px;
  --font_11_line-height: 13.2px;
  --h1_size: 54px;
  --h1_size_line-height: 26.4px;
  --h1_54_size_line-height: 64.8px;
  --h2_size: 45px;
  --h2_size_line-height: 54px;
  --h3_size: 37px;
  --h3_size_line-height: 44.4px;
  --h4_size: 31px;
  --h4_size_line-height: 37.2px;
  --h5_size: 26px;
  --h5_size_line-height: 31.2px;
  --h6_size: 22px;
  --h6_size_line-height: 26.4px;
  --web_font_16: 16px;
  --web_font_16_line-height: 22.4px;
  --font_weight_300: 300;
  --font_weight_400: 400;
  --font_weight_500: 500;
  --font_weight_600: 600;
  --font_weight_700: 700;
  --font_weight_800: 800;
  --font_weight_900: 900;
  --box-shadow: 0 1px 3px rgba(21, 21, 21, 0.12), 0 2px 5px rgba(21, 21, 21, 0.1), 0 4px 12px rgba(21, 21, 21, 0.12);
  --border_radius_2: 2px;
  --border_radius_8: 8px;
}
.btn-bg-0055BA:hover {
  background: var(--background-color_5) !important;
  border: solid 2px var(--color_7) !important;
  color: var(--color_4) !important;
}
.border-primary-size-22:hover {
  background: #c2d6ef;
}
.border-primary-size-22,
.primary-size-22,
.text-primary-size-22 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_22);
  padding: var(--padding_22);
  line-height: var(--font_22_line-height);
}
.primary-size-22 {
  box-shadow: var(--box-shadow);
  border-radius: var(--border_radius_2);
  color: var(--color_4);
}
.border-primary-size-22,
.text-primary-size-22 {
  background: #fff0;
}
.border-primary-size-22 {
  border-radius: var(--border_radius_2);
}
.text-primary-size-22 {
  border: solid 2px #fff0;
  text-decoration: underline 3px !important;
}
.btn-bg-0055BA {
  background: var(--background-color_1);
  border: solid 2px #0055ba;
}
.btn-bg-00387A {
  background: var(--background-color_5);
  border: solid 2px #00387a;
}
.btn-bg-002552 {
  background: var(--background-color_6);
  border: solid 2px #002552;
}
.border-00387A,
.border-0055BA {
  border: solid 2px #0055ba;
  color: var(--color_1);
}
.border-00387A {
  border: solid 2px #00387a;
}
.border-002552 {
  border: solid 2px #002552;
  color: var(--color_5);
}
.team-m span.f-12,
.text-0070F5 {
  color: var(--color_6);
}
.text-00387A {
  color: var(--color_7);
}
.text-002552 {
  color: var(--color_5);
}
.gap-btn button {
  margin: 8px 4px;
}
.btn-a {
  text-decoration: none;
  outline: 0;
}
.primary-white-size-22 {
  border-radius: var(--border_radius_2);
  font-weight: var(--font_weight_700);
  font-size: var(--font_22);
  padding: var(--padding_22);
  line-height: var(--font_22_line-height);
}
.btn-bg-CFE5FF,
.btn-bg-fff {
  color: var(--color_1) !important;
}
.btn-bg-fff {
  background: var(--background-color_4);
  border: solid 2px #fff;
}
.btn-bg-CFE5FF {
  background: var(--background-color_3);
  border: solid 2px #cfe5ff;
}
.btn-bg-99C8FF {
  background: var(--background-color_2);
  color: var(--color_1) !important;
  border: solid 2px #99c8ff;
}
.border-CFE5FF,
.border-fff {
  border: solid 2px #fff;
  color: var(--color_4);
}
.border-CFE5FF {
  background: rgba(207, 229, 255, 0.24);
}
.border-99C8FF {
  border: solid 2px #99c8ff;
  color: var(--color_2);
}
.text-fff {
  color: var(--color_4);
}
.text-CFE5FF {
  color: var(--color_3);
}
.text-99C8FF {
  color: var(--color_2);
}
.border-primary-size-18,
.primary-size-18 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  padding: var(--padding_18);
}
.primary-size-18 {
  color: var(--color_4);
}
.border-primary-size-18 {
  border-radius: var(--border_radius_2);
  background: #fff0;
}
.border-00387A {
  background: rgba(0, 85, 186, 0.24);
}
.text-primary-size-18 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  padding: var(--padding_18);
  background: #fff0;
  border: solid 2px #fff0;
  text-decoration: underline 3px !important;
}
.border-primary-size-16,
.primary-size-16,
.text-primary-size-16 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  padding: var(--padding_18);
}
.primary-size-16 {
  color: var(--color_4) !important;
  border-radius: var(--border_radius_8);
}
.border-primary-size-16,
.text-primary-size-16 {
  background: #fff0;
}
.border-primary-size-16 {
  border-radius: var(--border_radius_8);
}
.text-primary-size-16 {
  border: solid 2px #fff0;
  text-decoration: underline 3px !important;
}
.border-primary-size-19,
.primary-size-19,
.text-primary-size-19 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_19);
  line-height: var(--font_19_line-height);
  padding: var(--padding_18);
}
.primary-size-19 {
  color: var(--color_4);
}
.border-primary-size-19,
.text-primary-size-19 {
  background: #fff0;
}
.border-primary-size-19 {
  border-radius: var(--border_radius_2);
}
.text-primary-size-19 {
  border: solid 2px #fff0;
  text-decoration: underline 3px !important;
}
.border-primary-size-13,
.primary-size-13,
.text-primary-size-13 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_13);
  line-height: var(--font_13_line-height);
  padding: var(--padding_18);
}
.primary-size-13 {
  color: var(--color_4);
}
.border-primary-size-13,
.text-primary-size-13 {
  background: #fff0;
}
.border-primary-size-13 {
  border-radius: var(--border_radius_2);
}
.text-primary-size-13 {
  border: solid 2px #fff0;
  text-decoration: underline 3px !important;
}
.b-0 {
  border-radius: 0;
}
.b-2 {
  border-radius: 2px;
}
.b-4,
.fields-form {
  border-radius: 4px;
}
.fields-form {
  background: var(--background-color_4);
  border: 1px dashed #7b61ff;
  padding: 40px;
}
.fields {
  width: 100%;
  outline: 0;
  border: 0;
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  color: var(--color_8);
  padding: 16px 10px;
}
.d-flex-icons i {
  color: var(--color_8);
  padding: 19px 0;
}
::placeholder {
  color: var(--color_8);
}
.d-flex-icons {
  display: flex;
  position: relative;
}
.glass-search {
  position: absolute;
  left: 7px;
}
textarea.fields {
  height: 58px;
}
.eye-icon {
  position: absolute;
  right: 5px;
}
.left-sp {
  padding-left: 40px !important;
}
.right-sp {
  padding-right: 40px !important;
}
.form-input {
  background: var(--background-color_4);
  border: 1px dashed #7b61ff;
  border-radius: 4px;
  padding: 40px;
}
.big-input,
.form-input label {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_9);
  width: 100%;
}
.big-input {
  border: var(--border-10);
  border-radius: 4px;
  padding: 0 16px;
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_8);
  height: 35px;
  margin-bottom: 10px;
}
.big-input::placeholder,
.medium-input::placeholder,
.small-input::placeholder {
  color: var(--color_10);
}
.medium-input,
.small-input {
  border: var(--border-10);
  border-radius: 4px;
  padding: 0 12px;
  width: 100%;
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  color: var(--color_8);
  height: 35px;
  margin-bottom: 10px;
}
.small-input {
  padding: 10px 8px;
  height: 32px;
}
.card-box {
  background: var(--background-color_4);
  border-radius: 4px;
  padding: 40px;
  margin-top: 25px;
}
.tab-btn {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_6);
  padding: 12px 14px;
  position: relative;
  background: #fff0;
  border: 0;
}
.tab-btn:focus {
  color: var(--color_1);
}
.tab-btn:focus:active::after {
  background: var(--background-color_1);
}
.tab-btn::after {
  content: '';
  border-radius: 2px 2px 0 0;
  width: 50%;
  height: 2px;
  position: absolute;
  bottom: 0;
  left: 25%;
}
.tab-span {
  background: var(--color_6);
  border-radius: 2px;
  color: var(--color_4);
  padding: 1px;
}
.tab-right-sp {
  margin-right: 2px;
}
.tab-left-sp {
  margin-left: 2px;
}
.tab-btn i {
  font-size: 9px;
  color: var(--color_6);
}
.form-in.input-0055BA label,
.tab-btn.tab-0055BA {
  color: var(--color_1);
}
.tab-btn.tab-0055BA .tab-span,
.tab-btn.tab-0055BA::after,
.tab-btn::after {
  background: var(--color_1);
}
.tab-border {
  border: 2px solid var(--border-3);
  border-radius: 4px 4px 0 0;
}
.tab-btn.tab-bababa {
  color: var(--color_15);
}
.tab-btn.tab-bababa .tab-span,
.tab-btn.tab-bababa::after {
  background: var(--color_15);
}
.tab-btn.tab-747474 {
  color: var(--color_11);
}
.tab-btn.tab-747474 .tab-span,
.tab-btn.tab-747474::after {
  background: var(--color_11);
}
.tab-btn.tab-747474.tab-border {
  border: 2px solid #7474741f;
}
.tab-btn.tab-f-11 {
  font-size: var(--font_11) !important;
  line-height: var(--font_11_line-height) !important;
}
.position-relative {
  position: relative;
}
.form-in i {
  top: 36px;
  right: 12px;
}
.form-in.input-0055BA .big-input {
  border: 1px solid var(--color_1);
}
.form-in.input-0055BA ::placeholder {
  color: var(--color_8) !important;
}
.form-in.input-2C2C2C label {
  color: var(--color_9);
}
.form-in.input-2C2C2C .big-input {
  border: 1px solid var(--color_11);
}
.form-in.input-2C2C2C ::placeholder {
  color: var(--color_11) !important;
}
.form-in.input-D9D9D9 label {
  color: var(--color_12);
}
.form-in.input-D9D9D9 .big-input {
  border: 1px solid var(--color_12);
}
.form-in.input-D9D9D9 ::placeholder {
  color: var(--color_12) !important;
}
.form-in.input-CC3F40 label {
  color: var(--color_13);
}
.form-in.input-CC3F40 .big-input {
  border: 1px solid var(--color_13);
}
.form-in.input-3D9F79 ::placeholder,
.form-in.input-CC3F40 ::placeholder {
  color: var(--color_8) !important;
}
.form-in.input-3D9F79 label {
  color: var(--color_14);
}
.form-in.input-3D9F79 .big-input {
  border: 1px solid var(--color_14);
}
.helper {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  letter-spacing: -0.5px;
}
.font-CC3F40 {
  color: var(--color_13);
}
.font-3D9F79 {
  color: var(--color_14);
}
.eye-icon.medium {
  top: 36px;
}
.eye-icon.small {
  top: 32px;
}
.dflex {
  display: flex;
}
.box-tag {
  padding: 2px 0;
  background: var(--background-color_1);
  border-radius: 2px;
  width: 12px;
  height: 12px;
  font-size: 7px;
  text-align: center;
  color: var(--color_4);
  margin-top: 11px;
  margin-right: 0;
}
.master-fields {
  letter-spacing: -0.5px;
  width: 100%;
  padding: 5px 4px;
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  color: var(--color_16);
  margin-bottom: 10px;
}
.checkbox {
  margin-right: 4px;
  width: 16px !important;
  height: 16px !important;
  margin-top: 9px;
}
.form-search-home {
  padding: 60px 0 24px;
}
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}
em,
input {
  font-style: normal;
}
body {
  font-size: 14px;
  font-weight: 400;
  background-color: #fff;
}
.accordian_txt ul {
  margin: 15px 0;
  padding-left: 30px;
}
.accordian_txt ul li {
  list-style: disc;
  font-size: 16px;
  color: #4d4d4d;
  font-weight: 400;
  line-height: 140%;
  margin-bottom: 5px;
}
.btn,
a,
button,
input[type='buttton'],
input[type='submit'] {
  transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  -ms-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  outline: 0 !important;
  font-style: normal;
  text-decoration: none;
}
input {
  outline: 0 !important;
  text-decoration: none;
  width: 100%;
}
figure,
img,
li,
ul {
  margin: 0;
  padding: 0;
}
li,
ul {
  list-style: none;
}
a,
a:focus,
a:hover {
  text-decoration: none;
}
input,
textarea {
  padding: 5px 10px;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -moz-appearance: none;
  appearance: none;
  -webkit-appearance: none;
  margin: 0;
}
input[type='number'] {
  -moz-appearance: textfield;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  opacity: 1;
  color: #fff;
  font-weight: 400;
  font-family: 'Inter', sans-serif;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  opacity: 1;
  color: #fff;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  color: #fff;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
}
/* input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  opacity: 1;
  color: #fff;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
} */
.noradius {
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
.noselect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.pointerNone {
  pointer-events: none;
  -webkit-pointer-events: none;
  -moz-pointer-events: none;
}
.relative {
  position: relative;
}
.bootstrap-select:not(.input-group-btn),
.bootstrap-select:not([class*='col-']):not([class*='form-control']):not(.input-group-btn),
.bootstrap-select[class*='col-'] {
  width: 100%;
}
.top_hdng {
  margin-bottom: 15px;
}
.top_hdng h6 {
  font-size: 12px;
  line-height: 140%;
  color: #fff;
  font-weight: 600;
}
.top_hdng h1 {
  font-size: 31px;
  line-height: 120%;
  color: #fdca40;
  font-weight: 700;
  margin-bottom: 5px;
}
.top_hdng h2 {
  font-size: 22px;
  line-height: 120%;
  color: #fff;
  font-weight: 400;
}
.jobspage {
  padding: 24px 0 40px;
}
.jobspage ul li {
  padding: 2px 4px;
  margin-right: 24px;
  position: relative;
  color: #bababa;
  font-size: 16px;
  line-height: normal;
  font-weight: 400;
}
.jobspage ul li:last-child {
  margin-right: 0;
}
.jobspage ul li:last-child::after {
  display: none;
}
.jobspage ul li::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: -18px;
  background-image: url(../public/images/Vector.png);
  width: 10px;
  height: 10px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
}
.jobspage ul li a {
  font-size: 16px;
  line-height: normal;
  font-weight: 400;
  color: #0070f5;
  display: block;
}
.job_title {
  font-size: 24px;
  margin-bottom: 24px;
}
.job_subtitle {
  font-size: 16px;
  line-height: 140%;
  font-weight: 400;
  color: #4d4d4d;
}
.top_hdng h1 span {
  display: inline-block;
  color: #fff;
}
.trending_job_sec {
  padding: 20px 0 0;
}
.trending_hdng {
  margin-bottom: 15px;
}
.faq_hdng h3,
.trending_hdng h6,
.trending_job_list li a {
  font-size: 18px;
  line-height: 120%;
  font-weight: 600;
  color: #fff;
}
.trending_hdng h6 span {
  display: inline-block;
  margin-left: 5px;
}
.trending_job_list li a {
  padding: 8px 16px;
  background-color: rgba(255, 255, 255, 0.36);
  border-radius: 24px;
  font-size: 17px;
  display: inline-block;
}
.expand_srch_sec {
  padding-top: 15px;
}
.expand_srch_hdng {
  margin-bottom: 15px;
}
.expand_srch_hdng h6,
.expand_srch_list li a,
.faq_hdng p,
.trending_job_list span {
  font-size: 16px;
  line-height: 140%;
  color: #2c2c2c;
  font-weight: 600;
}
.expand_srch_list ul,
.trending_job_list ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}
.expand_srch_list li a {
  background-color: #ebf4ff;
  display: flex;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #cfe5ff;
  align-items: center;
  column-gap: 8px;
  transition: all 0.3s ease-in-out;
}
.expand_srch_list li a span {
  display: block;
  line-height: 0;
}
.expand_srch_list li a:hover,
.expand_srch_list li.active a {
  background-color: #cfe5ff;
  border-color: #0070f5;
  color: #0070f5;
}
.faq_sec {
  padding: 80px 0 0;
}
.stay-updated {
  margin-top: 80px;
}
.faq_hdng {
  margin-bottom: 48px;
}
.faq_hdng h3 {
  font-size: 45px;
  margin-bottom: 8px;
  font-weight: 700;
  color: #0055ba;
}
.faq_hdng p {
  font-size: 26px;
  line-height: 120%;
  font-weight: 500;
}
.faq_area .accordion-item {
  border-radius: 0;
  border: 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  padding: 20px 0;
  background-color: transparent;
}
.faq_area .accordion-button {
  border-radius: 0 !important;
  background-color: transparent;
  box-shadow: none;
  padding: 0;
  font-size: 18px;
  line-height: 120%;
  font-weight: 600;
  color: #151515;
  border: 0;
}
.faq_area .accordion-body {
  padding: 8px 0 0;
}
.accordian_txt p {
  font-size: 16px;
  color: #4d4d4d;
  font-weight: 400;
  line-height: 140%;
}
.more_about_job_sec {
  background-color: #ebf4ff;
  padding: 80px 0;
}
.more_about_job_innr,
.more_about_job_top {
  border: 1px solid #d9d9d9;
  border-radius: 16px;
  background-color: #fff;
  padding: 40px;
  margin-bottom: 24px;
}
.more_about_job_innr h3,
.more_about_job_top h3 {
  font-size: 31px;
  line-height: 120%;
  color: #2c2c2c;
  font-weight: 500;
  margin-bottom: 24px;
}
.more_about_job_innr li a,
.more_about_job_top p {
  font-size: 18px;
  line-height: 120%;
  color: #747474;
  font-weight: 400;
}
.more_about_job_bottom {
  display: flex;
  flex-wrap: wrap;
  margin: 0-12px;
}
.more_about_job_col {
  width: 50%;
  padding: 0 12px;
}
.more_about_job_innr h3 {
  font-size: 26px;
}
.more_about_job_innr ul {
  column-count: 2;
}
.more_about_job_innr li {
  display: block;
}
.more_about_job_innr li a {
  display: block;
  line-height: 180%;
  transition: all 0.3s ease-in-out;
}
.more_about_job_innr li a:hover {
  color: #0070f5;
}
.trending_job_list span {
  line-height: 120%;
  color: #fff;
  display: none;
}
.salary-insights {
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
}
p.feature {
  color: #000;
  font-size: 15px;
  line-height: 1;
}
.feat {
  display: inline-block;
  padding: 5px 10px;
  background-color: #fcd422;
  border-radius: 8px;
}
p.feature span {
  margin-left: 7px;
}
.job_feature {
  gap: 16px;
  margin-top: 23px;
}
p.c_name {
  line-height: 0.4;
}
.jobs_post_date p {
  font-size: 12px;
  line-height: 140%;
  font-weight: 300;
  color: #999;
  text-align: left;
  margin-top: 10px;
}
.job_feature,
.jobspage ul {
  display: flex;
  align-items: center;
}

.sidebar-filters .slider-dropdown {
  .ant-slider-track {
    background: #0055ba;
    height: 3px;
  }
  .ant-slider-handle::after {
    background: #0055ba;
    box-shadow: none;
    box-shadow: 0 0 0 2px #0055ba;
  }
}
.testimonial-container {
  width: 100%;
  position: relative;
}
.testimonial-container a {
  border: 1px solid #fdca40;
}

.testimonial-container .carousel-control-prev {
  left: 48%;
  translate: -50%;
}
.testimonial-container .carousel-control-next {
  right: 48%;
  translate: 50%;
}
.accordion-header-faq button::after {
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230070F5'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>");
}

.accordion-button:not(.collapsed)::after {
  -webkit-filter: grayscale(0) !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-background-clip: text;
  -webkit-text-fill-color: #000000e0;
  transition: background-color 5000s ease-in-out 0s;
  box-shadow: inset 0 0 20px 20px #23232329;
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #0070f5;
  text-shadow: none;
}

.ant-tabs-top > .ant-tabs-nav::before,
.ant-tabs-bottom > .ant-tabs-nav::before,
.ant-tabs-top > div > .ant-tabs-nav::before,
.ant-tabs-bottom > div > .ant-tabs-nav::before {
  border-bottom: none;
}
.search-select .ant-select-selection-placeholder {
  color: #5c5c5c !important;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.radio-btn .ant-radio-inner {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  border-radius: 50%;
  width: 16px;
  height: 16px;

  border: 2px solid #999;
  transition: 0.2s all linear;
  margin-right: 5px;

  position: relative;
  top: 4px;
}
.review-tab .ant-tabs-ink-bar {
  background: none !important;
}

.review-tab .ant-tabs-tab {
  margin: 0 !important;
  padding: 0;
}
.review-tab .ant-tabs-nav-list {
  display: flex;
  gap: 8px;
}
.review-tab .ant-tabs-nav-operations {
  display: none !important;
}
.company-detail-tab .ant-tabs-nav-wrap {
  border-bottom: 1px solid #eee;
}
.datapoint_search svg {
  height: 20px;
  width: 20px;
  margin-right: 2px;
}
.datapoint_search input::placeholder {
  color: #999;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.5px;
}
.email-template .u-row,
.u-col {
  max-width: 284px !important;
  min-width: 284px !important;
}
.instance-dropdown .instance-item-option-grouped {
  padding-inline-start: 10px;
}
.instance-dropdown .instance-item-group {
  color: #b8b8b8;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-size: 12px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: -0.5px;
  text-transform: uppercase;
}
.ant-select-multiple .ant-select-selector {
  overflow: auto;
}

@media (max-width: 991px) {
  .more_about_job_innr ul {
    column-count: 1;
  }
  .expand_srch_sec {
    padding-bottom: 15px;
  }
}
@media (max-width: 767px) {
  .ant-btn.ant-btn-sm {
    height: 40px;
    width: 100%;
    text-align: left;
    margin-bottom: 15px;
  }
  .job-list-item-wrapper .row {
    flex-direction: row-reverse;
  }
  .job-list-item-wrapper .col {
    width: 30%;
  }
  .job-list-item-wrapper .col-sm-7 {
    width: 70%;
    text-align: left;
  }
  .more_about_job_col {
    width: 100%;
  }
}
@media (max-width: 430px) {
  .top_hdng h6 {
    font-size: 11px;
  }
  .faq_area .accordion-button,
  .top_hdng h2,
  .trending_hdng h6 {
    font-size: 16px;
  }
  .trending_job_list span {
    display: block;
    padding-top: 10px;
  }
  .expand_srch_list ul,
  .trending_job_list ul {
    gap: 8px;
  }
  .faq_hdng h3 {
    font-size: 40px;
  }
  .faq_hdng p {
    font-size: 23px;
  }
  .accordian_txt p {
    font-size: 13px;
  }
  .faq_sec,
  .more_about_job_sec {
    padding: 40px 0;
  }
  .more_about_job_top h3 {
    font-size: 28px;
  }
  .more_about_job_innr,
  .more_about_job_top {
    padding: 16px;
    border-radius: 8px;
  }
  .more_about_job_innr li a,
  .more_about_job_top p {
    font-size: 16px;
  }
  .more_about_job_innr h3 {
    font-size: 23px;
  }
  .ant-btn.ant-btn-sm {
    height: 40px;
    width: 100%;
    text-align: left;
  }
  .job_title_container {
    flex-wrap: wrap;
  }
  .job_subtitle,
  .job_title {
    width: 100%;
    margin-bottom: 10px;
  }
  .salary-insights {
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .stay-updated {
    margin-top: 40px;
  }
}
.check-label,
ul.drop-list li a {
  letter-spacing: -0.5px;
  width: 100%;
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
}
.check-label {
  padding: 0 4px;
  margin-bottom: 10px;
}
.bg-F9F9F9 {
  background: #f9f9f9;
}
.form-card-box {
  background: var(--background-color_4);
  border-radius: 4px;
  padding: 25px;
}
ul.drop-list {
  margin: 0;
  padding: 0;
}
ul.drop-list li {
  list-style: none;
  position: relative;
}
ul.drop-list li a {
  padding: 10px;
  color: var(--color_16);
  display: block;
  box-shadow: 0 4px 4px #c3c1c129;
}
ul.drop-list li a .tag-menu {
  position: absolute;
  right: 10px;
  top: 4px;
  padding: 1px;
  width: 16px;
  height: 16px;
  font-size: 10px;
  line-height: 1.4;
}
ul.drop-list li a.left-sp .box-tag.tag-menu {
  right: unset;
  left: 10px;
}
.sp-l-r {
  padding: 5px 10px;
}
.sp-l-r label {
  margin-bottom: 0;
}
.w-16 {
  width: 16px;
  height: 16px;
  border-radius: 100px;
}
.w-24,
.w-32,
.w-40,
.w-56 {
  width: 24px;
  height: 24px;
  border-radius: 100px;
}
.w-32,
.w-40,
.w-56 {
  width: 32px;
  height: 32px;
}
.w-40,
.w-56 {
  width: 40px;
  height: 40px;
}
.w-56 {
  width: 56px;
  height: 56px;
}
.avatars-img img {
  margin: 0 6px;
}
.btn-list {
  font-weight: var(--font_weight_600);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  padding: 2px 4px;
  border: 0;
  background: #fff0;
}
.btn-span {
  background: var(--color_16);
  border-radius: 2px;
  color: var(--color_4);
  padding: 1px;
}
.btn-C1C4D6 {
  color: var(--color_18);
}
.btn-C1C4D6 .btn-span {
  background: var(--background-color_9);
}
.btn-051B44 {
  color: var(--color_16);
}
.btn-051B44 .btn-span {
  background: var(--color_16);
}
.bg-EDEFF5 {
  background: var(--background-color_8);
}
.btn-0066FF {
  color: var(--color_17);
}
.btn-0066FF .btn-span {
  background: var(--color_17);
}
.bg-E9F1FF {
  background: var(--background-color_10);
}
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider,
.slider:before {
  position: absolute;
  bottom: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.slider {
  cursor: pointer;
  width: 34px;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(21, 21, 21, 0.32);
}
.slider:before {
  content: '';
  height: 20px;
  width: 20px;
  left: 1px;
  background-color: #fff;
}
.switch-sp {
  margin: 0 8px 0 0;
}
input:checked + .slider {
  background-color: #0055ba;
}
input:focus + .slider {
  box-shadow: 0 0 1px #0055ba;
}
input:checked + .slider:before {
  -webkit-transform: translateX(13px);
  -ms-transform: translateX(13px);
  transform: translateX(13px);
}
.slider.round {
  border-radius: 34px;
}
.slider.round:before {
  border-radius: 50%;
}
.checkbox-size {
  width: 18px;
  height: 18px;
  margin: 0 3px;
}
@media (max-width: 981px) {
  .card-box,
  .fields-form,
  .form-input {
    padding: 20px;
  }
}
.btn-bg-747474 {
  background: #747474;
  padding: 4%0;
}
.head-part {
  background: var(--background-color_4);
}
.logo-head {
  width: 200px;
}
.head-part ul.navbar-nav li a {
  font-weight: var(--font_weight_600);
  font-size: var(--font_16);
  line-height: var(--font_19_line-height);
  color: var(--color_8) !important;
  padding: 0 13px !important;
  font-family: var(--opensans-font);
}
.head-part .navbar-light .navbar-nav .nav-link.active {
  color: var(--background-color_1) !important;
}
.login,
.login:hover,
button.navbar-toggler {
  color: var(--color_1) !important;
}
.login,
.signup {
  border-radius: 2px !important;
  font-size: var(--font_18) !important;
  font-weight: var(--font_weight_700) !important;
  line-height: var(--font_18_line-height) !important;
  height: 46px !important;
  padding: 12px 24px !important;
}
.login {
  border: 2px solid var(--color_1) !important;
  background: rgba(0, 85, 186, 0.08) !important;
}
.signup {
  border: 2px solid var(--background-color_1) !important;
  color: var(--background-color_4) !important;
  margin-left: 10px !important;
  background: var(--background-color_1) !important;
}
.login:hover {
  background: var(--color_52) !important;
}
.border-primary-size-18:hover {
  background: var(--color_52);
}
.signup:hover,
p.banner-btn a:hover {
  background: var(--background-color_5) !important;
}
.signup:hover {
  border: solid 2px var(--color_7) !important;
  color: var(--color_4) !important;
}
.btn-bg-fff:hover {
  background: var(--background-color_3);
  color: var(--color_1) !important;
  border: solid 2px var(--color_3);
}
p.banner-btn a:hover {
  color: var(--color_4);
}
p.banner-btn a {
  transition: 0.5s;
}
.border-primary-size-22.border-fff:hover {
  background: #ffffff3d;
}
.download:hover {
  background: var(--color_52);
}
.head-part ul.navbar-nav {
  margin-right: 15px;
}
span.navbar-toggler-icon {
  background: var(--color_4) !important;
}
button.navbar-toggler {
  font-size: 30px !important;
  border: 0 !important;
  outline: 0 !important;
  padding: 0;
}
.del-trash:hover {
  background: var(--color_53);
}
.cancel:hover {
  background: var(--color_22) !important;
}
.edit-pencil:hover {
  background: var(--color_53);
}
.save:hover {
  background: var(--background-color_5) !important;
  border: solid 2px var(--color_7);
}
.navbar-toggler:focus {
  box-shadow: unset !important;
}
.navbar-toggler.collapsed .fa-bars {
  display: unset;
}
#dropdownMenuButton1.primary-size-16::after,
.navbar-toggler .fa-bars,
.navbar-toggler.collapsed .close-x {
  display: none;
}
.navbar-toggler .close-x {
  display: unset;
}
.j-end {
  justify-content: end;
}
.search-in {
  position: relative;
  width: 480px;
}
.search-in input {
  padding: 0 30px;
  border: 1px solid var(--color_15);
  border-radius: 4px;
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  letter-spacing: -0.5px;
  color: var(--color_10);
  height: 35px;
  font-family: var(--opensans-font);
}
.search-in .form-control:focus {
  border-color: var(--color_15);
  box-shadow: unset;
}
.search-in ::placeholder {
  color: var(--color_10);
}
.glass-ser {
  position: absolute;
  top: 10px;
  left: 8px;
  color: var(--color_10);
}
.head-icon a {
  font-size: 25px;
  color: var(--color_1);
  margin: 0;
}
.user-img img {
  margin: 0 10px;
}
#navbarSupportedContent p {
  text-align: center;
  cursor: pointer;
}
.dask-tab-mobile-d-flex {
  display: flex;
}
.fill-bell {
  transform: rotate(-12deg);
}
.head-icon.notifications {
  position: relative;
  padding: 0 15px;
}
.box-noti {
  position: absolute;
  width: 400px;
  right: 0;
  background: var(--background-color_4);
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
  border-radius: 4px;
  z-index: 9;
  padding: 15px;
  top: 62px;
}
.not-title {
  font-weight: var(--font_weight_600);
  color: var(--color_1);
  text-align: left;
}
.box-noti h5,
.box-noti p {
  text-align: center;
  color: var(--color_10);
}
.box-noti h5,
.not-title {
  font-family: var(--opensans-font);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
}
.box-noti p {
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.fa-bell-slash {
  color: var(--color_12);
  font-size: 40px;
  margin-top: 15px;
  margin-bottom: 15px;
}
.mark-as {
  font-weight: var(--font_weight_600) !important;
  font-size: var(--font_12) !important;
  line-height: var(--font_12_line-height) !important;
  color: var(--color_6) !important;
  text-align: right !important;
}
.text-left,
.text-left p {
  text-align: left !important;
}
.pr-0 {
  padding-right: 0;
}
.pl-0 {
  padding-left: 0;
}
p.f-16 {
  margin: 0 0 3px !important;
  font-size: var(--font_16) !important;
  line-height: var(--font_16_line-height) !important;
  color: var(--color_9);
}
.box-noti h5,
.build-download p b,
.note b,
.pro-tip b,
p.f-16 b {
  font-weight: var(--font_weight_700);
}
.f-12,
a.view-all {
  font-size: var(--font_12) !important;
  line-height: var(--font_12_line-height) !important;
}
.f-12,
p.f-16 {
  font-weight: var(--font_weight_400) !important;
}
.f-12 {
  margin: 1px 0 15px !important;
  color: var(--color_10);
}
a.view-all {
  font-weight: var(--font_weight_600);
  text-align: center;
  color: var(--color_6);
}
.round-bell {
  background: #fdca40;
  width: 10px;
  height: 10px;
  position: absolute;
  left: 12px;
  border-radius: 100px;
  top: 1px;
  border: solid 2px #fff;
}
@media (max-width: 1122px) {
  .head-part ul.navbar-nav li a {
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
  .login,
  .signup {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
  }
  .search-in {
    width: 348px;
  }
}
@media (max-width: 981px) {
  .head-part ul.navbar-nav li {
    margin: 8px 0;
  }
  .head-part ul.navbar-nav {
    margin-right: 0;
    margin-bottom: 50px;
    margin-top: 30px;
  }
  .navbar-toggler-icon {
    height: unset;
  }
}
@media (max-width: 767px) {
  .d-flex.mobile-single {
    display: unset !important;
  }
  .mobile-w-100 {
    width: 100%;
  }
  .signup {
    margin: 10px 0;
  }
  .search-in {
    width: 100%;
    margin-top: 13px;
  }
  .dask-tab-mobile-d-flex {
    justify-content: center;
  }
  .head-icon.notifications {
    position: unset;
  }
  .homepage_banner_sec .tab-m-0 {
    font-size: 29px;
    line-height: 35px;
  }
  #who-hiring-section .talent,
  #who-hiring-section .talent h2 {
    text-align: center;
  }
  #who-hiring-section .talent .tab-m-0 {
    margin-top: 1em !important;
  }
  #job-platform .bannerText {
    text-align: center;
    font-size: 28px;
    line-height: 35px;
    margin-bottom: 0;
  }
  #highest-offers-section .banner-text,
  #need-help-section h3,
  #need-help-section h4,
  #need-help-section h5,
  #need-help-section h6,
  #top-job-opening-section .title-heading,
  #top-job-opening-section h5 {
    text-align: center;
  }
  #find-by-location-section img,
  #looking-hire-section img {
    height: auto;
  }
  #find-by-location-section p {
    font-size: 12px;
  }
  #find-by-location-section p.font-12 {
    font-size: 10px;
  }
  #need-help-section h3 {
    font-size: 30px;
    line-height: 40px;
  }
  #need-help-section h4 {
    font-size: 21px;
    line-height: 30px;
  }
  #need-help-section h5 {
    font-size: 26px;
    line-height: 35px;
  }
  #need-help-section h6 {
    font-size: 18px;
  }
  #looking-hire-section h3 {
    text-align: center;
    font-size: 30px;
  }
  #looking-hire-section h6 {
    text-align: center;
    font-size: 20px;
  }
  #looking-hire-section p {
    text-align: center;
    font-size: 14px;
  }
  #top-blog-section .title-heading.text-center {
    font-size: 26px;
  }
  #top-blog-section .mobile-f-23 {
    font-size: 18px !important;
    line-height: 25px;
  }
  #top-blog-section #pills-tab li button {
    font-size: 12px;
    padding: 6px;
    margin: 10px 0 0 5px;
  }
}
.footer-part {
  padding: 100px 0;
}
.footer-part .first-link {
  font-weight: var(--font_weight_700);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  color: var(--color_1);
  margin-bottom: 25px;
}
.ant-picker-dropdown {
  z-index: 9999999;
}
.ant-select-dropdown {
  z-index: 9999999;
}
.ft-list li a,
.toast-body p,
.toast-head h5 {
  font-size: var(--font_16);
  font-family: var(--opensans-font);
}
.ft-list li a {
  font-weight: var(--font_weight_400);
  line-height: var(--font_16_line-height);
  color: var(--color_8);
  transition: 0.6s;
}
.ft-list li a:hover {
  color: var(--color_1);
}
.ft-list li {
  list-style: none;
  margin-bottom: 10px;
}
.ft-list {
  padding: 0 3px;
}
.last-footer .copy-text {
  margin: 0;
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  font-feature-settings:
    salt on,
    liga off;
  color: #d9dbe1;
}
.social-icons {
  padding: 0;
  margin: 0;
  text-align: right;
}
.social-icons li {
  list-style: none;
  display: inline-block;
}
.social-icons li a {
  color: #fff;
  padding: 4px 7px;
  width: 24px;
  height: 24px;
  display: block;
  background: #424242;
  border-radius: 100px;
  font-size: 12px;
  margin: 0 0 0 15px;
}
.last-footer {
  background: var(--color_8);
  padding: 10px 0;
}
.logo-ft {
  margin-bottom: 30px;
  width: 200px;
}
.signup-cards {
  padding: 24px 16px;
  font-weight: var(--font_weight_700);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  border-radius: 8px;
  border: 0;
  outline: 0;
}
.white-btn {
  background: var(--background-color_4);
  color: var(--color_1);
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
}
.blue-btn {
  background: var(--background-color_1);
  color: var(--color_4);
}
.signup-cards i {
  margin-right: 7px;
}
.width-1400 {
  max-width: 1400px !important;
}
.text-right {
  text-align: right;
}
.toast-body,
.toast-head {
  background: var(--background-color_4);
}
.toast-head {
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
  border-radius: 4px 4px 0 0;
  padding: 3px 10px;
  border-bottom: solid 1px #eee;
}
.toast-body p,
.toast-head h5 {
  margin: 0;
  line-height: var(--font_16);
  color: var(--color_19);
  font-weight: var(--font_weight_700);
}
.toast-body {
  border-radius: 0 0 4px 4px;
}
.toast-body p {
  font-weight: var(--font_weight_400);
}
.link-12 {
  font-weight: var(--font_weight_600);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_6);
  font-family: var(--opensans-font);
}
.toast-footer {
  background: var(--background-color_4);
  border-radius: 4px;
  margin-top: 0;
  font-family: var(--opensans-font);
}
.toast-footer h5,
.xmark-icon {
  font-size: var(--font_16);
  line-height: var(--font_16);
  color: var(--color_19);
}
.toast-footer h5 {
  margin: 0;
  font-weight: var(--font_weight_600);
  font-family: var(--opensans-font);
}
.xmark-icon {
  margin: 0 0 0 10px;
}
.toast-box p {
  margin: 0;
}
.color-0C5A14 .toast-footer h5,
.color-0C5A14 .toast-head h5,
.color-0C5A14 .xmark-icon {
  color: var(--color_20);
}
.color-0C5A14 .toast-body,
.color-0C5A14 .toast-footer,
.color-0C5A14 .toast-head {
  background: var(--background-color_11);
}
.toast-box.color-0C5A14 h5 .fa-circle-check,
.toast-box.color-D04E4F h5 .fa-circle-check {
  color: #3d9f79;
}
.left-sp-alt {
  padding-left: 24px;
}
.color-D04E4F .toast-footer h5,
.color-D04E4F .toast-head h5 {
  color: var(--color_21);
}
.color-D04E4F .toast-body,
.color-D04E4F .toast-footer,
.color-D04E4F .toast-head {
  background: var(--color_22);
}
.color-D04E4F .toast-head {
  border-bottom: solid 1px #fd7373;
}
.color-D04E4F .fa-circle-xmark {
  color: #fd7373;
}
.color-0055BA .toast-footer h5,
.color-0055BA .toast-head h5 {
  color: var(--color_1);
}
.color-0055BA .toast-body,
.color-0055BA .toast-footer,
.color-0055BA .toast-head {
  background: var(--background-color_15);
}
.toast-footer h5 i,
ul.skills.pop-skils li i.fa-briefcase {
  margin-right: 5px;
}
.color-0055BA .toast-head {
  border-bottom: solid 1px var(--background-color_3);
}
.color-0055BA h5 .fa-circle-info {
  color: #298bff;
}
.color-D57B11 .toast-footer h5,
.color-D57B11 .toast-head h5,
.up-down-item .accordion-collapse .f-D57B11 h5 {
  color: var(--color_25);
}
.color-D57B11 .toast-body,
.color-D57B11 .toast-footer,
.color-D57B11 .toast-head {
  background: var(--color_24);
}
.color-D57B11 .toast-head {
  border-bottom: solid 1px #eee;
}
.color-D57B11 .fa-triangle-exclamation {
  color: #fdca40;
}
.candidate-box {
  background: #fff;
}
.candidate-box .username {
  font-size: 16px;
  color: #0055ba;
}
.candidate-box .email {
  font-size: 12px;
  opacity: 0.5;
  display: block;
  margin-top: -3px;
}
.candidate-box .role {
  font-size: 10px;
  line-height: 10px;
  display: inline-block;
  margin-left: 5px;
  opacity: 0.7;
  background: #c1def3;
  color: #0055ba;
  padding: 2px 5px;
  border-radius: 4px;
  vertical-align: middle;
  margin-top: -2px;
}
.name-user {
  margin: 0;
  font-weight: var(--font_weight_600);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_1);
}
.interview {
  background: #e2f3dc;
  color: #2a6217;
  border-radius: 20px;
  font-size: 12px;
  padding: 4px 0;
  text-align: center;
  margin-top: 10px;
}
.interview:first-letter {
  text-transform: uppercase;
}
.can-list {
  list-style: none;
  padding: 0 8px;
  margin: 0;
}
.can-list li a {
  display: block;
  padding: 3px 8px;
  color: #606060;
  font-size: 15px;
}
.can-list li a:hover {
  color: #000;
}
.dash-profile-img {
  max-width: 120px;
  position: relative;
  z-index: 8;
}
.dash-profile-img img {
  border: 1px solid var(--color_3) !important;
}
.dash-profile-img a,
.diamond {
  position: absolute;
  text-align: center;
}
.dash-profile-img a {
  background: var(--color_3);
  width: 24px;
  height: 24px;
  padding: 3px 2px;
  border-radius: 100px;
  bottom: 1px;
  color: var(--color_1);
  font-size: 14px;
  right: 11px;
  z-index: 99;
}
.diamond {
  max-width: 120px;
  height: 29px;
  background: var(--color_6);
  border: 0;
  width: 100%;
  top: 100%;
  transition: 0.6s;
  z-index: 9;
}
.diamond i {
  position: unset !important;
  background: #fff0;
  color: var(--background-color_4);
  font-size: 16px;
}
.dash-profile-img:hover .diamond {
  top: 80%;
}
.radius {
  border-radius: 200px;
  overflow: hidden;
}
.up-down-item .accordion-button {
  font-size: var(--font_16);
  line-height: var(--font_16);
  font-weight: var(--font_weight_700);
  color: var(--color_20);
  background: var(--background-color_11);
  border-radius: 4px;
  font-family: var(--opensans-font);
}
.up-down-item button img {
  margin-right: 3%;
}
.accordion-button:focus {
  border-color: unset;
  box-shadow: unset;
}
.up-down-item .accordion-collapse {
  margin: 4px 0 0;
  background-color: var(--background-color_4);
  border-radius: 4px;
}
.up-down-item .accordion-collapse .accordion-body {
  background: var(--background-color_4);
  box-shadow: inset 0-1px 0#edeff5;
  padding: 10px 20px;
  border-bottom: solid 1px #edeff5;
  border-radius: 4px;
}
#pills-tab li button.nav-link.active {
  color: #fff !important;
}
.up-down-item .accordion-body p {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_11);
  font-family: var(--opensans-font);
}
.up-down-item .accordion-body h5 {
  font-size: var(--font_16);
  line-height: var(--font_16);
  font-weight: var(--font_weight_700);
  color: var(--color_20);
  margin-bottom: 7px;
  font-family: var(--opensans-font);
}
.up-down-item .accordion-item {
  background-color: #fff0;
  border: unset;
}
.up-down-item .accordion-collapse .f-D04E4F h5 {
  color: var(--color_21);
}
.w-270 {
  max-width: 270px;
}
.accordion-button:not(.collapsed)::after {
  -webkit-filter: grayscale(100%);
}
.up-down-item .accordion-button.c-D57B11 {
  color: var(--color_25);
  background: var(--color_24);
}
.up-down-item .accordion-button.c-D04E4F {
  color: var(--color_21);
  background: #ffe5e5;
}
.btn-app {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  letter-spacing: -0.02em;
  text-transform: uppercase;
  border: 1px solid #000;
  padding: 4px 12px;
  border-radius: 16px;
  margin-bottom: 8px;
}
.bg-D57B11-app {
  color: var(--color_25);
  border: 1px solid var(--color_26);
  background: #fff7e1;
}
.bg-0055BA-app {
  color: var(--color_1);
  border: 1px solid var(--color_1);
  background: #ebf4ff;
}
.bg-3D9F79-app {
  color: var(--color_14);
  border: 1px solid var(--color_14);
  background: #e9f7f1;
}
.back-fill-3D9F79 {
  background: var(--color_14);
  color: var(--color_4);
  border: 1px solid var(--color_14);
}
.bg-D04E4F-app {
  color: var(--color_21);
  border: 1px solid var(--color_21);
  background: #fee1e1;
}
.bg-bababa-app {
  background: var(--color_12);
  border: 1px solid var(--color_15);
  color: var(--color_15);
}
.ready-to-in .accordion-body p,
.sel-opt {
  font-weight: var(--font_weight_400);
  letter-spacing: -0.5px;
}
.ready-to-in button.accordion-button,
.sel-opt {
  border-radius: 4px;
  padding: 10px 16px;
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.sel-opt {
  font-family: var(--opensans-font);
  color: var(--color_10ssss);
  height: 56px;
  border: 1px solid #bababa;
  outline: 0;
}
.ready-to-in button.accordion-button {
  border: 1px solid var(--color_15);
  font-weight: var(--font_weight_400);
  letter-spacing: -0.5px;
  color: var(--color_8);
  background: var(--background-color_4);
}
.ready-to-in .accordion-item {
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.ready-to-in .accordion-body {
  background: var(--background-color_4);
  border-radius: 4px;
  margin-top: 5px;
}
.ready-to-in .accordion-body p {
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  color: var(--color_9);
}
.ready-to-in .accordion-body p b {
  font-weight: var(--font_weight_600);
}
.ready-to-in .accordion-body p.f-0055BA {
  color: var(--color_1);
  position: relative;
}
.ready-to-in .accordion-body p.f-0055BA .fa-check {
  position: absolute;
  right: 0;
  bottom: 0;
}
.profile-checklist.back-white h6,
.ready-to-in .accordion-button.collapsed {
  color: var(--color_11);
}
.modal-uploade .modal-header,
.tab-part #pills-tab {
  justify-content: center;
}
.max-size,
.modal-uploade h5,
.upload-text {
  font-weight: var(--font_weight_700);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_1);
}
.max-size,
.upload-text {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  text-align: center;
  color: var(--color_15);
  margin: 0;
}
.max-size {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
}
.pro-tip {
  font-size: var(--font_16);
  line-height: var(--font_16);
  text-align: center;
  color: var(--color_9);
}
.note,
.pro-tip,
.right-text-edit p strong {
  font-weight: var(--font_weight_600);
}
.note {
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  text-align: center;
  color: var(--color_21);
}
.cancel-btn {
  background: var(--c-fff);
  align-items: center;
  padding: 12px 24px;
  border: 2px solid var(--color_13);
  border-radius: 8px;
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_13);
  width: 30%;
}
.cancel-btn:hover {
  background: var(--color_22) !important;
  color: var(--color_13) !important;
}
.banner-text h1,
.cancel-btn,
.update-btn {
  font-weight: var(--font_weight_700);
}
.update-btn {
  background: var(--color_1);
  border-radius: 8px;
  align-items: center;
  padding: 12px 24px;
  width: 65%;
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  border: 2px solid var(--color_1);
  color: var(--background-color_4);
}
.upload-file {
  border: 1px dashed var(--color_10);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  text-align: center;
}
.upload-file input {
  margin: 0 auto 10px;
  position: relative;
  z-index: 9;
  opacity: 0;
  width: 100%;
  height: 39px;
}
.file-up {
  position: relative;
  width: 45px;
  margin: 6px auto;
}
.file-up .cemra {
  position: absolute;
  right: 0;
}
.modal-uploade .modal-header {
  border-bottom: 0 solid #dee2e6;
}
.modal-uploade .modal-footer {
  border-top: 0 solid #dee2e6;
}
.modal-uploade .modal-body {
  padding-top: 0;
  padding-bottom: 0;
}
.modal-uploade .modal-content {
  background: var(--background-color_4);
  border-radius: 16px;
}
.profile-checklist {
  background: var(--color_6);
  border-radius: 16px;
  padding: 15px;
  width: 312px;
}
.profile-checklist h5,
.signup-cards.f-22 {
  font-weight: var(--font_weight_500);
  color: var(--background-color_4);
}
.profile-checklist h6,
.unlocked-text,
ul.Verify li {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
}
.profile-checklist h6 {
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: #eee;
}
.unlocked-text,
ul.Verify li {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
ul.Verify li {
  list-style: none;
  margin: 15px 0;
}
ul.Verify {
  padding: 0;
  margin: 10px 0 30px;
}
.signup-cards.f-22 i,
ul.Verify li i {
  margin-right: 8px;
}
.signup-cards.f-22 {
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  color: var(--color_1);
  width: 100%;
  padding: 14px 10px;
}
li.dis {
  opacity: 0.6;
}
.signup-cards.blue-btn.f-22,
ul.Verify li {
  color: var(--color_4);
}
.profile-checklist.back-white {
  background: var(--background-color_4);
}
.banner-text h1 span,
.profile-checklist.back-white h5 {
  color: var(--color_1);
}
.unlocked-text {
  color: var(--color_11);
}
.Unsaved-text {
  font-weight: var(--font_weight_500) !important;
  font-size: var(--font_22) !important;
  line-height: var(--font_22_line-height) !important;
  color: var(--color_9) !important;
}
.discard {
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_10);
}
.banner-1 {
  background: #dbfaf3;
}
.banner-2 {
  background: #d9f6fc;
}
.banner-3 {
  background: #fff9eb;
}
.banner-4 {
  background: #ffeeeb;
}
.banner-part .carousel-item {
  padding: 120px 0;
}
.banner-text h1 {
  color: var(--color_27);
}
.banner-text p,
.profile-checklist h5,
p.banner-btn a {
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
}
.banner-text p {
  font-weight: var(--font_weight_500);
  color: var(--color_9);
}
p.banner-btn a {
  padding: 16px 32px;
  font-weight: var(--font_weight_700);
}
p.banner-btn {
  margin-top: 45px;
}
.banner-text {
  padding: 180px 0 0;
}
.carousel-control-next,
.carousel-control-prev {
  position: absolute;
  right: 10%;
  left: unset;
  width: 50px;
  height: 50px;
  opacity: 1;
  border: solid 2px var(--color_28);
  border-radius: 100px;
  bottom: 50px;
  top: unset;
  padding: 12px;
}
.carousel-control-next {
  right: 5%;
}
.carousel-control-next-icon {
  background-image: url(/images/right-arrow.png) !important;
}
.carousel-control-prev-icon {
  background-image: url(/images/left-arrow.png) !important;
}
.dots-banner {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 16%;
}
.pricing-part {
  background: var(--background-color_4);
  padding: 70px 0;
}
.banner-text h1,
.plan-box h3,
.pricing-part h2 {
  font-size: var(--font_45);
  line-height: var(--font_45_line-height);
}
.plan-box h4,
.pricing-part h2 {
  font-weight: var(--font_weight_700);
}
.choose-plan,
.pricing-part h2 {
  text-align: center;
  color: var(--color_27);
}
.pricing-part .flexible_plan,
ul.check-close li i {
  color: var(--color_1);
}
.choose-plan {
  font-weight: var(--font_weight_500);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
}
.pay-d-flex p {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  color: var(--color_19);
  margin: 0;
}
.pay-d-flex {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}
.pay-d-flex .save-25 {
  position: absolute;
  margin: -7px 0 0;
  width: 200px;
}
.pay-d-flex .switch.switch-sp {
  margin: -5px 5px;
}
.blue-plan .started-part,
.plan-box {
  background: var(--background-color_4);
}
.plan-box {
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
  border-radius: 4px;
  padding: 40px 24px;
  transition: 0.6s;
  margin-bottom: 35px;
}
.plan-box h4 {
  color: var(--color_9);
}
.pay-d-flex p,
.plan-box p,
ul.check-close li {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.plan-box h3 sup,
.plan-box p {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  color: var(--color_11);
}
.plan-box h3 {
  color: #191d23;
}
.plan-box h3 small,
.plan-box h4 {
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
}
.plan-box h3 sup {
  font-weight: var(--font_weight_300);
}
.plan-box h3,
.started-part,
.tab-btn-speak a p {
  font-weight: var(--font_weight_700);
}
.plan-box h3 sup,
.started-part {
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
}
.started-part {
  background: var(--color_1);
  border-radius: 2px;
  color: var(--background-color_4);
  align-items: center;
  padding: 12px 24px;
  border: 0;
  width: 100%;
}
ul.check-close li {
  font-style: normal;
  font-weight: var(--font_weight_500);
  color: var(--color_29);
  list-style: none;
  margin-top: 30px;
  position: relative;
}
ul.check-close li i {
  width: 32px;
  height: 32px;
  background: var(--background-color_3);
  border-radius: 100px;
  text-align: center;
  padding: 8px 0;
  position: absolute;
  left: -40px;
}
ul.check-close li i.fa-xmark {
  background: var(--color_30);
  color: var(--color_27);
}
.blue-plan {
  background: var(--color_1);
  box-shadow:
    0 2px 6px rgba(21, 21, 21, 0.18),
    0 4px 10px -4px rgba(21, 21, 21, 0.25),
    0 14px 28px -2px rgba(21, 21, 21, 0.3);
  border-radius: 12px;
}
.blue-box ul.full-time.f-12-list li,
.blue-plan h3,
.blue-plan h4,
.blue-plan p,
.blue-plan ul.check-close li {
  color: var(--color_4);
}
.blue-plan h3 sup {
  color: var(--color_31);
}
.blue-plan .started-part {
  color: var(--color_1);
}
.started-part:hover {
  background: var(--color_7);
}
.blue-plan .started-part:hover {
  background: #cfe5ff;
}
.build-download h5,
.left-text-fieild h3,
.tab-part #pills-tab button {
  font-weight: var(--font_weight_500);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
}
.tab-part #pills-tab button {
  background: var(--color_3);
  border-radius: 4px;
  padding: 12px 24px;
  margin: 0 8px;
  color: var(--color_1);
}
.tab-part #pills-tab button.active,
p.banner-btn a {
  background: var(--color_1);
  color: var(--color_4);
}
.w-48 {
  width: 48px;
  height: 48px;
}
.tab-btn-speak a p {
  background: var(--color_3);
  border-radius: 24px;
  font-family: var(--opensans-font);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_1);
  padding: 8px 16px;
}
.tab-btn-speak {
  margin: 20px 0;
}
.tab-interview,
.tab-name-user {
  font-family: var(--opensans-font);
  line-height: var(--font_16_line-height);
}
.tab-name-user {
  font-weight: var(--font_weight_600);
  font-size: var(--font_18);
  color: var(--color_9);
  margin: 0 0 6px;
}
.tab-interview {
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  color: #737474;
  margin: 0;
}
.build-download {
  background: var(--background-color_4);
  padding: 20px;
  position: relative;
}
.build-download h2 {
  font-size: var(--h4_size);
  line-height: var(--h4_size_line-height);
}
.build-download h5,
.left-text-fieild h3 {
  color: var(--color_8);
}
.build-download h2,
.build-download h5.blue-title {
  font-weight: var(--font_weight_700);
  color: var(--color_1);
}
.build-download p,
.start-now a {
  line-height: var(--font_16_line-height);
}
.build-download p {
  font-weight: var(--font_weight_400);
  color: #000;
}
.start-now a {
  font-weight: var(--font_weight_700);
  font-size: var(--font_16);
  color: var(--color_6) !important;
  text-decoration: underline 2px !important;
}
.slider-cv {
  padding: 10px;
}
.edit-pi i,
.up-down-icon {
  color: var(--color_1);
  cursor: pointer;
}
.up-down-icon {
  position: absolute;
  left: 50%;
  background: var(--background-color_4);
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
  border-radius: 16px;
  width: 32px;
  height: 32px;
  padding: 8px 10px;
  bottom: -15px;
}
.work-experience-fieild {
  background: var(--background-color_4);
  border-radius: 2px;
  padding: 20px;
}
.build-download p,
.left-text-fieild p,
.right-text-edit h6,
.right-text-edit p {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
}
.left-text-fieild p {
  font-weight: var(--font_weight_600);
  line-height: var(--font_16_line-height);
  color: var(--color_11);
}
.right-text-edit {
  background: #f9f9f9;
  border: 1px solid var(--color_12);
  border-radius: 16px;
  padding: 16px;
}
.right-text-edit h6 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
.right-text-edit p {
  font-weight: var(--font_weight_400);
}
.right-text-edit p strong {
  color: var(--color_1);
}
.date-time {
  color: var(--color_10);
}
.data-text {
  color: var(--color_19);
}
.edit-pi i {
  font-size: var(--font_26);
}
.add {
  font-family: var(--opensans-font);
  font-style: normal;
  font-weight: var(--font_weight_600);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  letter-spacing: -0.5px;
  color: var(--color_6);
  margin-top: 20px;
}
.add i {
  margin-right: 10px;
}
.edit-pi {
  margin-bottom: -24px;
}
.form-experience-fieild .fild-des,
.form-experience-fieild label {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_9);
  width: 100%;
}
.form-experience-fieild .fild-des {
  border: 1px solid var(--color_15);
  border-radius: 4px;
  padding: 2px 16px;
  font-family: var(--opensans-font);
  font-size: var(--font_16);
  line-height: var(--font_14_line-height);
  letter-spacing: -0.5px;
  color: var(--color_10);
  height: 35px;
  background: var(--color_4);
  margin: 5px 0 20px;
  outline: 0;
}
.form-experience-fieild ::placeholder {
  color: var(--color_10);
}
.d-flex-form input {
  margin: 0 5px 0 0;
  position: relative;
  top: 2px;
}
.form-experience-fieild textarea.fild-des {
  height: 145px;
  margin: 0;
}
.words {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  text-align: right;
  letter-spacing: -0.5px;
  color: #474d66;
  font-family: var(--opensans-font);
  margin: 0;
}
.save {
  border-radius: 8px;
  align-items: center;
  border: 2px solid var(--color_1);
  padding: 12px 24px;
  font-weight: var(--font_weight_700);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.cancel {
  border-radius: 8px;
  align-items: center;
  padding: 12px 24px;
  color: var(--color_13) !important;
  border: 2px solid var(--color_13);
  margin: 0 10px 0 0;
  background: var(--c-fff);
  font-weight: var(--font_weight_700);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.save {
  background: var(--color_1);
  color: var(--color_4);
}
.form-experience-fieild .glass-ser {
  color: var(--color_8);
}
@media (max-width: 1100px) {
  .banner-text {
    padding: 109px 0 0;
  }
  .banner-text h1 {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  .banner-text p,
  p.banner-btn a {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .tab-part #pills-tab button {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
    padding: 10px 16px;
  }
  .tab-btn-speak a {
    font-size: var(--font_11);
    line-height: var(--font_11_line-height);
  }
  .tab-name-user {
    font-size: var(--font_16);
  }
  .tab-interview {
    font-family: var(--opensans-font);
    font-size: var(--font_13);
    line-height: var(--font_13_line-height);
  }
}
@media (max-width: 991px) {
  .close-x {
    font-size: 20px;
  }
  .carousel-control-prev {
    right: 100px;
  }
  .carousel-control-next {
    right: 26px;
  }
  .tab-part #pills-tab button {
    margin-bottom: 15px;
  }
  .build-download h2 {
    font-size: var(--font_22);
    line-height: var(--font_22_line-height);
  }
  .build-download h5 {
    font-size: var(--font_18);
    line-height: var(--font_18_line-height);
  }
  .build-download p {
    font-family: var(--opensans-font);
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
    margin: 0 0 6px;
  }
  .filter-sec .ant-btn {
    padding: 7px 12px;
    height: auto;
    border-radius: 4px;
    color: #2c2c2c !important;
    border: 1px solid #d9d9d9 !important;
    font-size: 13px;
    line-height: 27px;
  }
  .filter-sec h4 {
    font-size: 19px;
    font-weight: 500;
    line-height: 23px;
    margin: 0;
  }
  .filter-sec > div {
    align-items: center;
  }
  .filter-sec p {
    font-size: 13px;
  }
}
@media (max-width: 767px) {
  .header-navigation .navbar {
    padding: 16px 0 !important;
  }
  .form-search-home {
    background-position: center center !important;
  }
  .footer-part {
    background-size: auto;
    padding: 0;
  }
  .logo-width {
    padding-bottom: 0;
  }
  .mobile-reverse {
    flex-direction: column-reverse;
  }
  .social-icons {
    margin: 0 0 6px;
  }
  .last-footer .copy-text,
  .social-icons {
    text-align: center;
  }
  .banner-img {
    margin-top: 30px;
  }
  .banner-text {
    padding: 0;
  }
  .banner-part .carousel-item {
    padding: 50px 0 80px;
  }
  .carousel-control-prev {
    right: 65px;
  }
  .carousel-control-next,
  .carousel-control-prev {
    bottom: 15px;
    transform: scale(0.6);
  }
  .pay-d-flex .save-25 {
    display: none;
  }
  .pricing-part h2 {
    font-size: var(--h4_size);
    line-height: var(--h4_size_line-height);
  }
  .choose-plan {
    font-size: var(--font_18);
    line-height: var(--font_18_line-height);
  }
  .tab-part #pills-tab button {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .mobile-column-reverse {
    flex-direction: column-reverse;
  }
  .form-search-home input,
  .form-search-home select {
    font-size: 13px;
    font-weight: 400;
    line-height: 16px;
    padding: 11px 8px;
    color: #5c5c5c !important;
  }
  .form-search-home .ant-select-selection-placeholder {
    font-size: 13px;
    color: #5c5c5c !important;
  }
  .form-search-home button.ant-btn {
    height: 43px;
    border-radius: 4px;
  }
}
@media (max-width: 370px) {
  .banner-text h1 {
    font-size: var(--h3_size);
    line-height: var(--h3_size_line-height);
  }
  .pricing-part h2 {
    font-size: var(--h5_size);
    line-height: var(--h5_size_line-height);
  }
}
.mr-1 {
  margin-right: 1rem;
}
.banner-part-home h1 {
  font-weight: var(--font_weight_700);
  font-size: var(--h1_size);
  line-height: var(--h1_54_size_line-height);
  color: var(--color_27);
}
.font-banner-26 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
}
.f-12-747474 a,
.form-get a,
.span-color {
  color: var(--color_1);
}
.span-color-2 {
  color: var(--color_28);
}
.sp-80 {
  padding: 80px 0 !important;
}
.form-search-home {
  background-image: url(../public/images/back-form.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.form-card {
  background: var(--color_4);
  border-radius: 24px;
  padding: 16px;
}
.form-card #basic-addon1,
.form-card input {
  background: var(--color_4);
  border-color: #0055ba47;
}
.form-card #basic-addon1 {
  border-radius: 100px 0 0 100px;
  border-right: 0 var(--color_4);
  color: var(--color_1);
  font-size: 22px;
}
.form-card input {
  border-radius: 0 100px 100px 0 !important;
  border-left: 0 !important;
  padding: 13px 4px;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: #5c5c5c;
}
.form-card .form-control:focus {
  border-color: #0055ba47;
  box-shadow: unset;
}
.talent {
  background: var(--color_3);
}
.title-heading {
  font-size: var(--font_45);
  line-height: var(--font_45_line-height);
  color: var(--color_8);
}
.talent h2 {
  text-align: center;
}
.we-to h5 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_31);
  line-height: var(--font_31_line-height);
  color: var(--color_19);
}
.title-heading,
.we-to h2 {
  font-weight: var(--font_weight_700);
}
.we-to h2 {
  font-size: 66px;
  line-height: 79.2px;
  color: var(--color_1);
  text-align: left;
}
.we-to {
  margin-top: 23%;
}
.call-us {
  background: #0055ba;
}
.call-us h3,
.call-us h5,
.updated-stay h2 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_45);
  line-height: var(--font_45_line-height);
  color: var(--color_4);
}
.call-us h5 {
  font-weight: var(--font_weight_400);
  font-size: var(--font_31);
  line-height: var(--font_31_line-height);
}

.job-data h4,
.looking-hire h6,
.make-it h6,
.tips h6,
.job-data h4 {
  font-weight: var(--font_weight_700);
  color: var(--color_8);
}

.bg-ebf1f9 {
  background: var(--color_36);
}
.find-bylocation,
.top-jobs {
  background-size: cover;
  background-repeat: no-repeat;
}

.find-bylocation {
  background-image: url(../public/images/location-back.jpg);
  padding: 80px 0;
}
.find-bylocation h3.title-heading {
  color: var(--color_4);
  text-align: center;
}
.find-bylocation h5 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  text-align: center;
  color: var(--color_35);
}
.box-flg-w-20 {
  display: flex;
  flex-wrap: wrap;
}
.flg-part {
  width: 20%;
  padding-right: 8px;
  margin-bottom: 25px;
}
.flg-part p,
.flg-part p.font-12 {
  font-weight: var(--font_weight_600);
}
.flg-part p {
  margin: 0;
  font-family: var(--opensans-font);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_4);
}
.flg-part p.font-12 {
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_28);
  margin-top: 5px;
}
.need-help {
  padding: 80px 0;
}
.big-short big,
.looking-hire h3,
.need-help h3,
.need-help h4 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_45);
  line-height: var(--font_45_line-height);
  color: var(--color_1);
}
.need-help h4 {
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
}
.make-it,
.tips {
  border-radius: 4px;
  padding: 32px 16px 16px;
}
.tips {
  background: var(--color_1);
}
.make-it h5,
.tips h5 {
  font-size: var(--font_37);
  line-height: var(--font_37_line-height);
}
.tips h5 {
  font-weight: var(--font_weight_700);
  color: var(--color_4);
}
.looking-hire h6,
.make-it h6,
.tips h6 {
  color: var(--color_35);
}
.make-it {
  background: var(--color_34);
}
.make-it h5 {
  color: var(--color_32);
}
.looking-hire h6,
.make-it h6 {
  font-weight: var(--font_weight_500);
  color: var(--color_32);
}
.looking-hire {
  background: var(--color_33);
  padding: 80px 0;
}
.looking-hire h6,
.make-it h5 {
  font-weight: var(--font_weight_700);
}
.looking-hire h6 {
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
}
.stay-updated {
  background-image: url(../public/images/blue-back.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.form-white-22-font {
  font-weight: var(--font_weight_500);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  color: var(--color_3);
  padding: 15px 20px;
  width: 100%;
  background: #fff0 !important;
  border: solid 2px var(--color_4);
  border-radius: 5px;
}
.form-white-22-font::placeholder {
  color: var(--color_3);
}
.updated-stay .font-26 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_3);
}
.form-get label {
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_12_line-height);
  color: var(--color_9);
  width: 100%;
}
form.form-get {
  max-width: 462px;
  margin: 0 auto;
}
.form-get input,
.form-get textarea {
  border: 1px solid var(--color_15);
}
.b-des-btn,
.form-pages h2,
.form-pages h5 {
  font-weight: var(--font_weight_700);
  color: var(--color_1);
}
.b-des-btn {
  border: 2px solid var(--color_1);
  border-radius: 8px;
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  background: rgba(0, 85, 186, 0.08);
  padding: 12px 24px;
}
.c-2C2C2C {
  color: var(--color_9) !important;
}
.form-pages h2,
.form-pages h5 {
  font-size: var(--font_45);
  line-height: var(--font_45_line-height);
  text-align: center;
}
.form-pages h5 {
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
}
.f-12-747474,
.or p {
  font-family: var(--opensans-font);
  color: var(--color_11);
}
.f-12-747474 {
  font-weight: var(--font_weight_400);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
}
.f-12-747474,
.form-pages h3,
.or {
  text-align: center;
}
.or p {
  margin: 15px 0;
  font-weight: var(--font_weight_300);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  position: relative;
}
.or p::after,
.or p::before {
  content: '';
  width: 100px;
  height: 2px;
  position: absolute;
  background: var(--color_12);
  top: 10px;
}
.or p::after {
  margin-left: 15px;
}
.or p::before {
  margin-right: 15px;
  left: 108px;
}
.google-g,
.linkedin-button {
  border-radius: 8px;
  padding: 8px;
}
.google-g {
  background: var(--color_4);
  border: 1px solid #ccc;
  color: #444;
  line-height: var(--font_18_line-height);
}
.google-g img {
  width: 32px;
}
.linkedin-button {
  border: 0;
  background: #0072b1;
  color: #fff;
}
.linkedin-button i {
  font-size: var(--font_31);
  margin-right: 6px;
}
.linkedin-button span {
  position: relative;
  bottom: 5px;
}
.top-m-sp {
  margin-top: 18%;
}
.f-22 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_22) !important;
  line-height: var(--font_22_line-height) !important;
  color: var(--color_19);
}
.f-24 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_18) !important;
  line-height: var(--font_24_line-height) !important;
  color: var(--color_19);
}
.form-pages h3 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_37);
  line-height: var(--font_37_line-height);
  color: var(--color_1);
}
.big-select {
  border-radius: 4px;
  padding: 0 16px;
  width: 100%;
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  height: 35px;
  margin-bottom: 10px;
  border: 1px solid var(--color_15);
  outline: 0;
  background: #fff;
}
.c-999999 input.big-input {
  color: var(--color_10) !important;
}
.big-select,
.c-999999 .eye-icon {
  color: var(--color_10);
}
.check-label {
  color: var(--color_8);
}
.form-get label input {
  position: relative;
  top: 3px;
}
.c-747474 {
  color: var(--color_11) !important;
}
.forgot,
a.dropdown-item.item-1 {
  color: var(--color_1);
}
.popup-left-text p.f-22 {
  font-weight: 500;
}
@media (max-width: 1199px) {
  .banner-part-home h1 {
    font-size: var(--font_48);
    line-height: var(--font_48_line-height);
  }
  .font-banner-26 {
    font-size: var(--font_23);
    line-height: var(--font_23_line-height);
  }
  .footer-part h5 {
    font-size: var(--font19);
    line-height: var(--font19_line-height);
  }
  .top-m-sp {
    margin-top: 4%;
  }
}
@media (max-width: 991px) {
  .tab-m-0 {
    margin-top: 0 !important;
  }
  .tab-w-100 {
    width: 100%;
  }
  .tab-m-t-b {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .border-primary-size-22,
  .primary-size-22 {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .form-card .input-group {
    margin-bottom: 20px;
  }
  .we-to {
    margin-top: 0;
  }
  .call-us h3,
  .looking-hire h3,
  .need-help h3,
  .title-heading {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  .call-us h5,
  .we-to h5 {
    font-size: var(--font_28);
    line-height: var(--font_28_line-height);
  }
  .we-to h2 {
    font-size: 56px;
    line-height: 67.2px;
  }
  .flg-part {
    flex: 0 0 auto;
    width: 50%;
  }
  .find-bylocation h5,
  .make-it h6,
  .tips h6 {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .tab-none {
    display: none;
  }
  .form-pages h3,
  .make-it h5,
  .tips h5 {
    font-size: var(--font_33);
    line-height: var(--font_33_line-height);
  }
  .form-pages h2,
  .updated-stay h2 {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  .form-white-22-font {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .tab-sp {
    margin-top: 20px;
  }
  .form-pages h5,
  .looking-hire h6,
  .need-help h4,
  .updated-stay .font-26 {
    font-size: var(--font_23);
    line-height: var(--font_23_line-height);
  }
  .google-g {
    font-size: var(--font_16);
    color: #333;
    line-height: var(--font_16_line-height);
  }
}
@media (max-width: 767px) {
  .footer-part h5 {
    font-size: var(--font_22);
    line-height: var(--font_22_line-height);
  }
  .form-card input,
  .jab-list li {
    font-size: var(--font_13);
    line-height: var(--font_13_line-height);
  }
  .form-card #basic-addon1 {
    font-size: var(--font_16);
  }
  .talent h2,
  .top-jobs h5 {
    text-align: left;
  }
  .job-data h4 {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .jab-list li i {
    width: 18px;
  }
  .flg-part p,
  .top-jobs h5 {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
  }
  .flg-part p.font-12 {
    font-size: var(--font_11);
    line-height: var(--font_11_line-height);
  }
  .tab-part #pills-tab {
    justify-content: left;
  }
  .or p::before {
    left: 80px;
  }
  .mobile-m-0 {
    margin-top: 0 !important;
  }
  .b-des-btn {
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
}
@media (max-width: 360px) {
  .or p::before {
    left: 27px;
  }
  .banner-part-home h1 {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  .font-banner-26 {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .title-heading {
    font-size: var(--font_31);
    line-height: var(--font_31_line-height);
  }
  .top-jobs h5 {
    font-size: var(--font_17);
    line-height: var(--font_17_line-height);
  }
}
.head-part nav {
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
  z-index: 9;
}
.dashboard-page {
  background: var(--color_40);
}
.bg-fff {
  background: var(--background-color_4) !important;
}
.dash-right {
  padding: 30px 20px;
}
.m-auto {
  margin: 0 auto;
}
.name-text {
  font-weight: var(--font_weight_700);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_1);
}
.roll,
.side-menu-left li a {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_11);
}
.side-menu-left {
  text-align: left;
  padding: 0;
  max-width: 200px;
  margin: 0 auto;
}
.side-menu-left li {
  list-style: none;
  margin-bottom: 10px;
}
.side-menu-left li a {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_9);
  padding: 12px;
  display: block;
  border-radius: 6px 0 0 6px;
  transition: none;
  background: var(--color_4);
  border-right: 3px solid var(--color_4);
}
.side-menu-left li a:hover,
.side-menu-left li.active a {
  background: var(--color_3);
  border-right: 3px solid var(--color_1);
  color: var(--color_1);
}
.side-menu-left li a i {
  margin-right: 12px;
  font-size: var(--font_17);
}
.dash-card h6,
.dash-right h1 {
  font-weight: var(--font_weight_500);
}
.dash-right h1 {
  font-size: var(--font_45);
  line-height: var(--font_45_line-height);
  color: var(--color_8);
}
.dash-card {
  padding: 24px 16px;
  border-radius: 8px;
}
.d-c-1 {
  background: linear-gradient(93.12deg, #0eb1d2 0, #0055ba 100%);
}
.d-c-2 {
  background: linear-gradient(92.84deg, #48e5c2 0.42%, #0f6b57 100%);
}
.d-c-3 {
  background: linear-gradient(92.84deg, #53d8f4 0.42%, #0a8199 100%);
}
.dash-card-h5 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_33);
  line-height: var(--font_33_line-height);
  color: var(--color_4);
}
.dash-card h6 {
  font-size: var(--font_19);
  line-height: var(--font_19_line-height);
  text-align: right;
  color: #f9f9f9;
}
.dash-card .f-12 {
  color: var(--background-color_4);
  margin-bottom: 0 !important;
}
.over {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_8);
}
.sort-d-flex {
  display: flex;
}
.sort-by {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_1);
  margin: 7px 6px 0 0;
}
.all-recent {
  background: var(--background-color_4);
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  letter-spacing: -0.5px;
  color: var(--color_10);
  outline: 0;
}
.rwd-table {
  background-color: var(--color_4);
}
.rwd-table tr:not(:last-child) {
  border-bottom: 1px solid #cfe5ff;
}
@media screen and (max-width: 601px) {
  .rwd-table tr:nth-child(2) {
    border-top: none;
  }
}
@media screen and (min-width: 600px) {
  .rwd-table tr:hover:not(:first-child) {
    background-color: #d8e7f3;
  }
  .rwd-table td:before {
    display: none;
  }
  .rwd-table td,
  .rwd-table th {
    display: table-cell;
    padding: 1em !important;
  }
  .rwd-table td:first-child,
  .rwd-table th:first-child {
    padding-left: 0;
  }
  .rwd-table td:last-child,
  .rwd-table th:last-child {
    padding-right: 0;
  }
}
@media (max-width: 991px) {
  .dash-card {
    margin-bottom: 20px;
  }
  .dash-card-h5 {
    font-size: var(--font_26);
    line-height: var(--font_26_line-height);
  }
  .dash-card h6 {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
  }
  .dash-right h1 {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
}
@media (max-width: 360px) {
  .rwd-table {
    min-width: 100%;
  }
}
.pro .icon,
.rwd-table tr th i {
  margin-left: 5px;
}
.mrl-1 {
  margin: 0 10px;
}
.c-n,
.location {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_6);
}
.location {
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_19);
}
.team-m,
table.rwd-table tr th {
  font-weight: var(--font_weight_700);
}
table.rwd-table tr th {
  font-family: var(--opensans-font);
  font-size: var(--font_12);
  line-height: var(--font_12);
  color: var(--color_4);
}
.team-m {
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  text-align: center;
  color: var(--color_6);
}
.pro {
  background: var(--color_40);
  border: 1px solid var(--color_1);
  border-radius: 16px;
  padding: 4px 8px;
  font-weight: 600;
  font-size: 12px;
  color: var(--color_1);
  display: inline-flex;
  align-content: center;
}
.pro .icon {
  height: 18px;
  line-height: 18px;
}
.del-trash,
.edit-pencil {
  padding: 11px 5px;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  text-align: center;
  font-size: var(--font_12);
  cursor: pointer;
}
.edit-pencil {
  background: var(--color_40);
  color: var(--color_1);
  margin-right: 5px;
}
.del-trash {
  background: var(--color_48);
  color: var(--color_21);
}
.w-18 {
  width: 18%;
}
.free {
  background: var(--color_40);
  border: 1px solid var(--color_43);
  border-radius: 16px;
  padding: 4px 8px;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  line-height: var(--font_12_line-height);
  color: var(--color_44);
}
.all-recent.f-16-select,
.tab-filter #nav-home-tab {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.all-recent.f-16-select {
  border: 1px solid var(--color_15);
  border-radius: 4px;
  padding: 10px 16px;
  height: 56px;
  width: 290px;
}
.tab-filter #nav-home-tab {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  color: var(--color_6);
  border-bottom: solid 2px var(--color_6);
}
.free,
.tab-filter #nav-home-tab i {
  font-size: var(--font_12);
}
.tab-filter {
  position: relative;
}
.cat i,
.export {
  color: var(--color_1);
}
.export {
  position: absolute;
  right: 0;
  border: 2px solid var(--color_1);
  border-radius: 8px;
  font-weight: var(--font_weight_700);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  background: rgba(0, 85, 186, 0.08);
  padding: 12px 24px;
  top: -30px;
}
.filter {
  background: var(--color_4);
  border-radius: 0 8px 8px 8px;
  border: 1px solid var(--color_12);
}
.filter-sp {
  padding: 17px 25px;
}
.cat,
.filter-bottom {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.cat {
  background: var(--color_40);
  width: fit-content;
  padding: 4px 8px;
  border: 1px solid var(--color_3);
  border-radius: 4px;
  font-weight: var(--font_weight_400);
  color: var(--color_9);
}
.cat i {
  margin-left: 6px;
  cursor: pointer;
}
.filter-bottom {
  padding: 11px 15px;
  border-top: solid 1px var(--color_30);
  text-align: center;
  font-weight: var(--font_weight_600);
  color: var(--color_6);
}
.sp-right {
  padding-right: 6px;
}
.filter-bottom p,
.p-18 {
  margin: 0;
}
.p-18 {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
p.p-16 {
  font-weight: var(--font_weight_600);
  color: var(--color_1);
  margin: 0 0 8px;
}
.view-right li a,
p.p-16,
ul.full-time li {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
ul.full-time li {
  font-weight: var(--font_weight_400);
  color: var(--color_10);
  display: inline-block;
  padding-right: 20px;
}
ul.full-time {
  padding: 0;
  margin: 10px 0 0;
}
.posted {
  font-family: var(--opensans-font), sans-serif;
  font-weight: 400;
  font-size: 13px;
  margin: 10px 0-5px;
  display: block;
}
.pro.actively {
  color: var(--color_14);
  border: 1px solid var(--color_14);
}
.view-right {
  text-align: right;
}
.view-right li a {
  font-weight: var(--font_weight_600);
  color: var(--color_19);
}
a.dropdown-item.item-3 {
  color: var(--color_21);
}
.candidates-part {
  background: var(--color_49);
  border-radius: 8px;
  padding: 20px;
}
.tab-btn-dash {
  padding: 12px 24px;
  background: var(--color_40);
  border: 1px solid var(--color_3);
  border-radius: 4px;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}

.applicant {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.jobs-applicant {
  margin-left: auto;
}
.jobs-applicant select {
  width: 200px;
  height: 48px;
}

ul.row-btn,
ul.row-btn li {
  position: relative;
}
ul.row-btn li {
  padding-right: 10px;
}
#pills-tab li button.active,
.active-blue {
  background: var(--color_1);
}
.active-blue {
  color: var(--color_4);
}
li.right-p .export {
  position: unset !important;
}
li.right-p {
  position: absolute !important;
  right: 0;
}
.blue-text {
  color: var(--color_6) !important;
}
.black-text {
  color: var(--color_9) !important;
}
.Status-by,
.all-Status {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
}
.Status-by {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_19);
  margin-top: 6px;
  margin-right: 7px;
}
.all-Status {
  background: var(--color_4);
  border: 1px solid var(--color_15);
  border-radius: 4px;
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  letter-spacing: -0.5px;
  color: var(--color_10);
  padding: 0 !important;
  outline: 0;
  height: 32px;
}
.download,
ul.list-loc li a {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.download {
  border: 2px solid var(--color_1);
  border-radius: 8px;
  font-weight: var(--font_weight_700);
  color: var(--color_1);
  background: rgba(0, 85, 186, 0.08);
  padding: 12px 10px;
}
ul.blue-text-line,
ul.list-loc,
ul.row-btn {
  padding: 0;
  margin: 0;
}
ul.list-loc li,
ul.row-btn li {
  display: inline-block;
}
ul.list-loc li a {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  color: var(--color_15);
  padding: 10px 14px;
}
ul.list-loc li.active a {
  color: var(--color_6);
  background: var(--color_4);
  position: relative;
}
ul.list-loc li.active a::after {
  content: '';
  bottom: 0;
  width: 80%;
  height: 2px;
  position: absolute;
  background: var(--color_6);
  right: 10%;
}
select.selectMonth {
  width: 100%;
  padding: 6px 10px !important;
}
input.date-viza {
  border: 1px solid #bababa;
  height: 40px;
}
input.date-viza:focus {
  outline: 0;
}
.data-management {
  background: var(--color_49);
  border-radius: 8px;
  margin-top: 6px;
  padding: 20px;
}
.form-pages h4,
.left-list-form h4,
.left-list-form p {
  font-weight: var(--font_weight_500);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
}
.bg-form-img {
  background-image: url(../public/images/form-img2.png);
  background-size: cover;
  background-repeat: no-repeat;
}
.form-left-text {
  padding: 0 8%;
}
.form-left-text h3 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_37);
  line-height: var(--font_37_line-height);
  color: var(--color_4);
}
.left-list-form h4,
.left-list-form p {
  color: var(--color_11);
}
.left-list-form p {
  font-family: var(--opensans-font), sans-serif;
  font-weight: var(--font_weight_400);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
}
.left-list-form {
  padding: 20px 0 36px 40px;
  position: relative;
}
.left-list-form.active h4 {
  color: var(--color_50);
}
.left-list-form.active p {
  color: var(--color_12);
}
.left-list-form::before {
  content: '';
  background: var(--color_11);
  width: 16px;
  height: 16px;
  position: absolute;
  left: 9px;
  top: 25px;
  border-radius: 100px;
}
.left-list-form::after {
  content: '';
  width: 1px;
  position: absolute;
  left: 16px;
  height: 79%;
  border-left: 2px dashed var(--color_11);
  top: 40px;
  opacity: 0.4;
}
.left-list-form.active::after {
  border-left: 2px dashed var(--color_50);
}
.left-list-form:last-child::after {
  display: none;
}
.tab-none.gap-box {
  height: 250px;
}
.f-16-form {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_16_line-height);
  color: var(--color_9);
}
.skip {
  font-weight: var(--font_weight_700);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_6);
  text-decoration: underline 2px;
}
form.form-get.w-574 {
  max-width: 574px;
}
.close-x {
  background: var(--background-color_4);
  width: 35px;
  height: 35px;
  color: var(--color_1);
  font-size: var(--font_22);
  padding: 4px 5px;
}
.popup-right {
  background-image: url(../public/images/popup-right.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 0 30px 30px 0;
}
.close-x.mt-2 i {
  position: relative;
  bottom: 2px;
}
.br-16 {
  border-radius: 16px !important;
}
.popup-left-text {
  padding: 33px;
}
.popup-0EB1D2 h4,
.popup-left-text h3 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_37);
  line-height: var(--font_37_line-height);
  color: var(--color_8);
}
.explore-jobs a,
.popup-left-text p,
ul.blue-text-line li a {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
}
.popup-left-text p {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  color: var(--color_19);
}
.popup-0EB1D2 h4 {
  font-family: var(--opensans-font), sans-serif;
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
.popup-0EB1D2::before {
  background: var(--color_28);
  width: 12px;
  height: 12px;
  left: 11px;
}
.popup-0EB1D2::after {
  border-left: 2px dashed var(--color_28) !important;
}
.popup-0EB1D2.line-none::after {
  display: none;
}
.left-list-form.popup-0EB1D2 {
  padding: 20px 0 7px 40px;
}
.d-c-4 {
  background: linear-gradient(92.84deg, #ffb8a8 0.42%, #ff6847 100%);
}
.explore-jobs a,
ul.blue-text-line li a {
  font-weight: var(--font_weight_700);
  color: var(--color_6);
  text-decoration: underline 2px;
}
ul.blue-text-line li {
  list-style: none;
  display: inline-block;
  padding-left: 15px;
}
.em-name {
  font-weight: var(--font_weight_700);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
  margin: 0;
}
.em-work {
  font-weight: var(--font_weight_500);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  color: var(--color_1);
  margin: 0;
}
.c-000 {
  color: var(--color_47);
}
.f-18 {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_700);
  font-size: var(--font_18) !important;
  line-height: var(--font_18_line-height) !important;
  color: var(--color_9);
  margin: 0;
}
p.f-16.w-600 {
  font-weight: 600 !important;
}
p.f-16.c-0055BA {
  color: var(--color_1);
  margin: 5px 0 !important;
}
p.f-16.c-999999 {
  color: var(--color_10);
}
.skills {
  margin: 0;
  padding: 0;
}
.skills li {
  display: inline-block;
  padding: 0 1%0 0;
}
.link-right-icons {
  position: relative;
}
.link-right-icons p {
  position: absolute;
  right: 5px;
  top: 0;
}
.link-right-icons p a {
  font-size: var(--font_18);
  color: var(--color_11);
  padding: 0 0 0 8px;
}
.add_company_upload_logo_btn {
  top: 50px;
}
.uploade-btn {
  position: relative;
}
.uploade-btn input {
  position: absolute;
  top: 0;
  opacity: 0;
  padding: 10px 0;
  cursor: pointer;
}
.d-card {
  background: var(--color_49);
  border-radius: 8px;
}
.w-600 {
  font-weight: var(--font_weight_600) !important;
}
.default,
.list-message li a,
.search-center-box input {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
}
.default {
  padding: 6px 12px;
  background: var(--color_15);
  border-radius: 17px;
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
  color: var(--color_4);
}
.default-set {
  padding: 6px 12px;
  border-radius: 17px;
  font-size: var(--font_12);
  font-weight: 600;
  background: var(--c_fff);
  color: #216dbf;
  border: 1px solid #216dbf;
  pointer-events: none;
}
.w-box {
  border: 1px solid var(--color_12);
  border-radius: 8px;
}
.resume-text {
  font-weight: var(--font_weight_500);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  color: var(--color_9);
  padding-right: 10px;
}
.tab-span-sa {
  background: var(--color_15);
  color: var(--color_4);
  font-size: var(--font_11);
  margin-left: 4px;
}
.hot {
  border: 1px solid var(--color_23) !important;
  background: var(--color_51);
  color: var(--color_21) !important;
}
.tab-span-sa.c-0070F5 {
  background: var(--color_6);
  color: var(--color_4) !important;
}
.w-400 {
  font-weight: 400 !important;
}
.c-BABABA {
  color: var(--color_15) !important;
}
.c-4D4D4D {
  color: var(--color_19) !important;
}
.box-text-img {
  padding: 15px;
  align-items: center;
}
.box-text-img .avatar {
  margin-right: 20px;
}
.bg-CFE5FF {
  background: var(--color_40);
  border: 1px solid var(--color_3);
  border-radius: 8px;
}
.b-r-30 .popup-modal .border-left {
  border-radius: 30px 0 0 30px;
}
.b-r-30 .modal-content {
  border-radius: 30px;
}
.p-25 {
  padding: 24% 16px;
}
.b-r-30 .close-x {
  box-shadow: unset;
  top: -15px;
  position: relative;
  right: -5px;
}
.b-r-30 {
  margin-top: 83px;
}
.head-part ul.dropdown-menu.show {
  left: unset;
  right: 0;
}
.text-upper {
  text-transform: uppercase;
}
.progress-w {
  background: #3d9f79;
}
.progress,
.progress-w {
  border-radius: 60px;
}
.article-ul li {
  font-size: 18px;
  font-weight: bold;
  color: #0070f5;
  list-style: circle;
  margin-left: 20px;
  margin-bottom: 12px;
  text-decoration: underline;
}
.article-ul li {
  margin-top: 20px;
}
.article-box {
  background-color: #f5f5f5;
  padding: 24px 16px;
}
.job-portal {
  font-size: 45px;
  font-weight: 600;
  margin-top: 18px;
}
.talent-point {
  font-size: 37px;
  font-weight: 500;
  margin-top: 16px;
}
.your-partner {
  font-size: 22px;
  font-weight: 500;
  margin-top: 16px;
}

.article-heading {
  font-size: 18px;
  font-weight: 500;
  color: #151515;
}
.job-portal-para {
  font-size: 18px;
  font-weight: 400;
  margin-top: 16px;
}
.expand-job-heading {
  font-size: 22px;
  font-weight: 700;
  line-height: 26px;
  margin: 0 0 16px !important;
}
.expand-job {
  padding: 16px;
  border-radius: 16px;
  background: #ebf4ff;
  margin: 0 0 16px;
}
.expand-job ul li a {
  font-size: 18px;
  font-weight: 600;
  color: #2c2c2c;
  padding: 12px;
  border-radius: 8px;
  background-color: #fff;
  display: inline-block;
  border: 1px solid #cfe5ff;
  line-height: 28px;
}
.expand-job ul li:hover a {
  color: #0070f5;
  border: 1px solid #0070f5;
}
.expand-job ul {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
.know-your {
  font-weight: 700;
  font-size: 18px;
  line-height: 28.8px;
  margin: 0 0 4px;
}
.know-your-para {
  font-weight: 300;
  font-size: 12px;
  line-height: 16px;
  color: #686868;
  margin: 0 0 16px;
}
.know-your-btn {
  padding: 12px 12px;
  color: #fff;
  background-color: #0055ba;
  font-size: 14px;
  border-radius: 8px;
  border: none;
  line-height: 16px;
}
.right-side {
  width: 60%;
  padding: 10px 20px;
}
.left-side {
  width: 15%;
  padding: 10px;
}
.third-box {
  width: 25%;
  padding: 10px;
}
.right-side p {
  font-size: 18px;
  line-height: 28px;
}
@media (min-width: 768px) {
  .form-left-right-add-sp {
    padding: 0 7%;
  }
}
@media (min-width: 1024px) {
  #mar-top {
    margin-top: 65px !important;
  }
  #mar-top-lg {
    margin-top: 120px !important;
  }
}
@media (min-width: 1200px) {
  .form-left-right-add-sp {
    padding: 0 13%;
  }
  .wid-max {
    max-width: 940px;
  }
}
@media (min-width: 1500px) {
  .max-340 {
    max-width: 340px !important;
  }
}
@media (max-width: 1199px) {
  .c-n {
    font-family: var(--opensans-font);
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
  .location {
    font-size: var(--font_12);
    line-height: var(--font_12_line-height);
  }
  .team-m {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
  }
  table.rwd-table tr td {
    padding: 5px !important;
  }
  .del-trash,
  .edit-pencil {
    width: 25px;
    height: 25px;
    padding: 7px 5px;
  }
  p.c-n img.w-25 {
    width: 30px !important;
  }
  .rwd-table th {
    padding: 7px 5px !important;
    font-size: 8px !important;
  }
  #dropdownMenuButton1.primary-size-16,
  .download {
    font-size: var(--font_12);
    line-height: var(--font_12_line-height);
    padding: 10px 4px;
  }
  .download {
    padding: 12px 4px;
  }
  .form-left-text h3 {
    font-size: var(--font_33);
    line-height: var(--font_33_line-height);
  }
  .left-list-form h4 {
    font-size: var(--font_22);
    line-height: var(--font_22_line-height);
  }
  .left-list-form p {
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
  .left-list-form {
    padding: 20px 0 7px 40px;
  }
  .profile-checklist {
    margin: 0 auto;
  }
  .tab-add-sp {
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .rwd-table td:before {
    width: 50%;
    font-size: 12px;
    color: var(--color_38);
  }
  .rwd-table td p {
    width: 50%;
    float: right;
    margin: 0;
    text-align: left;
  }
  .free,
  .pro {
    padding: 2px 5px;
    font-size: var(--font_11);
    line-height: var(--font_11_line-height);
  }
  #dropdownMenuButton1.primary-size-16 {
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
    padding: 10px 4px;
  }
  .m-center {
    text-align: center;
  }
  .filter ul.dropdown-menu.view-right {
    width: 100%;
  }
  .all-recent.f-16-select {
    width: 80%;
  }
  .export {
    font-size: var(--font_11);
    line-height: var(--font_11_line-height);
    padding: 8px 10px;
    top: 3px;
    right: 0;
    position: relative;
  }
  .tab-filter #nav-home-tab {
    padding: 11px 10px;
  }
  .cat,
  .tab-filter #nav-home-tab {
    font-size: var(--font_12);
    line-height: var(--font_12_line-height);
  }
  .p-18 {
    margin: 10px 0;
  }
  .download {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
    padding: 12px 10px;
  }
  .Status-by {
    font-size: var(--font_12);
    line-height: var(--font_12_line-height);
  }
  .candidates-part .logo-filter {
    padding-right: 15px;
  }
  .tab-btn-dash {
    padding: 10px 9px;
    margin: 4px 0;
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
  li.right-p {
    position: unset !important;
    margin-top: 6px;
  }
  .dash-right {
    padding: 10px;
  }
  ul.list-loc li {
    display: inline-block;
    margin-bottom: 20px;
  }
  ul.blue-text-line li a,
  ul.list-loc li a {
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
  .dash-right h1 {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  h1.data {
    font-size: var(--font_31);
  }
  .popup-left-text h3 {
    font-size: var(--font_33);
    line-height: var(--font_33_line-height);
  }
  .m-column-reverse {
    flex-direction: column-reverse;
  }
  .m-m-0 li {
    margin-bottom: 0 !important;
  }
  .m-p-10 {
    padding: 10px;
  }
  .rwd-table .f-18 {
    font-size: var(--font_13);
  }
  .status_filter_tab_section {
    display: block;
    margin-top: 10px;
  }
}
@media (max-width: 355px) {
  p.c-n img.w-25 {
    width: 19px !important;
  }
  .c-n {
    font-size: var(--font_12);
    line-height: var(--font_12_line-height);
  }
  .profile-checklist {
    width: 100%;
  }
  .rwd-table .f-18 {
    font-size: var(--font_11);
  }
}
.bg-D9D9D9 {
  background: var(--color_4);
  border: 1px solid var(--color_12);
  border-radius: 8px;
}
.bg-EBF4FF {
  background: var(--color_40);
  border: 1px solid var(--color_3);
  border-radius: 8px;
}
.w-700 {
  font-weight: var(--font_weight_700) !important;
}
p.f-12.mb-sp {
  margin-bottom: 0 !important;
}
h3.f-54 {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_700);
  font-size: var(--h1_size);
  line-height: var(--h1_size_line-height);
}
.c-191919,
h3.f-54 {
  color: var(--color_8);
}
.c-0070F5 {
  color: var(--color_6) !important;
}
.c-2C2C2C {
}
.icon-w-12 {
  width: 12px;
}
.mt-sp-add {
  margin-top: 6px !important;
}
.border-radius-4 {
  border-radius: 4px;
  padding: 8px 16px;
}
.dash-right {
  background-image: url(../public/images/bg-dash.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 98vh;
}
.list-message {
  margin: 0;
  padding: 0;
}
.list-message li {
  display: inline-block;
  padding: 0 10px 0 0;
}
.list-message li a,
.search-center-box input {
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: var(--color_19);
}
.head-message {
  padding: 20px;
  border-bottom: solid 1px var(--color_40);
}
.body-message {
  padding: 20px;
}
.search-center-box input {
  width: 100%;
  font-weight: var(--font_weight_400);
  color: var(--color_11);
  outline: 0;
  padding: 14px 10px;
  border: 0;
  background: #fff0;
}
.search-center-box {
  background: var(--color_40);
  border: 1px solid var(--color_3);
  border-radius: 16px;
  padding: 16px;
  max-width: 640px;
  margin: 0 auto;
}
.search-center-box input::placeholder {
  color: var(--color_11);
}
.message-send,
.send-fild {
  background: var(--color_4);
  padding: 10px 16px;
}
.message-send {
  border: 1px solid var(--color_15);
  border-radius: 4px;
  width: 100%;
}
.send-fild {
  border: 1px solid var(--color_39);
  border-radius: 16px;
}
.send-fild ::placeholder {
  color: var(--color_10);
}
.switch.btn-swith {
  max-width: 60px;
}
#pills-tab li button.active,
.blue-active .active a,
.c-fff {
  color: var(--color_4) !important;
}
.pool-today {
  background-image: url(../public/images/blue-back2.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  padding: 30px;
  border-radius: 16px;
}
.span-yellow {
  color: var(--color_26);
}
.f-26 {
  font-weight: var(--font_weight_500);
  font-size: var(--font_26);
  line-height: var(--font_26_line-height);
  color: var(--color_9);
}
.c-151515 {
  color: var(--color_27);
}
.bg-0055BA {
  background-color: var(--color_1);
}
.f-37 {
  font-weight: var(--font_weight_700);
  font-size: var(--font_37);
  line-height: var(--font_37_line-height);
}
.m-r-l-sp {
  margin: 0 2%0 3%;
}
a.dropdown-item.item-2:hover {
  color: var(--color_1);
  background: var(--color_4);
}
.w-400-list a.dropdown-item.item-2 {
  font-weight: var(--font_weight_400);
}
ul.full-time.f-12-list li {
  font-weight: var(--font_weight_600);
  font-size: var(--font_12);
  line-height: var(--font_12_line-height);
}
.c-D9D9D9 {
  color: var(--color_12);
}
.frontend_user_dropdown ul.dropdown-menu.show .candidate-box ul.can-list li,
.m-sp-0,
p.tag-set-col.f-12.w-700.c-0055BA.bg-CFE5FF {
  margin: 0 !important;
}
.right-border {
  border-right: solid 2px var(--color_30);
}
.up-icon {
  font-size: var(--font_22);
}
.border-r-16 {
  border-radius: 16px;
}
.blue-box {
  background: var(--color_6);
  border-radius: 8px;
  padding: 16px;
}
.blue-box p.f-26 {
  margin: 0;
}
.c-CFE5FF {
  color: var(--color_3);
}
.work-senior {
  width: 108px;
  margin-bottom: 6px;
}
.blue-box p.f-16.w-600 {
  margin: 4px 0 !important;
}
.blue-active .active a {
  background: var(--color_1) !important;
  border-radius: 4px 4px 0 0 !important;
}
.blue-active .active a::after,
textarea {
  background: var(--color_4) !important;
}
.circle-round {
  font-size: 9px;
  color: var(--color_1);
}
.border-0 {
  border-radius: 0;
}
.b-fff {
  background: var(--color_4);
  padding: 15px;
  border: 1px solid var(--color_3);
}
.c-3D9F79 {
  color: var(--color_14) !important;
}
.c-FD7373 {
  color: var(--color_23) !important;
}
i.fa-circle-info.c-D9D9D9 {
  font-size: var(--font_14);
  position: relative;
  bottom: 2px;
}
.big-short {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_600);
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
.pl-sp {
  padding-left: 6%;
}
p.f-16.w-700 {
  font-weight: var(--font_weight_700) !important;
}
.pencil-12 {
  font-size: var(--font_12);
}
.round-img-200 {
  border-radius: 200px;
}
.hiring,
.last-fields {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  border: 0;
  outline: 0;
}
.last-fields {
  font-style: normal;
  font-weight: var(--font_weight_400);
  color: #252c32;
}
.hiring {
  font-weight: var(--font_weight_600);
  color: var(--color_1);
  text-align: right;
}
.hiring option {
  border: 0;
  padding: 8px;
}
.Senior-div.work-senior {
  width: 100%;
}
@media (max-width: 767px) {
  .pool-today {
    background-image: url(../public/images/blue-back2-mobile.jpg);
  }
  .get-wow p.f-22,
  .updated-stay .f-22.c-fff {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .m-sp-0 {
    margin-top: 0 !important;
    margin-bottom: 10px;
  }
  .sp-80 {
    padding: 40px 0 !important;
  }
  .mobile-left {
    text-align: left;
  }
  .mobile-f-23 {
    font-size: var(--font_23);
    line-height: var(--font_23_line-height);
  }
  .big-short,
  .get-wow p.f-18 {
    font-size: var(--font_16);
    line-height: var(--font_16_line-height);
  }
  .and-there .f-37 {
    font-size: var(--font_33);
    line-height: var(--font_33_line-height);
  }
  .and-there p.f-22,
  p.f-22 {
    font-size: var(--font_19);
    line-height: var(--font_19_line-height);
  }
  .mt-1.m-r-l-sp {
    margin: 0;
  }
  p.f-22 {
    font-size: var(--font_19) !important;
  }
  .big-short big {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  .name-candi p {
    float: unset !important;
    width: 100% !important;
  }
  .right-time p {
    margin-top: -18px !important;
    width: 77% !important;
  }
  .mobile-add-sp {
    margin: 15px 0 6px;
  }
}
.choose-currency {
  font-family: var(--opensans-font), sans-serif;
  font-style: normal;
  font-weight: var(--font_weight_400);
  font-size: var(--font_14);
  line-height: var(--font_14_line-height);
  letter-spacing: -0.5px;
  color: var(--color_10);
  width: 100%;
  background: var(--color_4);
  border: 1px solid var(--color_15);
}
.salary-box {
  background: var(--color_4);
  border: 1px solid var(--color_12);
  border-radius: 16px;
  padding: 16px;
}
.pad-sp {
  top: 18px !important;
  width: 12px;
}
.c-999999::placeholder {
  color: var(--color_10) !important;
}
.input-bababa input.medium-input.c-999999 {
  border: 1px solid var(--color_15);
  border-radius: 4px;
}
.pog-r {
  position: relative;
}
.logoCircle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.logoCircle {
  width: 195px;
  height: 195px;
  border-radius: 200px;
}
.imageCover {
  height: 280px;
  overflow: hidden;
}
.withoutImageCover {
  height: 80px;
  overflow: hidden;
}
.edit-banner {
  position: absolute;
  right: 15px;
  top: 10px;
  padding: 8px 6px;
  font-size: var(--font_12);
  width: 24px;
  height: 25px;
  background: var(--color_4);
  border-radius: 4px;
  cursor: pointer;
}
.f-54 {
  font-size: var(--h1_size);
  line-height: var(--h1_54_size_line-height);
}
.c-000,
textarea {
  color: #000 !important;
}
.skills-f-18 p.cat {
  font-size: var(--font_18);
  line-height: var(--font_18_line-height);
}
button.btn-bg-3D9F79,
button.btn-bg-D04E4F {
  font-style: normal;
  font-weight: var(--font_weight_600);
  line-height: var(--color_24);
  letter-spacing: -0.5px;
  border-radius: 4px;
  padding: 6px 7px;
}
button.btn-bg-3D9F79 {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
  color: var(--color_20);
  background: var(--background-color_11);
  border: 1px solid var(--color_14);
}
button.btn-bg-D04E4F {
  color: #d04e4f;
  background: #fffceb;
  border: 1px solid #d04e4f;
}
.tab-popup button,
button.btn-bg-D04E4F,
button.btn-bg-D57B11 {
  font-family: var(--opensans-font);
  font-size: var(--font_16);
}
button.btn-bg-D57B11 {
  font-style: normal;
  font-weight: var(--font_weight_600);
  line-height: var(--color_24);
  letter-spacing: -0.5px;
  color: #d57b11;
  background: #fffceb;
  border: 1px solid #d57b11;
  border-radius: 4px;
  padding: 6px 7px;
}
.mar-m {
  margin-top: -35px;
  position: relative;
  margin-bottom: 10px;
}
.over-h-none {
  overflow: unset !important;
  max-width: 200px !important;
}
.over-h-none img {
  border-radius: 131px;
}
.salary-box.border-r-0 {
  border-radius: 8px;
}
.header-remove .modal-header {
  display: none;
}
.body-sp-0 .modal-body {
  padding: 0;
}
.popup-right.border-left {
  border-radius: 16px 0 0 16px;
}
.big-size {
  max-width: 856px !important;
}
#headerdropdownMenuButton1 .fa-ellipsis-vertical {
  font-size: 25px;
  color: var(--color_1);
  margin: 4px 0 0;
}
.add_company_signup_popup .modal-header {
  position: absolute;
  right: 0;
  z-index: 99;
}
.add_company_signup_popup .modal-body {
  padding: 0;
}
.bg-0055BA .fa-xmark {
  color: var(--color_4);
}
.border-design {
  border-radius: 0 0 0 12px;
  position: absolute;
  right: 0;
  top: 0;
}
.head-box {
  background: var(--color_3);
  padding: 12px 18px;
}
.popup-body {
  padding: 18px;
}
.w-120 {
  width: 120px;
}
.f-31,
.time-mon-fri strong {
  font-weight: var(--font_weight_700);
}
.f-31 {
  font-size: var(--font_31);
  line-height: var(--font_31_line-height);
  text-align: center;
  color: var(--color_9);
}
.sp-50 {
  padding: 50px 0;
}
.bg-none {
  box-shadow: unset;
}
.h-2 {
  height: 200px;
}
.c-474D66 {
  color: #474d66;
}
.w-500 {
  font-weight: var(--font_weight_500) !important;
}
.popup-left-2 {
  background-image: url(../public/images/popup-left-2.png) !important;
}
.c-0055BA {
  color: var(--color_1);
}
.switch-w {
  display: flex;
}
.switch-w p.pog-r {
  top: 8px;
}
.hr-line {
  background: #cfe5ff;
  opacity: 1;
}
.tab-popup button {
  font-weight: var(--font_weight_400);
  line-height: var(--font_16_line-height);
  color: var(--color_15) !important;
  padding: 10px 0;
  margin-right: 20px;
}
.tab-popup button.active {
  color: var(--color_6) !important;
  background: #fff0 !important;
  border-bottom: solid 2px var(--color_6);
  border-radius: 0;
}
ul.skills.pop-skils li {
  margin-right: 7px;
  margin-bottom: 7px;
}
.time-mon-fri strong {
  color: var(--color_19);
}
.login_footer_section {
  max-width: 462px;
  margin: 0 auto;
}
.m-mins {
  margin-top: -25px;
}
#pb-sp-0 {
  padding-bottom: 0 !important;
}
.mar-bot-75 {
  margin-bottom: 75px;
}
.side-menu-left::-webkit-scrollbar {
  width: 1px;
}
.side-menu-left::-webkit-scrollbar-track {
  background: #fff0;
}
.side-menu-left::-webkit-scrollbar-thumb,
.side-menu-left::-webkit-scrollbar-thumb:hover {
  background: #fff0;
}
.all-recent {
  background: var(--color_4);
  border: 1px solid var(--color_15);
  border-radius: 4px;
  padding: 4px 8px;
  height: 32px;
}
.cat.bg-CFE5FF {
  background: var(--color_3);
}
ul.dropdown-menu.view-right.show {
  box-shadow:
    0 1px 3px rgba(21, 21, 21, 0.12),
    0 2px 5px rgba(21, 21, 21, 0.1),
    0 4px 12px rgba(21, 21, 21, 0.12);
  border-radius: 8px;
}
.z-num {
  z-index: 0 !important;
}
.z-number-9 {
  z-index: 9;
}
@media (min-width: 1020px) {
  .left-bar {
    position: relative;
  }
  .last-footer ul.social-icons {
    margin-right: 10%;
  }
}
@media (min-width: 1920px) {
  .container {
    max-width: 1690px !important;
  }
  .max-search {
    max-width: 959px !important;
  }
  .max-search button.btn-a.primary-size-16 {
    padding: 12px 35px;
  }
  .full-width {
    max-width: 94% !important;
  }
}
@media (min-width: 1500px) {
  .full-width a.navbar-brand {
    position: relative;
    left: 4%;
  }
  .side-menu-left {
    height: 415px;
  }
}
input {
  background: var(--color_4);
}
.bg-form-img,
.frontend_user_dropdown ul.dropdown-menu.show .candidate-box ul.can-list,
nav.navbar ul.dropdown-menu {
  padding: 0;
}
.admin-dropdown {
  display: block;
  width: 100%;
  padding: 0.25rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.fixd-left-box {
  padding: 40px;
  position: fixed;
  width: 41.7%;
  background-image: url(../public/images/form-img2.png);
  background-size: cover;
  background-repeat: no-repeat;
  overflow-y: scroll;
  height: 100vh;
}
.fixd-left-box::-webkit-scrollbar {
  width: 1px;
}
.fixd-left-box::-webkit-scrollbar-track {
  background: #fff0;
}
.fixd-left-box::-webkit-scrollbar-thumb,
.fixd-left-box::-webkit-scrollbar-thumb:hover {
  background: #fff0;
}
.stay-updated .email-form {
  margin-top: 50px !important;
}
.border-green {
  border: 1px solid var(--color_14);
}
@media (max-width: 991px) {
  .f-54 {
    font-size: var(--font_40);
    line-height: var(--font_40_line-height);
  }
  .mar-m {
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .f-37 {
    font-size: var(--font_28);
    line-height: var(--font_28_line-height);
  }
  .f-54 {
    font-size: var(--font_31);
    line-height: var(--font_31_line-height);
  }
  .link-right-icons p {
    position: unset;
    text-align: center;
    margin-top: 16px;
  }
  .mobole-add-sp {
    margin: 20px 0 !important;
  }
}
.btn-bg-0055BA:focus,
.btn.signup:focus,
p.banner-btn a:focus {
  background: #002552;
  box-shadow: unset;
  border-color: #002552;
}
.btn:focus {
  box-shadow: unset;
}
.close-pog {
  position: absolute;
  right: 10px;
  top: 10px;
}
.modal-md-size {
  max-width: 635px;
}
@media (min-width: 1300px) {
  .form-search-home .form-card .col-lg-5 {
    width: 44%;
  }
  .form-search-home .form-card .col-lg-2 {
    width: 10%;
  }
}
.line-height-22 {
  line-height: 22.4px !important;
}
.border-r .modal-content {
  border-radius: 30px;
}
.close-icon .close-x {
  top: -15px;
  position: relative;
  left: 10px;
}
.btn-a.border-primary-size-22.border-0055BA:focus,
.download:focus,
.login:focus,
button.border-0055BA.bg-ebf1f9:focus {
  border: solid 2px var(--color_5);
  color: var(--color_5);
  background: var(--color_4);
}
.hover-eff {
  overflow: hidden;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}
.hover-eff:focus,
.hover-eff:hover {
  color: var(--color_4) !important;
}
.hover-eff:hover:before {
  border-radius: 0;
}
.hover-eff:before {
  content: '';
  background: var(--background-color_1);
  height: 100%;
  transform: scale(0);
  padding: 30px;
  position: absolute;
  top: 0;
  left: 40%;
  z-index: -1;
  border-radius: 100px;
  transition: all 0.3s ease 0;
  margin: 0 auto;
}
.hover-eff:focus:before,
.hover-eff:hover:before {
  transform: scale(1);
  width: 100%;
  left: 0;
}
.max-131 {
  max-width: 131px !important;
}
.border-radius-16 {
  border-radius: 16px;
}
.w-100.max-100 {
  max-width: 100% !important;
}
hr.color-change {
  background: var(--color_35);
  opacity: 1;
}
@media (min-width: 1024px) {
  .close-icon .left-list-form {
    position: relative;
    right: 11px;
  }
}
@media (min-width: 1800px) {
  .form-pages h4,
  .spacing-left-1920 {
    padding-left: 14%;
  }
}
@media (max-width: 991px) {
  .fixd-left-box {
    position: unset;
    padding: 20px;
    width: 100%;
  }
  .mob-mt-sp {
    margin-top: 10px !important;
  }
}
@media (max-width: 767px) {
  ul.list-loc li {
    margin-top: 12px;
  }
  .tips {
    padding-top: 40px;
    padding-bottom: 25px;
  }
  .get-ready {
    margin-top: 60px;
  }
  .code-job1 {
    width: 76% !important;
  }
}
.user-img .user_name_letter {
  background: #edeff5;
  color: #8f95b1;
  font-size: 18px;
  font-weight: 500;
  border-radius: 23px;
}
.w-60-sp {
  width: 90px;
  padding: 0 5px !important;
}
.pricing-pop .modal-content {
  background: #fff;
  border-radius: 10.98889px;
}
.pricing-pop ul.check-close li {
  font-size: 11.9556px;
  line-height: 16px;
  margin: 10px 0;
}
.pricing-pop ul.check-close li i {
  width: 17px;
  height: 17px;
  padding: 3px 4px;
  font-size: 9px;
  left: -25px;
}
.pricing-pop ul.check-close {
  padding: 0 0 0 22px;
}
.pricing-pop .started-part {
  font-size: 13.45px;
  line-height: 16.14px;
}
.pricing-pop p {
  margin: 0 !important;
  text-align: left !important;
}
.head-icon .bell-box a,
.pricing-pop .plan-box h3 small {
  font-size: 16px;
}
.pricing-pop .plan-box h3 sup {
  font-size: 18px;
}
.drop-new button::after,
.head-d-none .modal-header,
.side-menu-left li a:hover .icon-a {
  display: none;
}
img.img-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}
button.accordion-button.border-green.ready-to-interview-class {
  color: #0c5a14;
  background: #dcf2ea;
  border: 1px solid #3d9f79;
}
button.accordion-button.border-green.Open-to-Offers-class {
  color: #d57b11;
  background: #fffceb;
  border: 1px solid #d57b11;
}
button.accordion-button.border-green.not-looking-class {
  color: #d04e4f;
  background: #ffe5e5;
  border: 1px solid #d04e4f;
}
.up-down-icon {
  transition: transform 1.5s ease-out;
}
/* .fa-chevron-up {
  transform: rotate(180deg);
} */
.content {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}
.show {
  opacity: 1;
}
.form-card select {
  border-radius: 0 100px 100px 0 !important;
  background: var(--color_4);
  border-left: 0 !important;
  border-color: #0055ba47;
  padding: 13px 4px;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  color: #5c5c5c;
}
p.error {
  margin-top: -15px;
  color: #d04e4f;
}
.company_profile_page_section {
  height: inherit;
}
.form-fild {
  position: relative;
}
i.right-line {
  opacity: 1 !important;
  border-right: solid 1px #999;
  padding-right: 5px;
  bottom: 38px !important;
}
.form-fild i {
  color: #191919;
  opacity: 0.4;
  position: absolute;
  left: 16px;
  bottom: 20px;
  font-size: 17px;
}
.form-fild input {
  padding: 0 40px !important;
}
.candidate_application_popup_body {
  overflow-y: scroll;
  height: 750px;
}
.skills_sections {
  display: flex;
  padding: 0;
  list-style: none;
}
.skills_sections li {
  background: #cfe5ff;
  padding: 5px 10px;
  margin: 5px;
  border-radius: 5px;
}
.form_field_sec {
  display: flex;
  flex-direction: column-reverse;
}
.form-experience-fieild select:focus-visible + label,
.form-experience-fieild textarea:focus + label,
.form-get input:focus + label {
  color: var(--color_1) !important;
}
a.rmewp {
  color: #0070f5;
  margin-right: 10px;
  cursor: pointer;
}
.upload_profile_image_model_dialog {
  margin-top: 100px;
  position: relative;
  z-index: 9999;
}
.abc_toast-footer {
  background: var(--background-color_4);
  border-radius: 4px;
  margin-top: 2px;
  padding: 3px 10px;
  font-family: var(--opensans-font);
  position: absolute;
  top: 12%;
  width: 36%;
  left: 45%;
}
.back-img-form {
  background-image: url(../public/images/form-img.png);
  background-size: cover;
  background-repeat: no-repeat;
}
.same-pad-up-down {
  margin: 0 !important;
  padding: 10%0 !important;
}
.filter.filter-sp.m-center,
.form-experience-fieild textarea.fild-des {
  margin-bottom: 15px;
}
@media (min-width: 585px) {
  .filter.filter-sp img.logo-filter,
  .image-same-size img.logo-filter {
    width: 78px;
    height: 78px;
  }
}
@media (min-width: 1100px) {
  .h-100vh {
    height: 92vh !important;
  }
}
@media (min-width: 1800px) {
  .back-img-form {
    height: 92vh;
  }
  .form-search-home .form-card .col-lg-5 {
    width: 45%;
  }
}
.form-experience-fieild .fild-des {
  color: #000 !important;
}
.work-experience-fieild .uploade-btn .btn-a.primary-size-16 {
  width: 245px;
  text-align: center;
}
.code-job h5 {
  margin-top: 3px;
}
.code-job .link-12 {
  position: relative;
  bottom: 3px;
}
.user-img .user_name_letter {
  display: block;
  width: 35px;
  height: 35px;
  padding: 4px 0;
}
.code-job {
  position: absolute;
  top: 80px;
  width: 32%;
  left: 48%;
  z-index: 9;
}
@media (max-width: 767px) {
  .m-none {
    display: none;
  }
}
.popup-body.scroll-pop-h {
  overflow-y: scroll;
  height: 450px;
}
.modal-body ::-webkit-scrollbar {
  width: 1px;
}
.modal-body ::-webkit-scrollbar-track {
  background: #f1f1f1;
}
.modal-body ::-webkit-scrollbar-thumb {
  background: #888;
}
@media (min-width: 1800px) {
  .popup-body.scroll-pop-h {
    height: 600px;
  }
}
.side-menu-left li a .icon-a,
.side-menu-left li a .icon-hover,
a#blog-icon i {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  transition: 0.5s;
}
.side-menu-left li a .icon-hover {
  display: none;
}
.side-menu-left li a:hover .icon-hover,
.side-menu-left li.active .icon-hover {
  display: unset;
}
.side-menu-left li.active a .icon-a {
  display: none !important;
}
.filter-pop .salary-box {
  border: solid 1px #eee;
}
.filter-pop .medium-input {
  border: solid 1px #bababa !important;
}
.filter-pop .form-in i {
  color: #191919 !important;
}
.filter-pop .form-master-field {
  padding-left: 12px;
}
.border-none-popup .modal-content {
  border: 0;
}
.b-r-8 {
  border-radius: 8px !important;
}
@media (min-width: 992px) {
  .dask-none {
    display: none;
  }
}
@media (max-width: 991px) {
  .tab-scroll-view .left-bar {
    overflow-y: scroll;
    height: 380px;
    padding-bottom: 30px;
  }
}
.left-accor {
  position: relative;
}
.left-accor div#collapseOne {
  position: absolute;
  top: 51px;
  box-shadow: 0 2px 11px #00000033;
}
.left-accor .accordion-body p {
  margin: 0;
}
.form-get input:focus,
input.big-input:focus {
  color: #000 !important;
}
.error,
.text-danger {
  padding-left: 22px;
  position: relative;
}
.text-danger::before {
  content: '!';
  position: absolute;
  left: 1px;
  border: solid 1px #dc3545;
  font-size: 10px;
  font-weight: 800;
  top: 3px;
}
.error::before,
.share-post li a,
.text-danger::before {
  text-align: center;
  border-radius: 100px;
}
.error::before {
  content: '!';
  position: absolute;
  left: 1px;
  border: solid 1px #dc3545;
  width: 15px;
  height: 15px;
  padding: 0 4px;
  font-size: 10px;
  font-weight: 800;
  top: 1px;
}
.sender {
  text-align: right;
}
.receiver {
  text-align: left;
}
.messages_chat_section {
  height: 450px;
  overflow-x: hidden;
  padding: 10px;
}
.m-0auto {
  margin: 0 auto;
}
.check-i {
  position: absolute;
  right: 28%;
  width: 50px;
  bottom: 28%;
}
.fa-spin {
  -webkit-animation-duration: var(--fa-animation-duration, 5s) !important;
  animation-duration: var(--fa-animation-duration, 5s) !important;
}
.toast-box.color-0055BA.code-job {
  margin-top: 18px;
  border: solid #eee 1px;
  box-shadow: 4px 4px 8px #c7c7c757;
  left: unset;
  max-width: 433px;
  right: 30px;
}
.toast-footer {
  padding: 8px;
}
.pro-diamond {
  position: relative;
  background: var(--color_4);
  border-radius: 100px;
  overflow: hidden;
}
.gem-diamond {
  text-align: center;
  background: var(--color_6);
  position: absolute;
  bottom: 0;
  width: 100%;
  border-radius: 0 0 100% 100%;
  padding: 1px 0;
  color: var(--color_4);
  font-size: 14px;
}
a.fa-gem-pen {
  bottom: 10px;
}
@media (max-width: 991px) {
  .user-profile-mobile h4.name-text {
    font-size: 18px;
    line-height: 21.6px;
    margin: 0;
  }
  .user-profile-mobile h5.roll {
    font-size: 12px;
    line-height: 16.8px;
    margin: 0;
  }
  .img-box-1 img.w-40 {
    width: 30px;
    height: 30px;
    margin-top: 4px;
  }
  .img-box-1 {
    margin-right: 7px;
  }
  .user-profile-mobile {
    width: 70%;
    margin-top: 7px;
  }
  .mobile-nati {
    width: 18%;
  }
  .m-top-add-sp {
    margin-top: 25px;
  }
  .side-menu-left {
    max-width: 100%;
  }
  .tab-scroll-view .left-bar {
    height: 406px;
  }
  .box-noti {
    width: 400px;
    right: 7px;
    box-shadow: unset;
  }
  .m-w-100 .candidate-box {
    width: 100% !important;
    max-width: 100% !important;
    box-shadow: unset;
  }
  .dropdown-menu.m-w-100 {
    width: 100%;
    min-width: 100%;
    top: 150px;
    border: unset;
  }
  .dropdown-menu.m-w-100 .can-list li {
    padding: 7px 20px;
  }
  .pog-none {
    position: unset !important;
  }
  .tab-flex-none {
    display: unset !important;
  }
}
@media (max-width: 767px) {
  .img-box-1 {
    margin-right: 7px;
  }
  .user-profile-mobile {
    width: 70%;
    margin-top: 7px;
  }
  .mobile-nati {
    width: 18%;
  }
  .m-top-add-sp {
    margin-top: 25px;
  }
  .side-menu-left {
    max-width: 100%;
  }
  .tab-scroll-view .left-bar {
    height: 406px;
  }
  .box-noti {
    right: 7px;
    box-shadow: unset;
  }
  .m-w-100 .candidate-box {
    width: 100% !important;
    max-width: 100% !important;
    box-shadow: unset;
  }
  .dropdown-menu.m-w-100 {
    width: 100%;
    min-width: 100%;
    top: 150px;
    border: unset;
  }
  .dropdown-menu.m-w-100 .can-list li {
    padding: 7px 20px;
  }
  .pog-none {
    position: unset !important;
  }
  .tab-flex-none {
    display: unset !important;
  }
  .box-noti {
    top: 148px;
    position: absolute;
    width: 100%;
  }
}
.f-form.form-get i {
  top: 40px !important;
}
.drop-new button {
  border: solid 1px #0055ba;
  border-radius: 4px;
  padding: 10px 16px;
  text-align: left;
  position: relative;
  height: 56px;
}
.drop-new ul.dropdown-menu.show {
  width: 100%;
  box-sizing: unset;
  border: 0;
}
.drop-new ul.dropdown-menu li a:hover {
  background-color: #fff;
}
.drop-new ul.dropdown-menu li {
  margin: 4px 0 10px;
}
.drop-new button,
.drop-new ul.dropdown-menu li a {
  font-size: 16px;
  color: #000;
}
.drop-new button i {
  position: absolute;
  right: 15px;
  top: 18px;
}
@media (min-width: 991px) {
  .dask-none {
    display: none !important;
  }
  ul.can-list li {
    margin-top: 10px;
  }
  ul.can-list li a i {
    margin-right: 8px;
  }
}
.contact_text {
  box-shadow: 0 0 10px #ccc;
  padding: 26px;
  border-radius: 10px;
  border: 2px solid #0055ba;
}
div#sector_input button {
  padding: 8px 10px;
}
div#image_admin_logo img {
  border-radius: inherit;
  border: 0;
  object-fit: contain;
  width: 100px !important;
  height: 100px !important;
}
div#stripe_data input.fild-des {
  padding-left: 50px !important;
}
.ql-container.ql-snow {
  height: 150px;
}
.ql-editor {
  min-height: 150px;
}
.sklls-select {
  height: 120px;
}
div#search-results .title_heading {
  font-size: 20px;
  color: #0055ba;
  font-weight: 700;
}
.company_jobs_search ul.list-unstyled {
  margin: 0;
  padding: 6px;
  overflow-y: auto;
}
.company_jobs_search {
  position: absolute;
  width: 100%;
  background: #fff;
  border: 1px solid #d3d3d3;
  border-radius: 6px;
}
@media (max-width: 767px) {
  .m-text-left {
    text-align: left;
  }
  .m-top-sp {
    margin-top: 10px;
  }
}
ul.careerfy-blog li {
  display: inline-block;
  padding-right: 20px;
}
ul.careerfy-blog li a {
  font-weight: 400;
  font-style: normal;
  color: #111;
  font-size: 16px;
  text-decoration: none;
}
ul.careerfy-blog li i {
  color: #0055ba;
}
.blog-profile h5 {
  font-size: 25px;
  font-weight: 600;
  color: #0056b9;
}
.blog-profile hr {
  opacity: 0.2;
}
.pro-file-name img,
.user-blog img {
  border-radius: 133px;
  margin-bottom: 10px;
}
.user-blog img {
  width: 115px;
  height: 115px;
  border: solid 3px #0056b9;
}
.blog-part {
  padding: 80px 0 16px;
}
.blog-part a {
  text-decoration: underline;
}
.blog-part-text {
  background: #fff;
  box-shadow: 0 0 13px 9px #dfdede85;
  border-radius: 10px;
  padding: 25px;
  border: solid 1px #e9e9e9;
  margin-bottom: 20px;
}
.share-post li {
  display: inline-block;
  padding: 0 5px;
}
.share-post li a {
  background: #0e5ebf;
  display: block;
  width: 35px;
  height: 35px;
  padding: 5px 0;
  color: #fff;
  transition: 0.5s;
}
.share-post li a:hover {
  background: #000;
}
.pages-banner {
  margin: 0;
  padding: 5%0;
  background: #0055ba;
  font-size: 40px;
  color: #fff;
  text-align: center;
}
ul.careerfy-blog {
  margin-bottom: 10px;
}
.blog-profile {
  text-align: center;
  background: #fff;
  box-shadow: 0 0 13px 9px #dfdede85;
  border-radius: 10px;
  padding: 25px;
  border: solid 1px #e9e9e9;
}
.post-by {
  color: #000;
  font-size: 18px;
  font-weight: 800;
  text-decoration: none;
}
.post-by a {
  text-decoration: none;
  color: #0056b9;
}
.date-blog,
.pro-file-name a {
  color: #444;
  font-size: 14px;
}
.pro-file-name img {
  width: 35px;
  height: 35px;
  margin-right: 6px;
}
.pro-file-name a {
  text-decoration: none;
}
.continue-link,
.title-blog-link a {
  text-decoration: none;
  font-weight: 600;
}
.continue-link {
  color: #0e5ebf;
  font-size: 12px;
  display: block;
  margin-top: 12px;
}
.title-blog-link a {
  color: #0055ba;
  font-size: 25px;
}
.title-blog-link {
  margin-bottom: 15px;
}
@media (max-width: 991px) {
  ul.careerfy-blog li a {
    font-size: 13px;
  }
  ul.careerfy-blog li {
    padding-right: 6px;
  }
  .blog-profile h5 {
    font-size: 20px;
  }
  li.share-post-tag-line {
    width: 100%;
  }
  .mobile-center {
    text-align: center !important;
  }
}
@media (max-width: 360px) {
  ul.careerfy-blog li a {
    font-size: 12px;
  }
}
ul.tab-btn-speak li {
  display: inline-block;
  margin-right: 0;
}
#pills-tab li button {
  background: var(--color_3);
  border-radius: 4px;
  padding: 12px 24px;
  margin: 0 8px;
  font-weight: var(--font_weight_500);
  font-size: var(--font_22);
  line-height: var(--font_22_line-height);
  color: var(--color_1) !important;
}
.mobile-view-slider {
  display: none;
}
@media (max-width: 767px) {
  .mobile-view-slider {
    display: block;
  }
}
.company_jobs_search .search_result_para {
  padding: 10px;
  border: 1px solid #d3d3d3;
  border-radius: 4px;
  margin: 10px 0;
  display: block;
}

.tab-card .col-3 img.w-48 {
  border-radius: 100px;
  width: 60px;
  height: 60px;
}
.tab-card h6.tab-name-user {
  margin-top: 5px;
}
form.form-get i {
  color: #2c2c2c !important;
}
.form_field_sec i.fa-regular.eye-icon.fa-eye-slash {
  transform: rotateY(179deg);
}
form.form-get span.text-danger {
  position: relative;
  bottom: 6px;
}
form.form-get .text-danger::before {
  top: 1px;
}
.close-x {
  border: solid 1px #ededed;
  border-radius: 100px;
  box-shadow:
    1px -1px 7px rgba(21, 21, 21, 0.12),
    0 1px 10px -3px rgba(21, 21, 21, 0.15),
    0 14px 20px -2px rgba(21, 21, 21, 0.16);
}
.form-create-acc .form-in i {
  top: 45px !important;
}
.form-design-num input.form-control {
  height: 57px;
  margin-top: 5px !important;
  border: 1px solid #bababa !important;
  width: 100%;
  color: #191919 !important;
}
.form-design-num input.form-control::placeholder {
  color: #191919 !important;
}
.react-tel-input .selected-flag {
  background: #fff;
  padding: 6px 8px !important;
  width: 50px !important;
}
.react-tel-input .form-control {
  padding-left: 58px !important;
}
.frontend_user_dropdown ul.dropdown-menu.show .candidate-box {
  width: 100%;
  padding: 0 !important;
}
.react-tel-input .selected-flag .arrow::after {
  content: '';
  width: 8px;
  background: #000;
  height: 1px;
  left: 0;
  position: absolute;
  transform: rotate(313deg);
  top: 1px;
}
.react-tel-input .selected-flag .arrow::before {
  content: '';
  width: 8px;
  background: #000;
  height: 1px;
  right: -3px;
  position: absolute;
  transform: rotate(45deg);
  top: 1px;
}
.flag {
  padding: 0 5px !important;
}
.react-tel-input .selected-flag .arrow.up {
  border-top: none;
  border-bottom: 0 solid #555 !important;
}
.react-tel-input .selected-flag .arrow {
  border-top: 0 solid #555 !important;
}
.react-tel-input .flag-dropdown {
  border-right: none !important;
  top: 5px;
}
.selected-flag:after {
  border-right: solid 1px #c1c4d6;
  content: '';
  position: relative;
  right: -42px;
  padding: 1px 0;
  top: 0;
}
.frontend_user_dropdown ul.dropdown-menu.show .candidate-box ul.can-list li a {
  padding: 6px 15px !important;
}
.frontend_user_dropdown ul.dropdown-menu.show {
  padding: 0;
  margin-top: 10px;
}
@media (max-width: 1780px) {
  .back-img-form.new-join {
    height: 92vh;
  }
}
@media (min-width: 1600px) {
  .looking-hire h3.mt-5 {
    margin-top: 125px !important;
  }
}
@media (max-width: 767px) {
  .mobile-sp-80 {
    padding: 80px 0 !important;
  }
  .find-bylocation,
  .looking-hire,
  .need-help {
    padding: 40px 0 !important;
  }
  .mobile-f-23 {
    font-size: 23px !important;
  }
  .tab-card h6.tab-name-user {
    margin-top: 8px;
  }
  .logo-ft {
    margin-bottom: 60px;
  }
  .filter.filter-sp {
    margin-bottom: 25px;
  }
  .dash-right h1 {
    margin-top: 30px;
  }
  .toast-footer h5 {
    font-size: 11px !important;
  }
  .toast-box.color-0055BA.code-job {
    margin-top: 18px;
    border: solid #eee 1px;
    box-shadow: 4px 4px 8px #c7c7c757;
    left: unset;
    right: 0;
    width: 95%;
  }
  .modal-body h2.f-31 {
    font-size: 25px !important;
  }
  .toast-box.c .code-job p a {
    font-size: 9px;
  }
  .m-pl-12 {
    padding-left: 12px;
  }
  .frontend_user_dropdown ul.dropdown-menu.show {
    width: 150px;
  }
  .work-experience-fieild .uploade-btn .btn-a.primary-size-16 {
    margin: 0 auto !important;
  }
  .dash-profile-img,
  .m-center .text-right,
  .uploade-btn {
    text-align: center;
  }
  .form-experience-fieild .uploade-btn {
    margin-bottom: 20px;
  }
}
span.icon-social-set {
  color: #fff;
  padding: 4px 7px;
  width: 24px;
  height: 24px;
  display: block;
  background: #424242;
  border-radius: 100px;
  font-size: 12px;
  margin: 0 0 0 15px;
  cursor: pointer;
}
p.tag-set-col {
  font-weight: var(--font_weight_500);
  line-height: var(--font_22_line-height);
  font-size: 14px;
  border-radius: 24px;
  padding: 8px 16px;
}
.f-12.mar-b-1 {
  margin-bottom: 4px !important;
}
.mar-top4 {
  margin-top: 30px;
}
.h-410 {
  height: 410px;
}
.pay-due table {
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}
.pay-due table caption {
  font-size: 1.5em;
  margin: 0.5em 0 0.75em;
}
.pay-due table tr {
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  padding: 0.35em;
}
.pay-due table td,
.pay-due table th {
  padding: 0.625em 0;
}
.pay-due table th {
  font-size: 0.85em;
}
@media screen and (max-width: 600px) {
  .pay-due table {
    border: 0;
  }
  .pay-due table caption {
    font-size: 1.3em;
  }
  .pay-due table thead {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
  .pay-due table tr {
    border-bottom: 3px solid #fff;
    display: block;
    margin-bottom: 0.625em;
  }
  .pay-due table td {
    border-bottom: 1px solid #fff;
    display: block;
    font-size: 0.8em;
    text-align: right;
  }
  .pay-due table td::before {
    content: attr(data-label);
    float: left;
    font-weight: 700;
    text-transform: uppercase;
  }
  .pay-due table td:last-child {
    border-bottom: 0;
  }
}
.w-15-par {
  width: 15%;
}
.w-60-par {
  width: 60%;
}
.w-25-par {
  width: 25%;
}
.w-30 {
  width: 30px;
}
.btn-outline-primary {
  color: #0055ba !important;
  border-color: #919191 !important;
}
.btn-outline-primary.active,
.btn-outline-primary:hover {
  background-color: #0055ba !important;
  color: #fff !important;
  border-color: #0055ba !important;
}
.pad-spac {
  padding: 8px 26px;
  border-radius: 12px;
}
.c-cc6666 {
  color: #c66 !important;
}
.pay-due .page-item.active .page-link {
  color: #333 !important;
  background-color: #ccc !important;
  border-color: #ccc !important;
}
.page-link {
  color: #333 !important;
  border: unset !important;
}
.pay-due .page-item {
  margin: 0 5px;
}
a.new-btn {
  color: #363;
  border: solid 1px #e3e3e3;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 700;
}
@media (max-width: 767px) {
  .m-text-left {
    text-align: left;
  }
  .m-top-sp {
    margin-top: 10px;
  }
}
button.accordion-button {
  border: 1px solid var(--color_15);
  padding: 18px 16px;
  border-radius: 4px !important;
  height: 35px;
}
.ready-to-in .accordion-item {
  background-color: unset !important;
  border: unset !important;
}
.tab-span-sa {
  border-radius: 4px;
  padding: 1px 4px;
}
.pricing-part ul.check-close {
  padding: 0 0 0 35px;
}
form .react-tel-input .form-control {
  height: 38px !important;
  width: 100% !important;
  margin-top: 0 !important;
}
input.form-control.fild-des-contact {
  border: 1px solid var(--color_15);
  border-radius: 4px;
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_14_line-height);
  letter-spacing: -0.5px;
  color: var(--color_10);
  width: 100%;
  height: 56px;
  background: var(--color_4);
  margin: 7px 0 15px;
}
div#scrolldata {
  height: 150px;
  overflow-y: auto;
  border: solid 1px #eee;
  padding: 20px;
  margin-bottom: 8px;
}
.choose-currency {
  padding: 0 5px;
  height: 35px;
  border-radius: 9px;
  border: solid 1px #0055ba;
  color: #000;
}
.skills_sections {
  width: 100%;
  overflow-x: scroll;
}
span.taglist-blogname {
  color: #0055ba;
  font-size: 25px;
  text-decoration: none;
  font-weight: 600;
}
.currency_dropdown,
.salary.m-0 {
  display: flex;
  align-items: center;
}
.currency_dropdown {
  border: 1px solid #bababa;
  padding: 2px;
  border-right: 0;
  border-radius: 6px 0 0 6px;
  height: 36px;
  width: 20%;
  justify-content: center;
}
input.fild-des.m-0 {
  outline: 0;
}
input.fild-des.m-0:focus-visible {
  outline: 1px solid var(--color_1) !important;
  border: 0 !important;
}
.blog-blue {
  background-image: url(../public/images/blue-back-blog.webp);
  background-repeat: no-repeat;
}
@media (min-width: 1800px) {
  .blog-blue {
    padding: 3%0;
  }
}
.blog-blue {
  background: url(../public/images/bg_blog_header.webp) no-repeat center center;
  background-size: cover;
  padding: 30px 0;
}
.blog-goes {
  padding: 40px 0;
}
.Rectangle-135 {
  border-radius: 15px;
}
.Speak-ex {
  padding: 8px 16px;
  border-radius: 24px;
}
.user-w-max {
  max-width: 290px;
}
.user-w-max img.w-48 {
  border-radius: 100px;
}
.Archivo {
  font-family: var(--archivo-font), sans-serif;
}
.icon-soc img {
  padding: 0 20px 0 0;
}
.f-45 {
  font-size: 45px !important;
  line-height: 54px !important;
}

.blogImage {
  height: auto;
  border-radius: 10px;
  width: 100%;
}
@media (min-width: 1800px) {
  .blog-blue {
    padding: 3%0;
  }
}
.blog-profile-2 {
  background: #cfe5ff;
  padding: 20px;
  background-image: url(../public/images/been-blog.png);
  background-size: cover;
  background-repeat: no-repeat;
}
a.cancel.cancel-modal,
label.upload-label,
span.copy-clipbpard,
span.teamm.f-12 {
  cursor: pointer;
}
#candidate_slug:hover {
  background-color: #0055ba;
  color: #ffff;
  border: 2px solid #0055ba;
}
.pro-diamond .diamond_icon_image {
  border: 2px solid var(--color_6) !important;
}
.modal.modal-part {
  background: #00000045 !important;
}
.dash-profile-img img {
  border-radius: 200px !important;
}
.frontend-form i.eye-icon {
  margin-top: 10px;
}
.close-b-des {
  border-radius: 0 2px 0 8px;
  font-size: 18px;
  border: 0;
}
.dropdown.down-btn .dropdown-toggle {
  text-align: left;
  margin: 4px 0;
  padding: 15px 5px;
}
.max-220 {
  max-width: 220px;
}
.img-hh {
  height: unset !important;
  border-radius: 12px;
}
.max-100-p {
  max-width: 100% !important;
}
.tab-popup ul#pills-tab li button.nav-link.active {
  color: #0070f5 !important;
}
ul.skills_sections.scr-x {
  overflow-x: unset;
  margin-bottom: 15px;
}
ul.skills_sections.scr-x li {
  margin: 0 8px 0 0;
}
.choose-currency.currencymm {
  border: solid 1px #bababa;
  font-size: 14px;
  color: #999;
  font-weight: 400;
  border-radius: 4px;
  height: 32px;
  padding: 3px 6px;
  margin: 7px 0 12px !important;
}
.tab-popup.callum #pills-tab li button {
  font-weight: var(--font_weight_400);
  font-size: var(--font_16);
  line-height: var(--font_16_line-height);
  background: #fff0 !important;
  padding: 13px 5px !important;
  border-radius: 0 !important;
  color: #bababa !important;
}
.same-pad-up-down.same-mm {
  padding-bottom: 0 !important;
  padding-top: 5% !important;
}
.b-r-8-p .modal-content {
  border-radius: 8px !important;
}
.tab-flex-none p.user-img img.w-40 {
  position: relative;
  top: -4px;
}
@media (max-width: 767px) {
  .pad-spac {
    padding: 5px 13px !important;
  }
  .form-in-3 input {
    margin: 0 0 12px;
  }
}
.dash-profile-img.m-auto.over-h-none.profile-img-set img {
  width: 196px;
  height: 196px;
  margin-top: -30px;
  margin-bottom: 15px;
}
.img-r-sp img {
  margin-right: 3px;
  margin-top: -3px;
}
.dropdown-item:hover {
  background-color: #ffffff00 !important;
}
.max-w-w .candidate-box {
  width: 200px !important;
  max-width: 240px !important;
}
.max-w-w .candidate-box li {
  padding: 5px 0;
}
.max-w-w .candidate-box ul.can-list {
  padding-bottom: 10px !important;
  padding-top: 10px !important;
}
.max-w-w .candidate-box li a:hover {
  color: #0055ba !important;
}
.modal-content {
  border-radius: 15px;
  border: unset;
  box-shadow: 0 2px 5px 0#33333387 !important;
}
.error.error-cs {
  margin-top: 4px;
}
.team_memebr {
  max-height: 200px;
  overflow-y: scroll;
  overflow-x: hidden;
}
.select__placeholder {
  padding-left: 8px;
}
.select__input-container.css-qbdosj-Input {
  height: 40px;
}
.basic-multi-select.css-b62m3t-container {
  margin-bottom: 10px;
}
.admin-tab-table .c-n img {
  width: 40px !important;
  height: 40px !important;
  border: solid 1px #cfe5ff80;
  border-radius: 2px !important;
  margin-right: 7px;
}
.admin-tab-table td a i:hover,
.tab-popup button#schedule_inter {
  color: #fff !important;
}
.max-210 {
  max-width: 210px;
  width: 100%;
}
.max-134 {
  max-width: 134px;
}
#profile.active .max-90 img.logo-filter,
.max-134 img.logo-filter {
  height: 134px !important;
  width: 134px !important;
  border-radius: 8px;
}
.max-260 {
  max-width: 260px;
}
.max-90-col {
  max-width: 90px;
}
.max-90-col img {
  width: 90px;
  height: 90px;
  border-radius: 4px;
}
@media (max-width: 767px) {
  .dash-profile-img.m-auto.over-h-none.profile-img-set img {
    width: 150px;
    height: 150px;
    margin-top: 20px;
  }
  .col.max-134 {
    margin: 0 auto;
  }
  div#home .sort-d-flex.mt-3 {
    justify-content: center;
    margin-bottom: 9px;
  }
}
@media (min-width: 1199px) {
  th.w-fist {
    width: 30%;
  }
  .side-menu-left {
    max-width: 225px;
  }
  .xx-w-120 {
    max-width: 120px;
  }
}
.chart_months_btn {
  color: #71717a;
  font-size: 18px;
  border: 0;
  background: 0 0;
  line-height: 50px;
}
.chart_months_btn_sec .active {
  border: 2px solid #c3d8f1;
  padding: 0 8px;
  border-radius: 10px;
  font-weight: 600;
  color: #000;
  background: #ebf4ff;
}
.w-150-col {
  max-width: 150px !important;
}
.max-268-col {
  max-width: 268px;
}
.same-w-150 {
  width: 100%;
  border-radius: 8px;
}
.close-none {
  display: none;
}
@media (max-width: 1199px) {
  .max-268-col a,
  .max-268-col button {
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .tab-w-col-12 {
    display: unset;
  }
  .w-150-col {
    max-width: 150px !important;
  }
  .max-268-col,
  .w-150-col {
    margin: 0 auto;
    padding: 0;
  }
  .m-center {
    text-align: center;
  }
  .sp-m-3 {
    margin: 20px 0;
  }
}
.error,
.text-danger {
  font-size: 12px;
}
.text-danger::before {
  width: 14px;
  height: 14px;
  padding: 0 !important;
}
.accordion-item:first-of-type .accordion-button.btn-acc {
  color: #999;
}
.salary-box input {
  margin-top: 5px;
}
.error-sp {
  margin-bottom: -15px;
}
.danger-sp {
  font-size: 18px;
  padding: 13px 17px;
  width: 100%;
}
.danger-sp:hover {
  background-color: #dc35451a !important;
  color: #000;
}
.pog {
  position: relative;
}
.error-m {
  margin-top: -15px;
}
.drop-choose {
  border: solid 1px #bababa;
  border-radius: 4px;
  padding: 0 4px;
  height: 40px;
}
ul.repot li img.fla,
ul.skills.pop-skils li i {
  margin-right: 2px;
}
.white-card.job-data .img-logos {
  background: #cfe5ff80;
  padding: 15px 10px;
  border-radius: 200px;
  width: 72px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.white-card.job-data .img-logos img {
  width: 60px;
  height: 40px;
  padding: 4px;
  border-radius: 0;
}
.line-sim::before {
  top: 0;
}
.select__input:focus-visible {
  outline: 0 !important;
}
.max-w-99 {
  max-width: 99px;
}
.max-w-99 img {
  border: solid 1px #eee;
  border-radius: 4px;
}
.b-r-16 {
  border-radius: 16px;
}
.stay-updated input.form-white-22-font:focus-visible {
  border: solid 2px #fff !important;
}
@media (min-width: 992px) {
  .plan-box {
    height: 650px;
  }
}
.work-experience-fieild h6.staff-member {
  font-family: var(--opensans-font);
  font-weight: var(--font_weight_700);
  font-size: var(--font_22);
  line-height: var(--font_18_line-height);
  color: var(--color_9);
}
a.arrow-left {
  padding-right: 10px;
  cursor: pointer;
}
select:focus-visible + label {
  color: var(--color_1) !important;
}
h5.unsave {
  font-size: 15px;
}
.step_label_css {
  display: block;
  position: relative;
  cursor: pointer;
}
:checked + .step_label_css:before {
  content: '✓';
  position: absolute;
  width: 50px;
  height: 50px;
  background: #0055ba;
  box-shadow: 0 0 9px #000;
  border-radius: 100%;
  color: #fff;
  font-size: 40px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 56px;
}
.rwd-table th,
.step_radio_css {
  display: none;
}
.step_label_css img {
  border: 2px solid #0055ba;
  border-radius: 10px;
  width: 100%;
}
.navbar-light .navbar-brand {
  width: 200px;
  object-fit: scale-down;
  height: 60px;
}
.logo-width img {
  height: 60px;
  object-fit: contain;
}
.fotgetpass a {
  float: right;
  text-decoration: underline;
}
.resume-wrapper {
  color: #4f4f4f;
}
.resume-name {
  font-size: 2.75rem;
  font-weight: 900;
  letter-spacing: 0.4rem;
  color: #54b689;
}
.resume-section-heading {
  position: relative;
  padding-left: 1rem;
  font-size: 24px;
  letter-spacing: 0.15rem;
  color: #54b689;
  font-weight: 800;
}
.resume-tagline {
  font-size: 28px;
  font-weight: 300;
}
.resume-main .item-title {
  font-size: 2rem;
}
.resume-section-heading:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 100%;
  background: #54b689;
  position: absolute;
  left: 0;
  top: 0;
}
#template_two .resume-header {
  background: #434e5e;
  color: rgba(255, 255, 255, 0.9);
  height: 220px;
}
#template_two .resume-timeline-item:before {
  content: '';
  display: inline-block;
  position: absolute;
  left: -32px;
  top: 3px;
  width: 15px;
  height: 15px;
  border: 4px solid #58677c;
  background: #fff;
  border-radius: 50%;
}
#template_two .resume-timeline:before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 100%;
  background: #aab4c3;
  left: 6px;
  top: 4px;
  position: absolute;
}
#template_two .resume-position-title {
  font-size: 1.125rem;
  color: #434e5e;
}
#template_two .resume-company-name {
  color: #58677c;
  font-size: 0.875rem;
  font-weight: 500;
}
#template_two .resume-section-title {
  font-size: 1.25rem;
  position: relative;
  color: #434e5e;
}
#template_two .resume-section-title:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1.5px;
  background: #8d9aad;
}
#template_two .resume-position-time {
  font-size: 0.875rem;
  color: #aab4c3;
}
#template_two .resume-timeline {
  padding-left: 2rem;
}
#template_two .resume-section-content {
  color: #58677c;
}
.template-one {
  border: 2px solid #54b689;
  border-radius: 5px;
}
a#hiring:hover,
a#verify1:hover {
  color: unset;
}
ul.check-close li i.set-check {
  width: 18px;
  height: 18px;
  background: var(--background-color_3);
  border-radius: 100px;
  text-align: center;
  padding: 0;
  color: var(--color_1);
  position: absolute;
  left: -24px;
}
ul.check-close.set-check-close li {
  margin: 8px !important;
}
ul.check-close li i.set-check::before {
  font-size: 9px;
}
ul.check-close li i.set-xmark {
  width: 19px;
  height: 19px;
  text-align: center;
  padding: 2px 0;
  position: absolute;
  left: -24px;
}
ul.check-close li i.set-xmark::before {
  font-size: 14px;
}
.view-resume-employee {
  padding: 11px 5px;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  text-align: center;
  font-size: var(--font_12);
  cursor: pointer;
  color: #0c5a14;
  background: #dcf2ea;
  border: 1px solid #3d9f79;
}
.filed_disabled {
  background: #f1f1f1 !important;
}
label#check-labelId {
  padding: 9px 4px !important;
}
div#upload-file1 {
  cursor: pointer !important;
}
.resume-gen,
p.resume-p {
  display: flex;
}
.notfication_name,
.notfication_name_two {
  color: #a9a9a9;
  font-weight: 700;
  background: #edeff5;
}
.notfication_name {
  padding: 4px;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.notfication_name_two {
  font-size: 16px;
  border-radius: 54px;
  text-align: center;
  width: 32px !important;
  height: 32px !important;
  margin: 5px 0 0 !important;
  padding: 5px 0;
}
span.message1 {
  font-weight: 400;
}
span.message2 {
  font-weight: 800 !important;
}
img.dummy-img {
  border: unset !important;
  border-radius: unset !important;
}
a.edit-img {
  top: 88px;
  right: 10px !important;
}
select.choose-currency.mb-4.job-search {
  width: 100%;
  border: 1px solid var(--color_15);
}
.code-job1 {
  position: fixed;
  top: 85px;
  right: 34px;
  width: 29%;
  z-index: 8;
}
.toast-footer h5.msg-toastr {
  font-weight: 500;
}
.form-search-home input::placeholder {
  color: #5c5c5c !important;
}
.hover-eff:focus:before {
  border-radius: 0 !important;
}
.frontend-form .mar-top-4 {
  margin-top: 4px !important;
}
select {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background: url(/images/arrow-down.png) no-repeat right center #fff !important;
}
.banner-part-home .form-pages p.text-danger {
  position: relative;
  bottom: 17px;
}
.lock-img {
  margin: -5px 14px 0 0;
}
.dropdown-style li:hover {
  background: #f7f7f7;
}
.dropdown-style li {
  list-style: none;
  margin: 0;
  background: #fff;
  color: #000;
  padding: 10px 13px;
  border-bottom: solid 1px #cdcdcd;
}
ul.dropdown-style {
  background: #eee;
  padding: 8px;
  height: 250px;
  overflow-y: scroll;
}
.dropdown-style::-webkit-scrollbar {
  width: 4px;
}
.dropdown-style::-webkit-scrollbar-track {
  background: #fff;
}
.dropdown-style::-webkit-scrollbar-thumb {
  background: #111;
}
.dropdown-style::-webkit-scrollbar-thumb:hover {
  background: #fff;
}
.border-none {
  border: 0 !important;
}
.border-gray {
  border: solid 1px #bababa !important;
}
.h-35 {
  height: 35px !important;
}
.h-140 {
  height: 140px;
}
.pro img.w-25 {
  margin-left: 5px;
  position: relative;
  bottom: 2px;
}
.bottom-0 {
  bottom: 0 !important;
}
.dash-profile-img .dummy-img {
  border: 1px solid var(--color_3) !important;
  height: 120px;
  border-radius: 100px !important;
}
.mar-bot-55 {
  margin-bottom: 55px;
}
@media (max-width: 1500px) {
  button.btn-bg-3D9F79 img.w-16 {
    width: 16px !important;
    height: 16px !important;
  }
}
@media (max-width: 767px) {
  .mobile-view-slider img {
    width: 95% !important;
  }
  .mobile-view-slider .slick-arrow {
    display: none !important;
  }
  .form-search-home .form-control {
    font-size: var(--font_13);
    line-height: var(--font_13_line-height);
  }
  .form-search-home .primary-size-16 {
    font-weight: 600;
  }
  .m-m-auto {
    margin: 0 auto !important;
  }
  .job_found {
    display: block !important;
    border: 1px solid #bababac2;
    padding: 5px 12px !important;
    border-radius: 5px;
  }
  a.jobsearch_popup i.fa-solid.fa-xmark.xmark-icon {
    color: #0d6efd;
    float: right;
    font-size: 17px;
  }
  .form-search-home {
    background-color: #0055ba;
    background-image: none;
  }
}
.flg-part p {
  margin-top: 15px;
}
.z-vel {
  z-index: unset;
}
.all-recent {
  width: fit-content;
  padding-right: 40px;
}
.blog-profile-2.fixed-section {
  position: sticky;
}
button.free1 {
  border: unset;
  background: 0 0;
}
span.notification_font_weight {
  font-weight: 700 !important;
}
.z-value2 {
  position: relative;
  z-index: 90;
}
.phone-field-2 .flag-dropdown {
  height: 91%;
  margin-top: 5px;
  border: 0;
  left: 0;
}
html {
  scroll-behavior: smooth;
}
::-webkit-scrollbar {
  width: 7px;
}
::-webkit-scrollbar-thumb {
  background: #00000080;
  border-radius: 30px;
}
.dash-profile-img img.Profile_Picture {
  border: unset !important;
}
.image-user-fix img.same-w-150 {
  height: 140px;
}
@media (min-width: 1024px) {
  .w-col-noti {
    width: 50px;
    max-width: 50px;
  }
}
@media (min-width: 1600px) {
  .max-button-auto {
    max-width: 240px;
    margin: 0 auto;
  }
}
p#headerdropdownMenuButton1 img.w-40 {
  position: relative;
  bottom: 4px;
}
.dash-profile-img .border-none,
.left-bar .modal-dialog img.cemra {
  border: 0 !important;
}
@media (max-width: 1460px) {
  .left-bar .dash-profile-img {
    margin-bottom: 10px !important;
  }
  .left-bar ul.side-menu-left {
    margin-top: 12px !important;
  }
  .left-bar ul.side-menu-left li {
    margin-bottom: 5px !important;
  }
  .left-bar .text-center.mt-4 {
    margin-top: 10px !important;
  }
}
@media (max-width: 991px) {
  img.resume-design-img {
    margin-bottom: 15px;
  }
  img.find-img {
    width: unset !important;
    height: unset !important;
  }
}
@media (max-width: 767px) {
  .m-w-100 {
    width: 100%;
  }
  .left-bar .modal-dialog {
    margin-top: 80px;
  }
  .m-text-left {
    text-align: left !important;
  }
}
.description-des p,
.open-sans {
  font-family: var(--opensans-font) !important;
}
.description-des p {
  font-size: 16px;
  color: #4d4d4d;
  line-height: 22.4px;
}
.description-des p strong {
  font-weight: 600 !important;
  color: #4d4d4d !important;
}
.description-des p span {
  color: #4d4d4d !important;
}
.bg-ebf4ff {
  background: #ebf4ff !important;
}
.h-90vh {
  height: 90vh;
}
@media (min-width: 1700px) {
  .h-90vh {
    height: 92vh;
  }
}
@media (max-width: 767px) {
  .m-text-right {
    text-align: right !important;
  }
  .name-user {
    font-size: 22px;
  }
  .name-work {
    font-size: 16px;
  }
  .m-d-b {
    display: block;
  }
  .m-w-47 {
    width: 47%;
  }
  .m-d-flex {
    display: flex;
    flex-wrap: wrap;
  }
  .edit-pi {
    position: relative;
    z-index: 3;
  }
  .img-size-profile img.profile-candidate {
    width: 80px !important;
    height: 80px !important;
    margin-top: -30px !important;
  }
  .m-text-center {
    text-align: center;
  }
  .colum-r {
    display: flex;
    flex-direction: row-reverse;
  }
  .m-w-60 {
    width: 60% !important;
  }
  .head-part .close-x {
    padding: 8px 10px;
    font-size: 16px;
    text-align: center;
  }
  .uploade-btn.m-w-100 {
    width: 100% !important;
  }
  .upload-text-m-13 {
    font-size: 13px;
  }
}
@media (max-width: 385px) {
  ul.over-tab li a {
    font-size: 12px !important;
    padding: 8px 9px !important;
  }
  .m-w-60 {
    width: 80% !important;
  }
}
.img-w-short {
  width: 80px;
  height: 80px;
  border-radius: 200px;
  border: solid 1px #a5b6c9;
}
a.edit-icon-bg-white {
  background: #fff;
  padding: 2px 4px;
  border-radius: 5px;
  position: absolute;
  right: 6px;
  top: 5px;
  color: #0055ba;
  font-size: 12px;
}
.share-icon-img {
  position: absolute;
  right: 6px;
  top: 8px;
}
p.mb-1 {
  margin-bottom: 5px !important;
}
p.message-13 {
  color: #bababa !important;
  font-size: 13px;
  font-weight: 400;
}
.f-13,
ul.repot li {
  font-size: 13px;
  line-height: 15.6px;
}
.card-filter {
  border: 1px solid #cfe5ff;
  background: #ecf4ff;
  padding: 6px;
  border-radius: 8px;
}
ul.repot li {
  list-style: none;
  display: inline-block;
  margin: 0 0 0 10px;
  color: var(--color_19) !important;
}
.view-jobs {
  border: 2px solid #0055ba;
  background: #0055ba14;
  font-size: 16px;
  line-height: 19.2px;
  font-weight: 600;
  color: #0055ba;
  padding: 12px 0;
  width: 100%;
  border-radius: 4px;
}
.user-short {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  margin-right: 8px;
}
.view-jobs .fa-paperclip {
  transform: rotate(311deg);
}
.b-r-4 {
  border-radius: 4px;
}
.textarea-filter textarea {
  border: 1px solid #bababa;
  border-radius: 4px;
  background: #bababa;
  padding: 12px 10px;
  width: 100%;
  height: 80px;
  color: #999;
  font-size: 14px;
  font-weight: 400;
}
.textarea-filter {
  border: 1px solid #d9d9d9;
  padding: 8px;
  border-radius: 8px;
}
.textarea-filter textarea::placeholder {
  color: #999;
}
.candidate-name2 {
  background: #fff;
  border-radius: 8px;
  padding: 8px;
  border: 1px solid #d9d9d9;
}
.dubai li img {
  width: 16px;
}
.dubai li {
  list-style: none;
  display: inline-block;
  margin: 0 10px 0 0;
  font-size: 13px;
  line-height: 15.6px;
  color: #999;
}
.seen-by {
  margin-top: 10px;
  border: 1px solid #00752f;
  background: #198754;
  color: #ffff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  line-height: 14.4px;
  padding: 4px 16px;
  width: fit-content;
}
.pad-sp {
  padding: 0 10px !important;
}
.c-999999 {
  color: #999;
}
.name-title-jobs {
  font-size: 11.75px;
  line-height: 14.1px;
}
.f-view {
  font-size: 23px;
}
.f-view span {
  font-size: 20px;
}
@media (max-width: 765px) {
  .btn-font-short .btn-a.primary-size-16 {
    font-size: 13px;
    padding: 10px 8px;
  }
  .btn-font-short ul.blue-text-line {
    margin-top: 4px !important;
  }
  .m-pb-3 {
    padding-bottom: 30px;
  }
}
.about-new {
  background: #0055ba;
}
.about-new h1 {
  color: #fff;
  font-size: 54px;
  line-height: 64.8px;
  font-weight: 700;
  word-break: break-all;
}
.span-yellow {
  color: #fdca40;
}
.about-new p,
.something-part h3 {
  color: #cfe5ff;
  font-size: 26px;
  line-height: 31.2px;
  font-weight: 500;
}
.for-employers,
.for-job {
  font-size: 22px;
  line-height: 26.4px;
  font-weight: 700;
  border-radius: 2px;
  padding: 16px 32px;
  border: 2px solid #fff;
  margin-right: 10px;
  transition: 0.6s;
}
.for-job {
  color: #0055ba;
  background: #fff;
}
.for-job:hover {
  background: #0055ba !important;
  color: #fff !important;
}
.for-employers {
  color: #fff;
  background: #0055ba;
}
.for-employers:hover {
  background: #fff !important;
  color: #0055ba !important;
}
.our-story {
  background: #002a5c;
  padding: 72px 0 40px;
}
.our-story h2 {
  color: #fff;
  line-height: 54px;
  font-size: 45px;
  font-weight: 700;
}
.our-story p,
.something-part p {
  font-size: 18px;
  line-height: 21.6px;
}
.our-story p {
  color: #fff;
  margin: 0;
}
.something-part {
  padding: 80px 0;
}
.primary-blue,
.something-part h2 {
  line-height: 54px;
  font-size: 45px;
  font-weight: 700;
  color: #0055ba;
  margin-top: 20px;
}
.something-part h3 {
  color: #2c2c2c;
}
.something-part p {
  color: #4d4d4d;
}
.primary-blue {
  font-size: 22px;
  line-height: 26.4px;
  color: #fff;
  border-radius: 2px;
  padding: 16px 32px;
  border: 2px solid #0055ba;
  margin-right: 10px;
  background: #0055ba;
  margin-top: 15px;
  transition: 0.6s;
}
.primary-blue:hover {
  background: #fff;
  color: #0055ba;
}
@media (max-width: 767px) {
  .about-new h1 {
    font-size: 35px;
    line-height: 54px;
  }
  .about-new h1 br {
    display: none;
  }
  .about-new p {
    font-size: 23px;
    line-height: 27.6px;
  }
  .for-employers,
  .for-job {
    font-size: 19px;
    line-height: 22.8px;
    width: 100%;
    margin-bottom: 15px;
  }
  .our-story h2 {
    line-height: 38.4px;
    font-size: 32px;
  }
  .our-story p,
  .something-part p {
    font-size: 16px;
    line-height: 19.2px;
  }
  .our-story {
    padding: 56px 0 40px;
  }
  .m-center {
    text-align: center;
  }
  .something-part h2 {
    line-height: 39.6px;
    font-size: 32px;
  }
  .something-part h3 {
    line-height: 28.8px;
    font-size: 24px;
  }
  .primary-blue {
    font-size: 19px;
    line-height: 22.8px;
    margin-top: 5px;
  }
  .reverse {
    display: flex;
    flex-direction: column-reverse;
  }
  .m-top-border {
    border-top: 1px solid #d6d6d6;
  }
}
.eye-resume {
  width: 24px;
  height: 25px;
  padding: 7px 0;
}
.pro {
  font-family: var(--opensans-font) !important;
}
button.pro img.w-25 {
  width: 18px !important;
  height: 18px !important;
}
.sp-right-1 {
  margin-right: 4px;
}
.salary-box .form-in {
  box-shadow: 0 4px 8px 0#051b4414;
}
.z-value-9 {
  z-index: 9;
}
@media (max-width: 767px) {
  .company_logo-input-fild {
    top: 0 !important;
  }
}
@media (min-width: 1500px) {
  .col-img {
    max-width: 120px !important;
  }
}
/* body,
html {
  overflow-x: hidden !important;
} */
@media (max-width: 767px) {
  img {
    max-width: 100%;
  }
  #id-add-class .nav {
    display: block;
  }
  #id-add-class li.nav-item {
    margin-bottom: 10px;
  }
  #id-add-class #pills-tab li button {
    margin: 0 auto;
  }
  .flg-part p {
    margin-top: 0 !important;
  }
}
input.fild-des.filed_disabled.addClass {
  margin-top: -2px;
}
.rwd-table {
  margin: auto;
  min-width: 300px;
  max-width: 100%;
  border-collapse: collapse;
  width: 100%;
  color: #333;
  border-radius: 0.4em;
}
.rwd-table th {
  border-top: none;
  background: var(--color_38);
  color: var(--color_4);
}
.rwd-table tr {
  border-top: 1px solid var(--color_39);
  border-bottom: 1px solid var(--color_39);
  background-color: var(--color_4);
  border-color: #bfbfbf;
}
.rwd-table td {
  display: block;
}
.rwd-table td:first-child {
  margin-top: 0.5em;
}
.rwd-table td:last-child {
  margin-bottom: 0.5em;
}
.rwd-table td:before {
  content: attr(data-th) ': ';
  font-weight: 700;
  width: 160px;
  display: inline-block;
  color: var(--color_47);
}
.rwd-table td,
.rwd-table th {
  text-align: left;
  padding: 0.5em 1em;
}
@media screen and (max-width: 601px) {
  .rwd-table tr:nth-child(2) {
    border-top: none;
  }
}
@media screen and (min-width: 600px) {
  .rwd-table tr:hover:not(:first-child) {
    background-color: #d8e7f3;
  }
  .rwd-table td:before {
    display: none;
  }
  .rwd-table td,
  .rwd-table th {
    display: table-cell;
    padding: 1em !important;
  }
  .rwd-table td:first-child,
  .rwd-table th:first-child {
    padding-left: 0;
  }
  .rwd-table td:last-child,
  .rwd-table th:last-child {
    padding-right: 0;
  }
}
.slider-cv {
  overflow: hidden;
}
.slider-cv img {
  width: 100%;
  height: auto;
}
.hoverChange {
  border-radius: 2px;
  color: var(--Primary-Absolute-Zero, #0055ba);
  font-size: 22px;
  font-style: normal;
  font-weight: 700;
  line-height: 26px;
  padding: 16px 32px;
  background: #fff;
  box-shadow:
    0 4px 12px 0 rgba(21, 21, 21, 0.12),
    0 2px 5px 0 rgba(21, 21, 21, 0.1),
    0 1px 3px 0 rgba(21, 21, 21, 0.12);
}
.bannerChange {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-repeat: no-repeat;
  border-radius: 16px;
  background-size: cover;
  background-position: top;
  background-image: url(/images/home/<USER>
  width: 100%;
  height: 100%;
  padding: 40px;
  background-color: #0055ba;
  position: relative;
}
.bannerText {
  color: #fff;
  font-size: 45px;
  font-style: normal;
  font-weight: 700;
  line-height: 54px;
}
.hoverChange.hoverChange1 {
  background: 0 0;
  color: #fff;
  border: 2px solid #fff;
}
.hoverChange.hoverChange1:hover {
  border: 2px solid #fff;
  border-color: #fff !important;
}
.bannerText
  button.ant-btn.css-dev-only-do-not-override-dho1id.ant-btn-default.btn-outline-light.hoverChange1.btn.text-light {
  font-size: 22px;
  font-weight: 700;
  line-height: 26px;
  letter-spacing: 0;
  text-align: center;
}
.addCoverWidth {
  max-width: 200px;
}
@media (max-width: 767px) {
  .bannerChange {
    padding: 0;
    font-size: 19px;
    font-weight: 500;
    padding: 40px 12px 24px 12px;
    justify-content: center;
    border-radius: 16px;
    background-image: url(/images/home/<USER>
  }
  .addCoverWidth {
    max-width: 100%;
  }
  .hoverChange,
  .hoverChange1 {
    width: 100%;
    margin-bottom: 20px;
  }
  .bannerText {
    color: #fff;
    font-size: 40px;
  }
  .hoverChange {
    font-size: 19px;
  }
}
.add-company-company-list-dropdown {
  top: 60px;
  z-index: 9;
}
ul.dropdown-style.add-exist-company-dropdown {
  position: absolute;
  width: 100%;
  top: 58px;
  z-index: 9;
  height: unset;
  padding: 0;
}
#sector_input .input-group {
  width: 68%;
}
.status_filter_tab_section button.active {
  color: #216dbf;
  border-color: #216dbf;
}
.ant-picker-dropdown.css-17cb29s.ant-picker-dropdown-placement-bottomLeft,
.ant-picker-dropdown.css-17cb29s.ant-picker-dropdown-placement-topLeft,
.ant-picker-dropdown.css-dev-only-do-not-override-17cb29s.ant-picker-dropdown-placement-bottomLeft,
.ant-picker-dropdown.css-dev-only-do-not-override-17cb29s.ant-picker-dropdown-placement-topLeft,
.ant-select-dropdown.css-17cb29s.ant-select-dropdown-placement-bottomLeft,
.ant-select-dropdown.css-17cb29s.ant-select-dropdown-placement-topLeft,
.ant-select-dropdown.css-dev-only-do-not-override-17cb29s.ant-select-dropdown-placement-bottomLeft,
.ant-select-dropdown.css-dev-only-do-not-override-17cb29s.ant-select-dropdown-placement-topLeft {
  z-index: ********* !important;
}
.sp-30 {
  padding: 30px 0;
}
@media (min-width: 1600px) {
  .homepage_banner_sec .home_banner_img {
    width: 500px;
    height: 350px;
  }
  .homepage_banner_sec {
    padding-top: 50px !important;
    padding-bottom: 50px !important;
  }
  .form-search-home {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 50px 0 !important;
  }
}
.blog-description-content img {
  width: 100%;
}
.blog_sidebar_dream_job_box_sec,
.blog_sidebar_looking_box_sec {
  position: relative !important;
  background-image: none !important;
  background: 0 0;
  padding: 0;
  margin-bottom: 15px;
}
.blog_sidebar_box_content_sec.overlay_2 {
  inset: 0;
}
.blog_sidebar_box_content_sec.overlay {
  position: absolute;
  background: rgba(4, 25, 64, 0.7);
  padding: 16px;
  border-radius: 5%;
}
.blog_sidebar_box_content_sec.overlay_dream_job {
  position: absolute;
  background: rgba(0, 112, 245, 0.7);
  padding: 16px;
  border-radius: 5%;
}
.blog_looking_to_hire_head {
  font-size: 22px !important;
  line-height: 26px;
  color: #fff;
  font-weight: 600 !important;
  margin: 0 0 8px;
}
.blog_dream_job_to_hire_sub_head,
.blog_looking_to_hire_sub_head {
  font-size: 18px;
  font-family: var(--archivo-font);
  font-weight: 600;
  line-height: 28px;
  color: #ebf4ff;
  padding: 0 0 14px;
}
.blog_dream_job_to_hire_sub_head {
  /* padding-bottom: 50px; */
  padding-bottom: 0px;
}
.blog_sidebar_section {
  position: fixed !important;
  /* overflow-y: scroll; */
  top: 10px;
  width: 306px;
}
.banner-img {
  height: auto;
  width: 100%;
}
.accordion-nav .accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230c63e4'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transform: rotate(-180deg);
}
.accordion-nav .accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transform: rotate(-180deg);
}
.blog-top {
  padding: 25px 0;
  background-color: #0055ba;
}
nav.breadcrumb li a {
  font-size: 13px;
  font-weight: 600;
  line-height: 18.2px;
}
.breadcrumb li a span {
  color: #cfe5ff;
}
.breadcrumb li span {
  color: #ffffff;
}
.blog-top .breadcrumb {
  margin-top: 0;
  margin-bottom: 24px;
}
.blog-title h1 {
  font-size: 48px;
  line-height: 120%;
  color: #fff;
  font-weight: 700;
}
.blog-author-card .name {
  color: #ebf4ff;
  font-size: 18px;
  line-height: 160%;
  font-weight: 600;
}
.blog-author-card small {
  color: #ebf4ff;
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
}
.apply-btn {
  margin-top: 10px;
  border-radius: 16px;
}

/* actions button css*/
button.action_btn {
  background: transparent;
  color: #0070f5;
  padding: 0;
  border: none;
  font-weight: 700;
}

button.action_btn:after {
  display: none;
}

button.action_btn i.fa-solid {
  margin: 0;
  padding: 0;
}

.blog-label {
  position: relative;
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  height: 32px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  display: block;
  margin-top: 5px;
}
.blog-select {
  width: 100%;
}
.blog-form {
}
.error-field {
  margin-top: 2px;
  color: #ff4d4f;
}

.blog-label::before {
  content: '*  ';
  color: #ff4d4f;
}

/*  */
.blog-details {
  padding-top: 80px;
}
.related-resources {
  margin-top: 80px;
}
.related-resources .list-tags {
  margin: 32px 0 16px;
}
.related-resources .list-tags li {
  margin: 0 5px 0 0;
  padding: 8px 16px;
  font-size: 12px;
  line-height: 16px;
  font-weight: 700;
}
.tab-card-box.related-resource-block h4 {
  font-size: 22px;
  font-weight: 500;
  line-height: 26.4px;
}
.tab-card-box.related-resource-block .blog-author-card .name {
  color: #2c2c2c;
}
.tab-card-box.related-resource-block .blog-author-card small {
  color: #737474;
}
.sidebar-title {
  font-size: 26px !important;
  line-height: 31px !important;
  margin: 0 0 8px;
  color: #fff;
  padding: 0;
}
.apply-ready-block {
  width: 100%;
  padding: 24px;
  background: linear-gradient(180deg, #f3f3f3 50%, #e0e0e0 100%);
  margin: 16px 0 0 0;
  position: relative;
  z-index: 1;
  border-radius: 16px;
}
.apply-ready-block::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60%;
  height: 100%;
  background: url(../public/images/apply-ready-img.png) bottom right no-repeat;
  z-index: -1;
  background-size: contain;
}

.apply-ready-block .btn-a {
  border-radius: 16px;
  font-weight: 500;
}
.apply-ready-block p.h3 {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: #0055ba;
  margin: 0 0 8px;
  max-width: 360px;
}
.apply-ready-block p.h4 {
  font-family: var(--archivo-font);
  font-size: 24px;
  font-weight: 700;
  line-height: 28.8px;
  color: #2c2c2c;
  max-width: 360px;
  margin: 0 0 24px;
}
.all-post-tab {
  margin-top: 58px;
}
.blog-share-sec p.h4 {
  color: #1f1f1f;
  margin: 0 0 16px;
}
.blog-main-content h2 {
  font-size: 45px;
  font-weight: 500;
  line-height: 54px;
  margin: 0 0 16px;
  color: #151515;
}
.blog-main-content h3 {
  font-size: 37px;
  font-weight: 500;
  line-height: 44.4px;
  margin: 0 0 16px;
}
.blog-main-content p {
  margin: 0 0 25px;
}
.blog-main-content h5 {
  font-size: 26px;
  font-weight: 700;
  line-height: 31.2px;
  color: #151515;
  margin: 0 0 16px;
}
.create-profile-sec {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 32px;
  background: #002a5c;
  box-shadow: 0px 2px 8px 0px #00000026;
  border-radius: 16px;
}
.create-profile-sec p.h3 {
  font-size: 28px;
  font-weight: 700;
  line-height: 39.2px;
  letter-spacing: 0.6000000238418579px;
  color: #ffffff;
  margin: 0 0 8px;
}
.create-profile-sec p {
  font-size: 16px;
  font-weight: 400;
  line-height: 22.4px;
  color: #ebf4ff;
}
.cps-btn {
  font-size: 18px;
  font-weight: 500;
  line-height: 21.6px;
  color: #151515;
  background: #fdca40;
  border-radius: 20px;
  padding: 12px 24px;
  white-space: nowrap;
}

@media (max-width: 1399px) {
  .right-side p {
    font-size: 14px;
    line-height: 24px;
  }
  .blog-main-content p {
    margin: 0 0 15px;
  }
  .know-your-btn {
    font-size: 13px;
    padding: 7px 15px;
  }
  .blog-main-content h2 {
    font-size: 2rem;
  }
  .cps-btn {
    font-size: 14px;
  }
  .know-your {
    line-height: 1.5;
  }
  .blog_dream_job_to_hire_sub_head,
  .blog_looking_to_hire_sub_head {
    font-size: 14px;
    line-height: 1.5;
  }
  .know-your-para {
    font-size: 11px;
  }
  .apply-ready-block .btn-a {
    padding: 7px 15px;
    line-height: 1;
    font-size: 14px;
  }
}

@media (max-width: 1199px) {
  .know-your-btn {
    font-size: 12px;
    padding: 7px 5px;
  }
  .blog-main-content h2 {
    font-size: 1.8rem;
    line-height: 1.5;
  }
  .blog-outer-part .middle-part .blog-main-content .expand-job h5 {
    font-size: 1rem;
  }
  .blog-outer-part .middle-part .blog-main-content h3 {
    font-size: 1.5rem;
  }
  .blog-author-card .name {
    font-size: 16px;
  }
  .blog-author-card small {
    font-size: 14px;
  }
  .cps-btn {
    font-size: 12px;
    padding: 8px 10px;
  }
  .create-profile-sec {
    padding: 25px 15px;
  }
  .border-primary-size-16,
  .primary-size-16,
  .text-primary-size-16 {
    padding: 10px 16px;
  }
  .f-45 {
    font-size: 2rem !important;
    line-height: 1.5 !important;
  }
  .related-resources {
    margin-top: 30px;
  }
  .tab-card-box.related-resource-block h4 {
    font-size: 1rem;
    line-height: 1.3;
  }
  .related-resources .list-tags li {
    padding: 5px 12px;
    font-size: 11px;
  }
  .related-resources .list-tags {
    margin: 20px 0 12px;
  }
  .right-side p {
    font-size: 13px;
    line-height: 1.5;
  }
}

@media (max-width: 1024px) {
  .logo-head {
    width: 150px;
  }
}

@media (max-width: 991px) {
  .blog-author-card small {
    font-size: 16px;
  }
}

@media (min-width: 1024px) and (max-width: 1199px) {
  .head-part #navbarSupportedContent .single-menu-space {
    padding: 20px 10px !important;
  }
  .login,
  .signup {
    height: 36px !important;
    padding: 10px 15px !important;
    font-size: var(--font_14) !important;
    line-height: 1 !important;
  }
  .logo-head {
    width: 150px;
  }
  .head-part ul.navbar-nav li a {
    font-size: var(--font_14);
    line-height: var(--font_14_line-height);
  }
}

@media (min-width: 771px) and (max-width: 1024px) {
  .head-part #navbarSupportedContent .single-menu-space {
    padding: 20px 10px !important;
  }
  .login,
  .signup {
    height: 36px !important;
    padding: 10px 15px !important;
    font-size: var(--font_14) !important;
    line-height: 1 !important;
  }
}

@media (max-width: 767px) {
  .apply-ready-block::after {
    display: none;
  }
  .mobile-banner {
    margin-left: -24px;
    margin-right: -24px;
    position: relative;
    bottom: -24px;
    width: auto;
  }

  .mobile-banner img {
    width: 100%;
    height: auto;
  }

  .apply-ready-block .btn-a {
    padding: 12px 24px;
    line-height: 19.2px;
    font-size: 16px;
  }
  .all-post-tab {
    margin-top: 30px;
    width: 100%;
    justify-content: center;
  }
  .blog-share-sec p.h4 {
    text-align: center;
    margin-top: 30px !important;
  }
  .blog-outer-part .middle-part p.icon-soc {
    text-align: center;
  }
  .blog_dream_job_to_hire_sub_head,
  .blog_looking_to_hire_sub_head {
    text-align: center;
    font-size: 16px;
    line-height: 25.6px;
  }
  .footer-part {
    background-image: unset;
  }

  .footer-part .top-culumn {
    margin-bottom: 20px;
  }

  .footer-part .logo-width {
    margin-bottom: 40px;
  }

  .footer-part .logo-width img {
    min-width: 203px;
    height: auto;
  }

  .ft-list {
    display: flex;
    flex-flow: row wrap;
    margin-top: 15px;
  }

  .ft-list li {
    margin-bottom: 15px;
    margin-right: 20px;
  }

  .ft-list li a {
    line-height: 22.4px;
    font-weight: 400;
  }
}
