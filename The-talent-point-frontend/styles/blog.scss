.breadcrumb {
  margin-top: 20px;
  ol {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;

    li {
      font-weight: 600;
      font-size: 13px;
      color: #bababa;
      display: flex;

      &:not(:last-child):after {
        content: '/';
        display: inline-block;
        margin: 0 5px;
      }

      &:last-child {
        color: #0070f5;
      }
    }
  }
}

.list-tags {
  display: flex;
  margin: 20px 0 20px 0;
  padding: 0;
  list-style: none;
  color: var(--primary-color);
  flex-wrap: wrap;

  li {
    font-weight: 600;
    background: #cfe5ff;
    border-radius: 24px;
    padding: 8px 20px;
    font-size: 13px;
    margin: 4px 5px 4px 0;
    text-transform: capitalize;
  }
}

.blog-author-card {
  display: flex;
  margin-top: 32px;
  align-items: center;

  img {
    width: 48px;
    border-radius: 30px;
    box-shadow: 2px 0 7px rgba(0, 0, 0, 0.1);
    margin-right: 12px;
  }

  .name {
    margin: 0;
  }
}

.tab-card-box {
  box-shadow: 0 2px 8px rgba(21, 21, 21, 0.1);
  border-radius: 16px;
  padding: 0;
  height: 100%;
  margin-bottom: 20px;

  .image {
    width: 100%;
    border-radius: 10px;
  }

  .title {
    color: var(--primary-color);
  }
}

@media screen and (max-width: 500px) {
  .list-tags {
    li {
      padding: 4px 12px;
      font-size: 12px;
    }
  }
}

.blog-author-card {
  .name {
    font-family: var(--opensans-font) !important;
  }
  small {
    font-family: var(--opensans-font) !important;
  }
}
.blog-details {
  .tab-card-box {
    box-shadow: unset;
  }
  .related-resources .list-tags li {
    font-family: var(--opensans-font) !important;
  }
}
.blog-outer-part {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-flow: row wrap;
  .left-side-outer {
    width: 148px;
    .left-side {
      width: 100%;
      padding-left: 0;
      padding-right: 0;
      h4 {
        color: #0b1347;
        font-family: var(--opensans-font) !important;
      }
      .know-your-para {
        font-family: var(--opensans-font) !important;
        font-weight: 500;
        line-height: 16.8px;
      }
      .know-your-btn {
        font-weight: 500;
        width: 100%;
      }
    }
  }

  .middle-part {
    width: calc(100% - 454px);
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
    .blog-main-content {
      p {
        font-family: var(--opensans-font) !important;
        color: #151515;
        a {
          color: #0070f5;
        }
        em {
          font-style: italic;
        }
      }
      ol li {
        list-style: decimal;
      }
      h3 {
        color: #151515;
      }
      .create-profile-sec {
        h3 {
          color: #fff;
        }
      }
      h4 {
        color: #2c2c2c;
        font-family: var(--archivo-font), sans-serif;
        font-size: 22px;
        font-weight: 500;
        line-height: 26.4px;
        margin-bottom: 30px;
        a {
          color: #0070f5;
          text-decoration: underline;
        }
      }
      h5 {
        font-size: 26px;
        line-height: 31.2px;
        font-weight: 700;
        color: #151515;
      }
      h6 {
        color: #151515;
        font-family: var(--archivo-font), sans-serif;
        font-size: 22px;
        font-weight: 500;
        line-height: 26.4px;
        margin-bottom: 10px;
      }
      ul {
        margin-bottom: 15px;
        padding-left: 10px;
        li {
          font-family: var(--opensans-font);
          color: #151515;
          font-size: 18px;
          line-height: 28px;
          font-weight: 400;
          list-style: disc;
          list-style-position: inside;
          p {
            display: inline;
          }
        }
        &.article-ul {
          margin-bottom: 0;
          margin-left: 6px;
        }
      }
      .expand-job {
        ul {
          li {
            list-style: none;
          }
        }
      }
      .article-box {
        margin-bottom: 15px;
        border-radius: 8px;
        p {
          font-family: var(--archivo-font), sans-serif !important;
          margin-bottom: 15px;
        }

        .article-ul li {
          font-family: var(--archivo-font), sans-serif !important;
          font-weight: 700;
          list-style: disc;
          margin-top: 0;
          color: #0070f5;
          a {
            color: #0070f5;
          }

          &:last-child {
            margin-bottom: 0;
          }
          color: #0070f5;
          cursor: pointer;
        }
      }
      .table-of-contents .article-ul {
        margin-left: 6px;
        ul {
          margin-left: 16px !important;
        }
      }
      .expand-job {
        h5 {
          color: #2c2c2c;
          font-size: 22px;
          line-height: 26.4px;
          font-weight: 700;
        }
        ul {
          margin-bottom: 0;
          li {
            a {
              font-family: var(--opensans-font) !important;
              .fa-solid {
                color: #0070f5;
              }
            }
          }
        }
      }
    }
    .create-profile-sec {
      margin-bottom: 25px;
      p {
        font-family: var(--opensans-font) !important;
        color: #fff;
        margin-bottom: 0;
      }

      p.h3 {
        font-family: var(--archivo-font), sans-serif !important;
      }
      .cps-btn {
        border-radius: 16px;
      }
    }
    .apply-ready-block {
      min-height: 208px;
      p.h3 {
        font-family: var(--opensans-font) !important;
        font-weight: 600 !important;
      }
      .btn-a {
        background: #0070f5 !important;
        border: 0 !important;
        width: auto;
        &:hover {
          background: var(--background-color_5) !important;
        }
      }
    }
    .blog-share-sec {
      .icon-soc {
        img {
          padding: 0;
          width: 20px;
          height: auto;
          vertical-align: top;
        }
        button {
          margin-right: 12px;
          height: 20px;
        }
        .copy-clipbpard {
          img {
            width: 24px !important;
          }
        }
      }
    }
  }
  .third-box {
    width: 306px;
    padding-left: 0;
    padding-right: 0;
    .blog-overlay {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      a {
        margin-top: auto;
        border: 0;
        word-wrap: break-word;
        white-space: normal;
        word-break: break-all;
      }
      button {
        margin-top: auto;
        border: 0;
        word-wrap: break-word;
        white-space: normal;
        word-break: break-all;
      }
      .sidebar-title {
        font-weight: 700 !important;
        font-family: var(--archivo-font), sans-serif !important;
      }
      .blog_looking_to_hire_head {
        font-size: 26px !important;
        line-height: 31.2px !important;
      }
      .blog_looking_to_hire_sub_head {
        font-family: var(--opensans-font) !important;
      }
      p {
        word-wrap: break-word;
        white-space: normal;
        // word-break: break-all;
        font-family: var(--opensans-font);
      }
    }
    .blog-profile-2.fixed-section {
      border-radius: 16px;
      overflow: hidden;
    }
  }
}

@media screen and (max-width: 1499px) {
  .apply-ready-block {
    h3,
    h4 {
      max-width: 46%;
    }
  }
}

@media screen and (max-width: 1279px) {
  .blog-title h1 {
    font-size: 2.5rem;
  }
  .blog-outer-part {
    .left-side-outer {
      width: 13%;
    }
    .middle-part {
      width: 65%;
      .blog-main-content {
        ul li {
          font-size: 14px;
          line-height: 24px;
        }
        .expand-job {
          h5 {
            font-size: 1.25rem;
          }
          ul {
            gap: 7px;
            margin-bottom: 0;
            li a {
              font-size: 14px;
              padding: 7px 12px;
              line-height: 24px;
            }
          }
        }
        h3 {
          font-size: 1.75rem;
          line-height: 1.2;
        }
        .create-profile-sec {
          h3 {
            font-size: 1.5rem;
          }
        }
        h6 {
          font-size: 1rem;
          line-height: 1.2;
        }
        h5 {
          font-size: 1.25rem;
          line-height: 1.2;
        }
        h4 {
          font-size: 0.9rem;
          line-height: 1.2;
        }
      }
    }
    .third-box {
      width: 22%;
      .blog-overlay {
        .sidebar-title,
        .blog_looking_to_hire_head {
          font-size: 1.25rem !important;
          line-height: 1.2 !important;
        }
        button {
          font-size: 14px;
          padding: 5px 15px;
        }
      }
    }
  }
  .apply-ready-block {
    h3 {
      font-size: 1rem;
      max-width: 44%;
    }
    h4 {
      font-size: 1.25rem;
      max-width: 44%;
    }
  }
}

@media screen and (max-width: 1399px) {
  .blog-title h1 {
    font-size: 2.5rem;
  }
  .blog-outer-part {
    .left-side-outer {
      width: 13%;
    }
    .middle-part {
      width: 65%;
      .blog-main-content {
        ul li {
          font-size: 14px;
          line-height: 24px;
        }
        .expand-job {
          h5 {
            font-size: 1.25rem;
          }
          ul {
            gap: 7px;
            margin-bottom: 0;
            li a {
              font-size: 14px;
              padding: 7px 12px;
              line-height: 24px;
            }
          }
        }
        h3 {
          font-size: 1.75rem;
          line-height: 1.2;
        }
        .create-profile-sec {
          h3 {
            font-size: 1.5rem;
          }
        }
        h6 {
          font-size: 1rem;
          line-height: 1.2;
        }
        h5 {
          font-size: 1.25rem;
          line-height: 1.2;
        }
        h4 {
          font-size: 0.9rem;
          line-height: 1.2;
        }
      }
    }
    .third-box {
      width: 22%;
      .blog-overlay {
        .sidebar-title,
        .blog_looking_to_hire_head {
          font-size: 1.25rem !important;
          line-height: 1.2 !important;
        }
        button {
          font-size: 14px;
          padding: 5px 15px;
        }
      }
    }
  }
  .apply-ready-block {
    h3 {
      font-size: 1rem;
      max-width: 44%;
    }
    h4 {
      font-size: 1.25rem;
      max-width: 44%;
    }
  }
}

@media screen and (max-width: 1199px) {
  .blog-title h1 {
    font-size: 2rem;
  }
  .blog-outer-part {
    .third-box .blog-overlay button {
      font-size: 12px;
      padding: 5px 10px;
    }
    .middle-part .apply-ready-block {
      min-height: 160px;
    }
  }
  .apply-ready-block {
    h4 {
      font-size: 1rem;
      line-height: 1.4;
    }
    h3 {
      font-size: 0.9rem;
      line-height: 1;
    }
  }
}

@media screen and (max-width: 1024px) {
  .blog-outer-part {
    flex-flow: row wrap;
    .left-side-outer {
      width: 100%;
      order: 3;
      display: flex;
      flex-flow: row wrap;
      justify-content: flex-start;
      align-items: stretch;
      margin-left: 1.33%;
      .left-side {
        max-width: 32%;
        margin-right: 1.33%;
        display: flex;
        flex-direction: column;
        align-items: center;
        .know-your-btn {
          margin-top: auto;
        }
        .know-your-para {
          text-align: center;
        }
      }
    }
    .middle-part {
      width: 100%;
      order: 1;
      p {
        font-size: 16px;
        line-height: 25.6px;
        margin-bottom: 25px;
      }
      .expand-job ul li a {
        font-size: 16px;
        line-height: 25px;
      }
    }
    .third-box {
      width: 100%;
      order: 2;
      display: flex;
      flex-flow: row wrap;
      justify-content: flex-start;
      align-items: stretch;
      margin-left: 1.33%;
      .third-box-item {
        max-width: 32%;
        margin-right: 1.33%;
      }
      .blog-overlay {
        .blog_looking_to_hire_head {
          font-size: 23px !important;
          line-height: 27.6px !important;
          text-align: center;
        }
        .blog_looking_to_hire_sub_head {
          font-size: 16px;
          text-align: center;
        }
        button {
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }
  .blog-main-outer {
    position: relative;
    display: flex;
    flex-direction: column;
    .blog-top {
      order: 1;
    }
    .blog-part {
      order: 2;
    }
    .blog-details {
      order: 3;
      .blog-cards {
        .f-45 {
          font-size: 40px !important;
          line-height: 48px !important;
        }
      }
      .tab-card-box {
        display: flex;
        flex-direction: column;
      }
      .related-resources {
        align-items: stretch;
        margin-top: 45px;
      }
      .tab-card-box.related-resource-block h4 {
        font-size: 19px;
        font-weight: 700;
        line-height: 22.6px;
        margin-bottom: 30px;
      }
      .related-resources .list-tags {
        margin: 20px 0 13px;
        li {
          font-size: 11px;
          line-height: 15.4px;
        }
      }
      .blog-author-card {
        margin-top: auto;
        img {
          width: 40px;
          border-radius: 200px;
        }
        .name {
          font-size: 16px;
          color: #2e3135;
          line-height: 25.6px;
        }
        small {
          font-size: 13px;
          line-height: 18.2px;
        }
      }
    }
  }
  .blog-part {
    padding: 20px 0 16px;
  }
}
@media screen and (min-width: 1024px) {
  .sticky-section {
    position: -webkit-sticky;
    position: sticky;
    top: 0px;
  }
}

@media screen and (max-width: 991px) {
  .blog-main-outer .blog-details .related-resources {
    .col-md-4 {
      width: 50%;
      margin-bottom: 25px;
    }
  }
  .blog-part {
    padding: 20px 0 16px;
  }
  .blog-title h1 {
    font-size: 31px;
    line-height: 37.2px;
  }
  .blog-top .breadcrumb {
    margin-bottom: 15px;
  }
  .blog-author-card .name {
    font-size: 18px;
    line-height: 28.8px;
  }
  .blog-outer-part .middle-part .blog-main-content .article-box .article-ul li {
    font-size: 16px;
    line-height: 25.6px;
  }
  .article-box {
    margin-bottom: 50px;
  }
  .blog-outer-part .middle-part {
    .blog-main-content {
      h2 {
        font-size: 40px;
        line-height: 48px;
      }
      h3 {
        font-size: 33px;
        line-height: 39.6px;
      }
      h4 {
        font-size: 19px;
        line-height: 22.8px;
        margin-bottom: 16px;
      }
      h5 {
        font-size: 23px;
        line-height: 27.6px;
      }
      h6 {
        font-size: 19px;
        line-height: 22.8px;
      }
      .expand-job ul {
        gap: 12px;
      }
      ul li {
        font-size: 16px;
        font-weight: 400;
        line-height: 25.6px;
      }
      div:nth-child(3) div div:nth-child(1) {
        margin-bottom: 50px;
      }
    }
  }

  .blog-outer-part .middle-part .blog-main-content .expand-job h5 {
    font-size: 19px;
    line-height: 22.8px;
  }
  .apply-ready-block .btn-a {
    max-width: 153px;
  }
}

@media screen and (max-width: 767px) {
  .blog-outer-part .middle-part {
    padding-left: 0px;
    padding-right: 0px;
  }
  .blog-outer-part .middle-part .blog-main-content .article-box {
    margin-bottom: 50px;
  }
  .create-profile-sec {
    flex-direction: column;
    align-items: flex-start;
    min-height: 233px;
    padding: 30px 18px;
    h3 {
      font-size: 24px;
      font-weight: 700;
      line-height: 33px;
    }
  }
  .blog-outer-part .middle-part .create-profile-sec .cps-btn {
    font-size: 16px;
    line-height: 19.2px;
    padding: 12px 24px;
  }
  .blog-outer-part .third-box {
    margin-left: 0;
    .third-box-item {
      max-width: 100%;
      margin-right: 0;
      width: 100%;
    }
    .blog-overlay button {
      line-height: 19.2px;
    }
  }
  .blog-outer-part .left-side-outer {
    margin-left: 0;
    .left-side {
      max-width: 100%;
      margin-right: 0;
    }
  }
  .blog-main-outer .blog-details .related-resources .col-md-4 {
    width: 100%;
  }
  .blog-cards .justify-content-end {
    justify-content: flex-start !important;
  }
  .blog-main-outer .blog-details .blog-cards .f-45 {
    margin-bottom: 20px;
  }
  .apply-ready-block h3,
  .apply-ready-block h4 {
    max-width: 100%;
  }
}

.blog-outer-part .middle-part .blog-main-content img {
  max-width: 100%;
  height: auto;
  margin-bottom: 15px;
}

@media (min-width: 1200px) {
  .blog-outer-part.sticky {
    position: relative;
    .left-side-outer {
      position: fixed;
      top: 0;
      left: 5%;
      width: 11%;
      .left-side {
        width: 145px;
      }
    }
    .middle-part {
      width: 65%;
      left: 13%;
      position: relative;
    }
    .third-box {
      position: fixed;
      top: 0;
      right: 5%;
      width: 19%;
      .third-box-item {
        width: 245px;
      }
    }
  }
}

@media (min-width: 1399px) {
  .blog-outer-part.sticky {
    position: relative;
    .left-side-outer {
      position: fixed;
      top: 0;
      left: 5%;
      z-index: 9999;
      .left-side {
        width: 149px;
      }
    }
    .middle-part {
      left: 11.5%;
      position: relative;
    }
    .third-box {
      position: fixed;
      top: 0;
      right: 7.2%;
      .third-box-item {
        background-color: #fff;
        width: 306px;
      }
    }
  }
}

.mobile-banner {
  height: 100%;
  width: 100%;
}

@media (min-width: 767px) {
  .mobile-banner {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .blog-outer-part .middle-part .blog-main-content .create-profile-sec p.h3 {
    font-size: 24px;
    line-height: 33.6px;
    letter-spacing: 0.6px;
  }
  .apply-ready-block h3 {
    font-size: 20px;
    line-height: 24px;
    text-align: center !important;
  }
  .apply-ready-block h4 {
    font-size: 24px;
    line-height: 28.8px;
    text-align: center;
  }
  .apply-ready-block .btn-a {
    margin-left: auto;
    margin-right: auto;
  }
  .blog-outer-part .third-box .blog-overlay button {
    padding: 12px 10px;
  }
  .blog-outer-part .left-side-outer .left-side h4 {
    font-size: 16px;
    line-height: 28.8px;
  }
  .blog-outer-part .third-box {
    margin-bottom: 0;
  }
  .blog-outer-part .left-side-outer .left-side {
    padding-top: 0;
  }
  .blog-details {
    padding-top: 40px;
  }
  .blog-outer-part .left-side-outer .left-side .know-your-btn {
    font-weight: 500;
    width: auto;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 16.8px;
  }
  .blog-main-outer .blog-details .related-resources .list-tags li {
    padding: 8px 16px;
  }
  .blog-outer-part .middle-part .blog-main-content .expand-job ul li a {
    font-size: 16px;
    padding: 12px 12px;
    line-height: 25.6px;
  }
}

.blog-goes {
  img {
    height: auto;
  }
}
.blog-part {
  .tab-card-box {
    box-shadow: unset;
  }
}
