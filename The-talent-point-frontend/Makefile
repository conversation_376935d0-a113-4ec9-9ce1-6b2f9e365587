# Server configurations
SERVER = root@
SERVER_DEV = thetalentpoint@157.180.21.27
REMOTE_DIR = thetalentpoint
# Build directories
BUILD_DIR = .next
STATIC_DIR = public
# Build the Next.js application
build:
	@echo "Building Next.js application..."
	@cat .env.prod > .env.production
	@npm install --legacy-peer-deps
	@npm run build
	@echo "Build completed!"
# Build for development
build-dev:
	@echo "$(YELLOW)Building Next.js application for development...$(NC)"
	@cat .env.dev > .env.production
	@npm install --legacy-peer-deps
	@npm run build
	@echo "$(GREEN)Development build completed!$(NC)"
# Deploy to production server (for Caddy with Node.js)
deploy: build
	@echo "Deploying to production server..."
	@ssh $(SERVER) "mkdir -p $(REMOTE_DIR)"
	@rsync -avz $(BUILD_DIR)/ $(SERVER):$(REMOTE_DIR)/$(BUILD_DIR)/
	@rsync -avz $(STATIC_DIR)/ $(SERVER):$(REMOTE_DIR)/$(STATIC_DIR)/
	@rsync -avz package.json $(SERVER):$(REMOTE_DIR)/
	@rsync -avz next.config.js $(SERVER):$(REMOTE_DIR)/
	@rsync -avz .env.production $(SERVER):$(REMOTE_DIR)/
	@ssh $(SERVER) "cd $(REMOTE_DIR) && npm install --production --legacy-peer-deps"
	@echo "Production deployment completed! Set up Caddy to proxy to Next.js server."
# Deploy to development server
deploy-dev: build-dev
	@echo "$(YELLOW)Deploying to development server...$(NC)"
	@ssh $(SERVER_DEV) "mkdir -p $(REMOTE_DIR)"
	@echo "Stopping existing application..."
	@ssh $(SERVER_DEV) "cd $(REMOTE_DIR) && pm2 delete nextjs-app 2>/dev/null || true"
	@echo "Syncing files..."
	@rsync -avz --progress --delete $(BUILD_DIR)/ $(SERVER_DEV):$(REMOTE_DIR)/$(BUILD_DIR)/
	@rsync -avz --progress $(STATIC_DIR)/ $(SERVER_DEV):$(REMOTE_DIR)/$(STATIC_DIR)/
	@rsync -avz --progress dist/ $(SERVER_DEV):$(REMOTE_DIR)/dist/
	@rsync -avz package.json package-lock.json next.config.js $(SERVER_DEV):$(REMOTE_DIR)/
	@rsync -avz .env.production $(SERVER_DEV):$(REMOTE_DIR)/.env.local
	@echo "Installing dependencies..."
	@ssh $(SERVER_DEV) "cd $(REMOTE_DIR) && npm ci --omit=dev --legacy-peer-deps"
	@echo "Starting application..."
	@ssh $(SERVER_DEV) "cd $(REMOTE_DIR) && pm2 start npm --name 'nextjs-app' -- start"
	@ssh $(SERVER_DEV) "pm2 save"
	@echo "$(GREEN)Development deployment completed!$(NC)"
	@make status-dev
# Start development server remotely
start-dev:
	@echo "Starting development server..."
	@ssh $(SERVER_DEV) "cd $(REMOTE_DIR) && pm2 start npm --name 'nextjs-app' -- start"
# Stop development server remotely
stop-dev:
	@echo "Stopping development server..."
	@ssh $(SERVER_DEV) "pm2 stop nextjs-app"
# Restart development server remotely
restart-dev:
	@echo "Restarting development server..."
	@ssh $(SERVER_DEV) "pm2 restart nextjs-app"
# Check development server status
status-dev:
	@echo "Checking development server status..."
	@ssh $(SERVER_DEV) "pm2 status nextjs-app"
# Show development server logs
logs-dev:
	@echo "Showing development server logs..."
	@ssh $(SERVER_DEV) "pm2 logs nextjs-app --lines 50"
# Clean build artifacts
clean:
	@echo "Cleaning build directory..."
	@rm -rf $(BUILD_DIR)
	@rm -rf node_modules/.cache
	@echo "Build artifacts cleaned!"
# Deep clean (removes node_modules)
deep-clean: clean
	@echo "Deep cleaning project..."
	@rm -rf node_modules
	@echo "Project deep cleaned!"
# Format code using Prettier
format:
	@echo "Formatting JavaScript, TypeScript, and TSX files..."
	@yarn prettier --write "pages/**/*.{js,jsx,ts,tsx}" "components/**/*.{js,jsx,ts,tsx}"
# Run development server locally
run-dev:
	@echo "Starting development server..."
	@yarn install
	@yarn dev
# Start the Next.js server in production mode locally
start:
	@echo "Starting Next.js server in production mode..."
	@yarn start
.PHONY: build build-dev deploy deploy-dev start-dev stop-dev restart-dev status-dev logs-dev clean deep-clean format run-dev start
