import React, {useState, useEffect} from 'react'; 
import { useRouter } from "next/router";
import StaffArchivedNamePosition from '../../../../components/Staff/Messages/ArchivedNamePosition'
export default function ArchivedNamePosition({id}:any) {
    return (
        <>
            <StaffArchivedNamePosition userId={id}/>
        </>
    )

}
export async function getServerSideProps({ params }:any) {
    return {
        props: { id: params.id }
    }
}