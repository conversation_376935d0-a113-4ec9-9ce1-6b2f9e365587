import React from 'react';
import Head from 'next/head';
import axios, { CancelTokenSource } from 'axios';

import { Job } from '@/lib/types';
import FrontendSingleJobs from '../../components/Frontend/SingleJobs';
import JsonLd from '@/components/JsonLd';
import { NextSeo } from 'next-seo';

interface SingleJobProps {
  job: Job;
  job_id: string;
  jobPosting: boolean
}

export default function SingleJob({ job, job_id }: SingleJobProps) {
  return (
    <>
      <Head>
        <title>{job.meta_tag ? job.meta_tag : 'Talent Point'}</title>
        <meta name="description" content={job.meta_desc ? job.meta_desc : ''} />
      </Head>
      <NextSeo noindex nofollow />
      {job && (
        <FrontendSingleJobs job={job} jobId={job_id} jobPosting={true} />
      )}

    </>
  );
}

export async function getServerSideProps({ params, CancelTokenSource }: any | CancelTokenSource) {
  try {
    if (params.params && params.params.length == 2) {
      const jobID = params.params[1].split('-').reverse()[0];
      const response = await axios.get(`/jobs/${jobID}`, {
        cancelToken: CancelTokenSource?.token,
      });
      return { props: { job: response.data, job_id: jobID } };
    }

    console.warn('Cannot find jobs with params', params)
    return {
      redirect: {
        destination: '/',
        permanent: false,
      }
    }
  } catch (err) {
    console.error(err)
    return {
      redirect: {
        destination: '/',
        permanent: false,
      }
    }
  }
}
