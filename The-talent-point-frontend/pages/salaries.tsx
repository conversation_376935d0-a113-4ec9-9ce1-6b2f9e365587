import FilterSearch from '@/components/Frontend/JobSalaries/FilterSearch';
import React from 'react';
import Head from 'next/head';
import {
   getCountries,
   getSectorsList
} from "@/lib/ApiAdapter";
import { Country, Sector } from '../lib/types';
import LocationCareer from '@/components/Frontend/JobSalaries/locationCareer';
interface countryIndustryProps {
   countries: Country
   industries: Sector
}
const currentYear = new Date().getFullYear();

export default function Salaries({ countries, industries }: countryIndustryProps) {
   return (
      <>
         <Head>
            <title>{`Average Salary Insights in Middle East - ${currentYear}`}</title>
            <meta
               content={`Explore the average salary for jobs in Middle east by Job Titles & Location in ${currentYear}  Discover the average compensation by job titles & locations across GCC & Find out what your skills are worth in today's job market`}
               name="description"
            />
         </Head>
         <div>
            <FilterSearch
               countries={countries}
               industries={industries}
            />
            <LocationCareer
               countries={countries}
               industries={industries}
            />
         </div>
      </>
   )
}
export async function getServerSideProps() {
   const response1 = await getCountries();
   const response2 = await getSectorsList();
   return {
      props: {
         countries: response1,
         industries: response2,
      },
   };
}
