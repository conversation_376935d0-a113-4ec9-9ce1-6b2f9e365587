import React from "react";
import Head from "next/head";
import { getAllNewAdminSettings } from "@/lib/adminapi";
import { Job, Settings } from "@/lib/types";
import FrontendJobsInSkill from "../../components/Frontend/jobsInSkill";
import { getSingleCityByName } from "@/lib/frontendapi";
import { searchJobs } from "@/lib/ApiAdapter";
import { getCurrentUserData } from "@/lib/session";
interface JobSearchPageProps {
    skillJobInCityName: '';
    jobs: Job[];
    settings: Settings;
    finalSkillName: '';
    finalCityName: '';
    cityCountryName: '';
}
export default function JobsInSkill({ skillJobInCityName, jobs, settings, finalSkillName, finalCityName, cityCountryName }: JobSearchPageProps) {
    const date = new Date();
    var months = ['Jan', 'Feb', 'March', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return (
        <>
            <Head>
                <title>
                    {finalSkillName} Jobs in {finalCityName} - {jobs.length} vacancies in {months[date.getMonth()]} {date.getFullYear()}
                </title>
                <meta
                    name="description"
                    content="Search from Over {jobs.length} vacancies in {finalCityName}. Register Today & apply for {finalSkillName} Jobs in {finalCityName}  on thetalentpoint.com"
                />
            </Head>
            {/* <FrontendJobsInSkill   skillJobInCityName={skillJobInCityName} cityCountryName={cityCountryName} ssrJobs={jobs} /> */}
        </>
    );
}
export async function getServerSideProps({ params }: any) {
    console.log(params, "params")
    return {
        redirect: {
            destination: '/' + params.skillJobsInCity,
            permanent: false, // Set this to true if the redirection is permanent
        },
    };
    // const response = await getAllNewAdminSettings();
    // const skillJobInCityName = params.skillJobsInCity;
    // const skillName = skillJobInCityName.substring(0, skillJobInCityName.indexOf("-jobs-in-"));
    // const newSkillName = skillName.replace(/-/g, ' ');
    // const newSkillArr = newSkillName.split(" ");
    // for (let i = 0; i < newSkillArr.length; i++) {
    //     newSkillArr[i] = newSkillArr[i].charAt(0).toUpperCase() + newSkillArr[i].slice(1);
    // }
    // const finalSkillName = newSkillArr.join(" ");

    // const cityName = skillJobInCityName.substring(skillJobInCityName.indexOf("-jobs-in-") + 1);
    // const cityReplaceName = cityName.replace('jobs-in-','');
    // const newCityName = cityReplaceName.replace(/-/g, ' ');
    // const newCityArr = newCityName.split(" ");
    // for (let i = 0; i < newCityArr.length; i++) {
    //     newCityArr[i] = newCityArr[i].charAt(0).toUpperCase() + newCityArr[i].slice(1);
    // }
    // const finalCityName = newCityArr.join(" ");

    // const currentUserData: any = getCurrentUserData();
    // let skillData = '';
    // let cityData = '';
    // if(finalSkillName){
    //     skillData = finalSkillName;
    //     cityData = finalCityName;
    // } else {
    //     skillData = '';
    //     cityData = '';
    // }
    // const data = {
    //     location: '',
    //     keywords: '',
    //     user_id: currentUserData.id,
    //     city: cityData,
    //     skill: skillData,
    // };
    // const cityDatas = {
    //     city_name: cityData
    // }
    // const cityResponse = await getSingleCityByName(cityDatas);
    // const cityCountryName = cityResponse.country_name;
    // const jobsResponse = await searchJobs(data);
    // return {
    //     props: { skillJobInCityName: skillJobInCityName, jobs: jobsResponse.data, settings: response, finalSkillName: finalSkillName, finalCityName: finalCityName, cityCountryName: cityCountryName },
    // };
}
