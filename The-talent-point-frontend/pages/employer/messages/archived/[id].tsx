import React, {useState, useEffect} from 'react'; 
import { useRouter } from "next/router";
import EmployerArchivedNamePosition from '../../../../components/Employer/Messages/ArchivedNamePosition'
export default function ArchivedNamePosition({id}:any) {
    return (
        <>
            <EmployerArchivedNamePosition userId={id}/>
        </>
    )

}
export async function getServerSideProps({ params }:any) {
    return {
        props: { id: params.id }
    }
}