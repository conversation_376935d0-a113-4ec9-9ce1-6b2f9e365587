import React, { lazy, Suspense, useEffect, useState, useMemo } from 'react';
import Head from 'next/head';
import dynamic from 'next/dynamic';

const BlogList = dynamic(() => import('@/components/Frontend/BlogList'));
// const BlogList = lazy(() => import('@/components/Frontend/BlogList'));

import { getAllBlogs, getAllNewAdminSettings } from '@/lib/adminapi';
import cache from 'memory-cache'
import { Settings, Blogs } from '@/lib/types';
interface BlogListProps {
  settings: Settings;
  blogs: Blogs[],
  latestBlog: Blogs | null,
  page: number,
  totalCount: number
}

import { useRouter } from 'next/router';
import ErrorHandler from '@/lib/ErrorHandler';
import { GetServerSideProps } from 'next';
export default function BlogData({ settings, blogs, latestBlog, page, totalCount }: BlogListProps) {
  // const [mounted, setMounted] = useState(false);

  const router = useRouter();

  // useEffect(() => {
  //   setMounted(true);
  // }, []);

  const ogUrl = useMemo(() => new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href, [router.asPath]);

  return (
    <>
      <Head>
        <title>{settings?.blog_listing_meta_title ? settings?.blog_listing_meta_title : 'Talent Point'}</title>
        <meta
          name="description"
          content={settings.blog_listing_meta_description ? settings.blog_listing_meta_description : ''}
        />
        {/* Open Graph tags */}
        <meta property="og:title" content={settings.carrer_meta_title ? settings.carrer_meta_title : `Talent Point`} />
        <meta
          property="og:description"
          content={
            settings.carrer_meta_description
              ? settings.carrer_meta_description
              : `Get Expert Career  tips, guides, and to clarify your doubts, map out your future aspirations, and uncover exciting career opportunities.`
          }
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.webp`} />
        <meta property="og:url" content={ogUrl} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@theTalentPoint" />
        <meta name="twitter:title" content={settings.carrer_meta_title ? settings.carrer_meta_title : `Talent Point`} />
        <meta
          name="twitter:description"
          content={
            settings.carrer_meta_description
              ? settings.carrer_meta_description
              : `Get Expert Career  tips, guides, and to clarify your doubts, map out your future aspirations, and uncover exciting career opportunities.`
          }
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.webp`} />
      </Head>
      <Suspense fallback={<div>Loading...</div>}>
        <BlogList blogs={blogs} latestBlog={latestBlog} page={page} totalCount={totalCount} />
      </Suspense>
      {/* {mounted ? ( */}
      {/* <Suspense fallback={<div>Loading...</div>}>
        <BlogList blogs={blogs} latestBlog={latestBlog} page={page} totalCount={totalCount} />
      </Suspense> */}
      {/* // ) : (
      //   <div>Loading...</div>
      // )} */}
    </>
  );
}

const formatDate = (dateStr: string | number) => {
  const date = new Date(dateStr);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  return date.toLocaleDateString(undefined, options);
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const page = context?.query.page ? parseInt(context.query.page as string, 10) : 1;
  const cacheKeyBlogs = `blogs-page-${page}`;
  const cacheKeySettings = 'admin-settings';

  try {
    // Check cache for blogs and settings
    let blogsRes = cache.get(cacheKeyBlogs);
    let settingsRes = cache.get(cacheKeySettings);

    if (!blogsRes) {
      // Fetch and cache blogs if not cached
      const params = {
        pageSize: 10,
        page: page,
      };
      blogsRes = await getAllBlogs(params);
      cache.put(cacheKeyBlogs, blogsRes, 30 * 1000); // Cache for 30 seconds
    }

    if (!settingsRes) {
      // Fetch and cache settings if not cached
      settingsRes = await getAllNewAdminSettings();
      cache.put(cacheKeySettings, settingsRes, 30 * 1000); // Cache for 30 seconds
    }

    // Process blog data
    const validBlogs = blogsRes?.data?.data
      .filter((blog: Blogs) => blog?.created_at !== undefined)
      .map((blog: Blogs) => ({
        ...blog,
        created_at: blog?.created_at ? formatDate(blog.created_at) : formatDate(0),
      }));
    const sortedBlogs = validBlogs?.sort(
      (a: Blogs, b: Blogs) => new Date(b.created_at!).getTime() - new Date(a.created_at!).getTime()
    );
    let latestBlog = sortedBlogs?.length > 0 ? sortedBlogs[0] : null;
    let restBlogs = sortedBlogs?.slice(1);

    return {
      props: {
        blogs: restBlogs,
        latestBlog: latestBlog,
        settings: settingsRes,
        page: page,
        totalCount: blogsRes?.data?.total,
      },
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return {
      props: {
        blogs: [],
        latestBlog: null,
        settings: {},
        totalCount: null,
        page: null,
      },
    };
  }
};


