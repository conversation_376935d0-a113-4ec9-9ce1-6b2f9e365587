const SITE_DATA_URL = process.env.NEXT_PUBLIC_BASE_URL;
const BLOG_DATA_URL = process.env.NEXT_PUBLIC_API_URL + '/blog/get-all-blogs';
const AUTHORS_DATA_URL = process.env.NEXT_PUBLIC_API_URL + '/author';

function generateSiteMap(blog, authors) {
  // Generate current date in ISO format instead of using a hardcoded date
  const lastModifiedAt = new Date().toISOString();

  return `<?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
     <!--We manually set the two URLs we know already-->
     <url>
     <loc>${SITE_DATA_URL}</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>1.00</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}jobs-in-gulf</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}career-tips</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}about-us</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}blog</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}for-employers</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}jobs-by-location</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
     <url>
     <loc>${SITE_DATA_URL}author</loc>
     <lastmod>${lastModifiedAt}</lastmod>
     <priority>0.80</priority>
     </url>
      ${blog.data
        .map(({slug, created_at, updated_at}) => {
          return `
          <url>
          <loc>${`${SITE_DATA_URL}blog/${slug}`}</loc>
          <lastmod>${created_at != null ? created_at : updated_at}</lastmod>
          <priority>0.64</priority>
          </url>
          `;
        })
        .join('')}
      ${authors.data
        .map(({slug, created_at, updated_at}) => {
          return `
          <url>
          <loc>${`${SITE_DATA_URL}author/${slug}`}</loc>
          <lastmod>${created_at != null ? created_at : updated_at}</lastmod>
          <priority>0.64</priority>
          </url>
          `;
        })
        .join('')}
   </urlset>`;
}

function SiteMap() {
  // getServerSideProps will do the heavy lifting
}

export async function getServerSideProps({res}) {
  // We make an API call to gather the URLs for our site
  const blog_request = await fetch(BLOG_DATA_URL);
  const blog = await blog_request.json();

  const authors_request = await fetch(AUTHORS_DATA_URL);
  const authors = await authors_request.json();

  // We generate the XML sitemap with the posts data
  const sitemap = generateSiteMap(blog, authors);

  res.setHeader('Content-Type', 'text/xml');
  // we send the XML to the browser
  res.write(sitemap);

  res.end();

  return {
    props: {},
  };
}

export default SiteMap;
