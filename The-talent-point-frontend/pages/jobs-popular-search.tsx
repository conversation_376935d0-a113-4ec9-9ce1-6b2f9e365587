import React, {useState, useEffect} from 'react';
import {useRouter} from 'next/router';
import FrontendJobsPopularSearch from '../components/Frontend/jobsPopularSearch';
import Head from 'next/head';

export default function JobsPopularSearch() {
  const router = useRouter();
  return (
    <>
      <Head>
        <title>The Talent Point - Jobs by Popular Search in the Middle East</title>
        <meta
          name="description"
          content="Explore popular job searches in the Middle East. Find part-time jobs, accountant jobs, teaching jobs, and more. Discover opportunities to work in Dubai, Abu Dhabi, and other regions."
        />
        <meta
          name="keywords"
          content="The Talent Point, jobs, Middle East, popular job searches, part-time jobs, accountant jobs, teaching jobs, Dubai, Abu Dhabi"
        />

        {/* Open Graph tags */}
        <meta property="og:title" content="The Talent Point - Jobs by Popular Search in the Middle East" />
        <meta
          property="og:description"
          content="Explore popular job searches in the Middle East. Find part-time jobs, accountant jobs, teaching jobs, and more. Discover opportunities to work in Dubai, Abu Dhabi, and other regions."
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@TheTalentPoint" />
        <meta name="twitter:title" content="The Talent Point - Jobs by Popular Search in the Middle East" />
        <meta
          name="twitter:description"
          content="Explore popular job searches in the Middle East. Find part-time jobs, accountant jobs, teaching jobs, and more. Discover opportunities to work in Dubai, Abu Dhabi, and other regions."
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>

      <FrontendJobsPopularSearch />
    </>
  );
}
