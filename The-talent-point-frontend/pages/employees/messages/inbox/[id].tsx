import React, {useState, useEffect} from 'react'; 
import { useRouter } from "next/router";
import EmployeesInboxNamePosition from '../../../../components/Employees/Messages/InboxNamePosition'
export default function NamePosition({id}:any) {
    return (
        <>
            <EmployeesInboxNamePosition userId={id}/>
        </>
    )

}
export async function getServerSideProps({ params }:any) {
    return {
        props: { id: params.id }
    }
}