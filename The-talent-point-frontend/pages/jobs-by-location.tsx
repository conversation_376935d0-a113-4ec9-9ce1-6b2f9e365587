import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import FrontendJobsLocation from '../components/Frontend/JobsLocation';
import Head from 'next/head';
import { getCountriesCitiesLocations } from '@/lib/frontendapi';

export default function JobsLocation({ cityLocation, otherLocation }: { cityLocation: any, otherLocation: any }) {
  const router = useRouter();
  return (
    <>
      <Head>
        <title>The Talent Point - Jobs by Location</title>
        <meta
          name="description"
          content="Explore job opportunities in different locations. Find jobs in the United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, and more. Discover career options in cities like Dubai, Riyadh, Doha, and others."
        />
        <meta
          name="keywords"
          content="The Talent Point, jobs by location, United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, job opportunities, Dubai, Riyadh, Doha"
        />

        {/* Open Graph tags */}
        <meta property="og:title" content="The Talent Point - Jobs by Location" />
        <meta
          property="og:description"
          content="Explore job opportunities in different locations. Find jobs in the United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, and more. Discover career options in cities like Dubai, Riyadh, Doha, and others."
        />
        <meta property="og:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
        <meta property="og:url" content={new URL(router.asPath, process.env.NEXT_PUBLIC_BASE_URL).href} />
        <meta property="og:type" content="website" />

        {/* Twitter card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@TheTalentPoint" />
        <meta name="twitter:title" content="The Talent Point - Jobs by Location" />
        <meta
          name="twitter:description"
          content="Explore job opportunities in different locations. Find jobs in the United Arab Emirates, Saudi Arabia, Qatar, Oman, Kuwait, and more. Discover career options in cities like Dubai, Riyadh, Doha, and others."
        />
        <meta property="twitter:image" content={`${process.env.NEXT_PUBLIC_BASE_URL}images/Avatars-4.png`} />
      </Head>

      <FrontendJobsLocation cityLocation={cityLocation} otherLocation={otherLocation} />
    </>
  );
}
export const getServerSideProps = async () => {
  try {
    const response = await getCountriesCitiesLocations();
    return {
      props: {
        cityLocation: response?.data,
        otherLocation: response?.other_location_data
      }
    }

  } catch (error) {
    console.log(error)
    return {
      props: {
        cityLocation: [],
        otherLocation: [],
      },
    };

  }
}
